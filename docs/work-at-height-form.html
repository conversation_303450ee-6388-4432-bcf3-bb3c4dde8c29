<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work at Height Permit Form</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
            padding: 20px;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Header styles */
        .header {
            background-color: #fef3e2;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            color: #9a3412;
            margin-bottom: 4px;
        }
        
        .serial-number {
            font-size: 12px;
            color: #6b7280;
            font-weight: 300;
            margin-left: 16px;
        }
        
        .validity-notice {
            font-size: 12px;
            color: #dc2626;
            font-weight: 500;
        }
        
        /* Form section styles */
        .form-section {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 12px;
        }
        
        .section-description {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 12px;
        }
        
        /* Grid layouts */
        .grid-1 { display: grid; grid-template-columns: 1fr; gap: 12px; }
        .grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; }
        .grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; }
        .grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px; }
        .grid-6 { display: grid; grid-template-columns: repeat(6, 1fr); gap: 4px; }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4, .grid-6 {
                grid-template-columns: 1fr;
            }
        }
        
        /* Form field styles */
        .field-group {
            margin-bottom: 12px;
        }
        
        .field-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .required {
            color: #ef4444;
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            padding: 8px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
            resize: vertical;
            min-height: 60px;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .form-checkbox {
            width: 16px;
            height: 16px;
            color: #10b981;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .checkbox-label {
            font-size: 12px;
            color: #374151;
            line-height: 1.2;
        }
        
        /* Table styles */
        .table-container {
            overflow-x: auto;
            margin-bottom: 16px;
        }
        
        .form-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #d1d5db;
            font-size: 12px;
        }
        
        .table-header {
            background-color: #f9fafb;
        }
        
        .table-header th {
            padding: 8px;
            text-align: left;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-right: 1px solid #d1d5db;
            border-bottom: 1px solid #d1d5db;
        }
        
        .table-cell {
            padding: 8px;
            border-right: 1px solid #d1d5db;
            border-bottom: 1px solid #d1d5db;
        }
        
        .table-input {
            width: 100%;
            padding: 4px;
            font-size: 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }
        
        .signature-pad {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            background-color: #f9fafb;
            color: #6b7280;
        }
        
        /* Button styles */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #ea580c;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #c2410c;
        }
        
        .btn-secondary {
            background-color: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background-color: #f9fafb;
        }
        
        .btn-add {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
            font-size: 12px;
            padding: 4px 12px;
            margin-top: 8px;
        }
        
        .btn-add:hover {
            background-color: #bbf7d0;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            margin-top: 24px;
        }
        
        /* Disabled styles */
        .disabled-section {
            background-color: #f3f4f6;
            opacity: 0.6;
        }
        
        .disabled-input {
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div style="display: flex; align-items: center; justify-content: center; gap: 16px; margin-bottom: 4px;">
                <h1>WORK AT HEIGHT PERMIT</h1>
                <span class="serial-number">Serial No: <span id="serialNumber">123456</span></span>
            </div>
            <div class="validity-notice">
                This permit is valid for ONE day only.
            </div>
        </div>

        <!-- Details Section -->
        <div class="form-section">
            <h3 class="section-title">Details</h3>
            <div class="grid-2">
                <div class="field-group">
                    <label class="field-label">PTW Ref.No <span class="required">*</span></label>
                    <input type="text" class="form-input" required>
                </div>
                <div class="field-group">
                    <label class="field-label">Starting from <span class="required">*</span></label>
                    <input type="datetime-local" class="form-input" required>
                </div>
                <div class="field-group">
                    <label class="field-label">Ending at <span class="required">*</span></label>
                    <input type="datetime-local" class="form-input" required>
                </div>
            </div>
        </div>

        <!-- Other permits in use Section -->
        <div class="form-section">
            <h3 class="section-title">Other permits in use</h3>
            <div class="grid-4">
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">GWP</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">HWP</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Electrical</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">CSE</label>
                </div>
            </div>
        </div>

        <!-- Mode of access Section -->
        <div class="form-section">
            <h3 class="section-title">Mode of access to be used</h3>
            <div class="grid-3">
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Scaffolding</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Ladder(for tasks not exceeding 30 minutes)</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Aerial lifts</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Staircase</label>
                </div>
                <div class="field-group">
                    <label class="field-label">Other(specify)</label>
                    <input type="text" class="form-input">
                </div>
            </div>
        </div>

        <!-- Description of work Section -->
        <div class="form-section">
            <h3 class="section-title">Description of work</h3>
            <div class="grid-1">
                <div class="field-group">
                    <label class="field-label">Description of work <span class="required">*</span></label>
                    <textarea class="form-textarea" required></textarea>
                </div>
                <div class="field-group">
                    <label class="field-label">Location <span class="required">*</span></label>
                    <input type="text" class="form-input" required>
                </div>
            </div>
        </div>

        <!-- Hazards Section -->
        <div class="form-section">
            <h3 class="section-title">Hazards</h3>
            <div class="field-group">
                <label class="field-label">Hazards <span class="required">*</span></label>
                <textarea class="form-textarea" required></textarea>
            </div>
        </div>

        <!-- Height Hazards Section -->
        <div class="form-section">
            <h3 class="section-title">Height Hazards</h3>
            <div class="grid-6">
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Falling objects</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Unstable surfaces</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Weather conditions</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Inadequate lighting</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Electrical hazards</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Moving machinery</label>
                </div>
            </div>
        </div>

        <!-- Fall Protection Section -->
        <div class="form-section">
            <h3 class="section-title">Fall Protection</h3>
            <div class="grid-4">
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Safety harness</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Safety nets</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Guard rails</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Safety lines</label>
                </div>
            </div>
        </div>

        <!-- Precautions Required Section -->
        <div class="form-section">
            <h3 class="section-title">Precautions Required</h3>
            <div class="table-container">
                <table class="form-table">
                    <thead class="table-header">
                        <tr>
                            <th>Precaution Required</th>
                            <th style="width: 80px; text-align: center;">Required</th>
                            <th style="width: 100px; text-align: center;">Initial (e.g V.K)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="table-cell">Proper PPE (helmet, safety shoes, etc.)</td>
                            <td class="table-cell" style="text-align: center;">
                                <input type="checkbox" class="form-checkbox">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input" placeholder="Initials">
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">Safety harness and lanyard</td>
                            <td class="table-cell" style="text-align: center;">
                                <input type="checkbox" class="form-checkbox">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input" placeholder="Initials">
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">Secure anchor points</td>
                            <td class="table-cell" style="text-align: center;">
                                <input type="checkbox" class="form-checkbox">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input" placeholder="Initials">
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">Weather monitoring</td>
                            <td class="table-cell" style="text-align: center;">
                                <input type="checkbox" class="form-checkbox">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input" placeholder="Initials">
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">Emergency rescue plan</td>
                            <td class="table-cell" style="text-align: center;">
                                <input type="checkbox" class="form-checkbox">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input" placeholder="Initials">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Inspections Section -->
        <div class="form-section">
            <h3 class="section-title">Inspections</h3>
            <p class="section-description">The following areas/items have been inspected by the issuer and the receiver.</p>

            <div class="grid-6">
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Danger/warning signs</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Buddy system</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Scaffold tag system</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Rescue</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Man basket</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Safety barriers</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Lighting</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Ground stability</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Competence of the operatives</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" class="form-checkbox">
                    <label class="checkbox-label">Weather</label>
                </div>
            </div>

            <!-- Inspector Authorization -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 12px; margin-top: 16px;">
                <h4 style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 8px;">Inspector Authorization</h4>
                <div class="grid-2">
                    <div class="field-group">
                        <label class="field-label">Inspector Name <span class="required">*</span></label>
                        <input type="text" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Inspector Signature <span class="required">*</span></label>
                        <div class="signature-pad">Signature Pad</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permit Issue Section -->
        <div class="form-section">
            <h3 class="section-title">Permit Issue</h3>
            <p class="section-description">I have examined the work site and satisfied myself that the work can be done safely.</p>

            <div class="grid-2">
                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px;">
                    <h4 style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 8px;">Issuer</h4>
                    <div class="field-group">
                        <label class="field-label">Name <span class="required">*</span></label>
                        <input type="text" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Designation <span class="required">*</span></label>
                        <input type="text" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Signature <span class="required">*</span></label>
                        <div class="signature-pad">Signature Pad</div>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Date <span class="required">*</span></label>
                        <input type="date" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Time <span class="required">*</span></label>
                        <input type="time" class="form-input" required>
                    </div>
                </div>

                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px;">
                    <h4 style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 8px;">Receiver</h4>
                    <div class="field-group">
                        <label class="field-label">Name <span class="required">*</span></label>
                        <input type="text" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Designation <span class="required">*</span></label>
                        <input type="text" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Signature <span class="required">*</span></label>
                        <div class="signature-pad">Signature Pad</div>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Date <span class="required">*</span></label>
                        <input type="date" class="form-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Time <span class="required">*</span></label>
                        <input type="time" class="form-input" required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permit Return Section -->
        <div class="form-section disabled-section">
            <h3 class="section-title">Permit Return</h3>
            <p class="section-description">I have examined the work site and satisfied myself that it has been left in a safe condition.</p>

            <div class="grid-2">
                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px; background-color: #f9fafb;">
                    <h4 style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 8px;">Issuer</h4>
                    <div class="field-group">
                        <label class="field-label">Name</label>
                        <input type="text" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Designation</label>
                        <input type="text" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Signature</label>
                        <div class="signature-pad" style="background-color: #f3f4f6; color: #9ca3af;">Signature Pad</div>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Date</label>
                        <input type="date" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Time</label>
                        <input type="time" class="form-input disabled-input" disabled>
                    </div>
                </div>

                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px; background-color: #f9fafb;">
                    <h4 style="font-size: 12px; font-weight: 500; color: #1f2937; margin-bottom: 8px;">Receiver</h4>
                    <div class="field-group">
                        <label class="field-label">Name</label>
                        <input type="text" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Designation</label>
                        <input type="text" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Signature</label>
                        <div class="signature-pad" style="background-color: #f3f4f6; color: #9ca3af;">Signature Pad</div>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Date</label>
                        <input type="date" class="form-input disabled-input" disabled>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Time</label>
                        <input type="time" class="form-input disabled-input" disabled>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sign off Section -->
        <div class="form-section">
            <h3 class="section-title">Sign off</h3>
            <p class="section-description">By signing this work at height permit, I accept to abide by the instituted control measures that will enhance safe working at height</p>

            <!-- Additional fields -->
            <div class="grid-2" style="margin-bottom: 16px;">
                <div class="field-group">
                    <label class="field-label">Safety supervisor <span class="required">*</span></label>
                    <input type="text" class="form-input" required>
                </div>
                <div class="field-group">
                    <label class="field-label">Supervisor Signature <span class="required">*</span></label>
                    <div class="signature-pad">Signature Pad</div>
                </div>
            </div>

            <!-- Signoff Table -->
            <div class="table-container">
                <table class="form-table">
                    <thead class="table-header">
                        <tr>
                            <th>S/No</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Signature</th>
                            <th>Time</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="table-cell">1</td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <div style="width: 80px; height: 24px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #f9fafb; text-align: center; line-height: 24px; font-size: 12px; color: #6b7280;">
                                    Sign
                                </div>
                            </td>
                            <td class="table-cell">
                                <input type="time" class="table-input">
                            </td>
                            <td class="table-cell">
                                <button type="button" style="color: #dc2626; background: none; border: none; cursor: pointer; font-size: 14px;">×</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">2</td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <div style="width: 80px; height: 24px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #f9fafb; text-align: center; line-height: 24px; font-size: 12px; color: #6b7280;">
                                    Sign
                                </div>
                            </td>
                            <td class="table-cell">
                                <input type="time" class="table-input">
                            </td>
                            <td class="table-cell">
                                <button type="button" style="color: #dc2626; background: none; border: none; cursor: pointer; font-size: 14px;">×</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell">3</td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <input type="text" class="table-input">
                            </td>
                            <td class="table-cell">
                                <div style="width: 80px; height: 24px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #f9fafb; text-align: center; line-height: 24px; font-size: 12px; color: #6b7280;">
                                    Sign
                                </div>
                            </td>
                            <td class="table-cell">
                                <input type="time" class="table-input">
                            </td>
                            <td class="table-cell">
                                <button type="button" style="color: #dc2626; background: none; border: none; cursor: pointer; font-size: 14px;">×</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <button type="button" class="btn-add">Add Row</button>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <button type="button" class="btn btn-secondary">Cancel</button>
            <button type="submit" class="btn btn-primary">Submit Form</button>
        </div>
    </div>

    <script>
        // Generate random serial number
        document.getElementById('serialNumber').textContent = Math.floor(Math.random() * 1000000).toString();

        // Set current date and time for datetime fields
        const now = new Date();
        const currentDateTime = now.toISOString().slice(0, 16);
        const endDateTime = new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16);

        const startDateTimeInputs = document.querySelectorAll('input[type="datetime-local"]');
        if (startDateTimeInputs.length >= 2) {
            startDateTimeInputs[0].value = currentDateTime;
            startDateTimeInputs[1].value = endDateTime;
        }

        // Set current date for date fields
        const currentDate = now.toISOString().slice(0, 10);
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            if (!input.disabled) {
                input.value = currentDate;
            }
        });

        // Set current time for time fields
        const currentTime = now.toTimeString().slice(0, 5);
        const timeInputs = document.querySelectorAll('input[type="time"]');
        timeInputs.forEach(input => {
            if (!input.disabled) {
                input.value = currentTime;
            }
        });
    </script>
</body>
</html>
