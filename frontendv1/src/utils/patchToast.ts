import { toast, ToastContent, ToastOptions, Id } from 'react-toastify';
import { sanitizeToastMessage } from './toastUtils';

// Wrap a toast function to sanitize string messages to <= 6 words
function wrapToastFn<T extends (...args: any[]) => Id>(fn: T): T {
  return ((content: ToastContent, options?: ToastOptions) => {
    let nextContent = content as any;
    if (typeof content === 'string') {
      nextContent = sanitizeToastMessage(content, 6);
    }
    return fn(nextContent, options);
  }) as T;
}

export function patchToast() {
  // IMPORTANT: Do not reassign the imported 'toast' binding (it's a const ESM binding)
  // Only patch helper methods that we actually use across the app
  toast.success = wrapToastFn(toast.success.bind(toast));
  toast.error = wrapToastFn(toast.error.bind(toast));
  toast.info = wrapToastFn(toast.info.bind(toast));
  toast.warning = wrapToastFn(toast.warning.bind(toast));
  toast.warn = toast.warning; // alias consistency
  if ((toast as any).dark) {
    (toast as any).dark = wrapToastFn(((toast as any).dark as any).bind(toast));
  }
}
