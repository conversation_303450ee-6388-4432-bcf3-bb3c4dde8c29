// import { MockedResponse } from '@apollo/client/testing';
// // import jest from "jest"
// import {
//   GET_MY_NOTIFICATIONS,
//   GET_UNREAD_NOTIFICATION_COUNT,
//   GET_ALL_NOTIFICATION_PREFERENCES
// } from '../graphql/queries';
// import {
//   MARK_NOTIFICATION_AS_READ,
//   MARK_ALL_NOTIFICATIONS_AS_READ,
//   UPDATE_NOTIFICATION_PREFERENCES,
//   SEND_TEST_NOTIFICATION
// } from '../graphql/mutations';
// import { ON_NOTIFICATION } from '../graphql/subscriptions';
// import { AppNotification, NotificationPreference, NotificationPriority, NotificationStatus } from '../types/notifications';


// // Mock notification data generators
// export const createMockNotification = (overrides: Partial<AppNotification> = {}): AppNotification => ({
//   id: Math.floor(Math.random() * 1000),
//   type: 'training_expiring',
//   title: 'Test Notification',
//   message: 'This is a test notification',
//   priority: NotificationPriority.MEDIUM,
//   status: NotificationStatus.SENT,
//   createdAt: new Date().toISOString(),
//   userId: 1,
//   tenantId: 1,
//   createdBy: 'system',
//   deliveries: [],
//   ...overrides
// });

// export const createMockPreference = (overrides: Partial<NotificationPreference> = {}): NotificationPreference => ({
//   id: Math.floor(Math.random() * 1000),
//   notificationType: 'training_expiring',
//   inAppEnabled: true,
//   emailEnabled: true,
//   smsEnabled: false,
//   minimumPriority: NotificationPriority.LOW,
//   doNotDisturbEnabled: false,
//   userId: 1,
//   createdAt: new Date().toISOString(),
//   ...overrides
// });

// // Mock GraphQL responses
// export const createNotificationMocks = (
//   notifications: AppNotification[] = [],
//   unreadCount: number = 0,
//   preferences: NotificationPreference[] = []
// ): MockedResponse[] => [
//     {
//       request: {
//         query: GET_MY_NOTIFICATIONS,
//         variables: { skip: 0, take: 50, unreadOnly: false }
//       },
//       result: {
//         data: {
//           myNotifications: notifications
//         }
//       }
//     },
//     {
//       request: {
//         query: GET_UNREAD_NOTIFICATION_COUNT
//       },
//       result: {
//         data: {
//           unreadNotificationCount: unreadCount
//         }
//       }
//     },
//     {
//       request: {
//         query: GET_ALL_NOTIFICATION_PREFERENCES
//       },
//       result: {
//         data: {
//           allNotificationPreferences: preferences
//         }
//       }
//     }
//   ];

// export const createMarkAsReadMock = (notificationId: number): MockedResponse => ({
//   request: {
//     query: MARK_NOTIFICATION_AS_READ,
//     variables: { notificationId }
//   },
//   result: {
//     data: {
//       markNotificationAsRead: true
//     }
//   }
// });

// export const createMarkAllAsReadMock = (): MockedResponse => ({
//   request: {
//     query: MARK_ALL_NOTIFICATIONS_AS_READ
//   },
//   result: {
//     data: {
//       markAllNotificationsAsRead: true
//     }
//   }
// });

// export const createUpdatePreferencesMock = (
//   notificationType: string,
//   preferences: Partial<NotificationPreference>
// ): MockedResponse => ({
//   request: {
//     query: UPDATE_NOTIFICATION_PREFERENCES,
//     variables: {
//       notificationType,
//       inAppEnabled: preferences.inAppEnabled ?? true,
//       emailEnabled: preferences.emailEnabled ?? true,
//       smsEnabled: preferences.smsEnabled ?? false,
//       minimumPriority: preferences.minimumPriority ?? NotificationPriority.LOW,
//       doNotDisturbEnabled: preferences.doNotDisturbEnabled ?? false,
//       doNotDisturbStart: preferences.doNotDisturbStart,
//       doNotDisturbEnd: preferences.doNotDisturbEnd
//     }
//   },
//   result: {
//     data: {
//       updateNotificationPreferences: true
//     }
//   }
// });

// export const createSendTestNotificationMock = (): MockedResponse => ({
//   request: {
//     query: SEND_TEST_NOTIFICATION,
//     variables: {
//       title: 'Test Notification',
//       message: 'This is a test notification to verify your notification settings.',
//       priority: NotificationPriority.MEDIUM
//     }
//   },
//   result: {
//     data: {
//       sendTestNotification: true
//     }
//   }
// });

// export const createSubscriptionMock = (notification: Notification): MockedResponse => ({
//   request: {
//     query: ON_NOTIFICATION
//   },
//   result: {
//     data: {
//       onNotification: notification
//     }
//   }
// });

// // Error mocks
// export const createErrorMock = (query: any, variables: any = {}, errorMessage: string = 'Network error'): MockedResponse => ({
//   request: {
//     query,
//     variables
//   },
//   error: new Error(errorMessage)
// });

// export const createNetworkErrorMock = (query: any, variables: any = {}): MockedResponse => ({
//   request: {
//     query,
//     variables
//   },
//   // networkError: new Error('Network connection failed')
// });

// // Test scenarios
// export const createHighPriorityNotificationScenario = () => {
//   const notifications = [
//     createMockNotification({
//       id: 1,
//       type: 'training_expired',
//       title: 'Critical: Training Expired',
//       message: 'Your safety certification has expired and requires immediate renewal',
//       priority: NotificationPriority.CRITICAL,
//     }),
//     createMockNotification({
//       id: 2,
//       type: 'safety_incident',
//       title: 'Safety Incident Reported',
//       message: 'A safety incident has been reported at Site Alpha',
//       priority: NotificationPriority.HIGH,
//     })
//   ];

//   return {
//     notifications,
//     unreadCount: 2,
//     mocks: createNotificationMocks(notifications, 2)
//   };
// };

// export const createMixedNotificationScenario = () => {
//   const notifications = [
//     createMockNotification({
//       id: 1,
//       type: 'training_expiring',
//       title: 'Training Expiring Soon',
//       priority: NotificationPriority.HIGH,
//     }),
//     createMockNotification({
//       id: 2,
//       type: 'worker_added',
//       title: 'New Worker Added',
//       priority: NotificationPriority.MEDIUM,
//       readAt: '2024-01-01T10:00:00Z'
//     }),
//     createMockNotification({
//       id: 3,
//       type: 'system_alert',
//       title: 'System Maintenance',
//       priority: NotificationPriority.LOW,
//     })
//   ];

//   return {
//     notifications,
//     unreadCount: 2,
//     mocks: createNotificationMocks(notifications, 2)
//   };
// };

// export const createEmptyNotificationScenario = () => ({
//   notifications: [],
//   unreadCount: 0,
//   mocks: createNotificationMocks([], 0)
// });

// export const createPreferencesScenario = () => {
//   const preferences = [
//     createMockPreference({
//       notificationType: 'training_expiring',
//       inAppEnabled: true,
//       emailEnabled: true,
//       smsEnabled: false,
//       minimumPriority: NotificationPriority.MEDIUM
//     }),
//     createMockPreference({
//       notificationType: 'safety_incident',
//       inAppEnabled: true,
//       emailEnabled: true,
//       smsEnabled: true,
//       minimumPriority: NotificationPriority.HIGH
//     }),
//     createMockPreference({
//       notificationType: 'system_alert',
//       inAppEnabled: false,
//       emailEnabled: true,
//       smsEnabled: false,
//       minimumPriority: NotificationPriority.LOW
//     })
//   ];

//   return {
//     preferences,
//     mocks: createNotificationMocks([], 0, preferences)
//   };
// };

// // Utility functions for testing
// export const waitForNotificationLoad = async (getByTestId: (id: string) => HTMLElement) => {
//   const loadingElement = getByTestId('loading');

//   // Wait for loading to complete
//   await new Promise(resolve => {
//     const observer = new MutationObserver(() => {
//       if (loadingElement.textContent === 'Loaded') {
//         observer.disconnect();
//         resolve(undefined);
//       }
//     });

//     observer.observe(loadingElement, { childList: true, subtree: true });

//     // Fallback timeout
//     setTimeout(() => {
//       observer.disconnect();
//       resolve(undefined);
//     }, 5000);
//   });
// };

// export const simulateNotificationReceived = (notification: Notification) => {
//   // Simulate real-time notification received
//   const event = new CustomEvent('notification-received', {
//     detail: notification
//   });
//   window.dispatchEvent(event);
// };

// export const mockBrowserNotifications = () => {
//   const mockNotification = {
//     close: jest.fn(),
//     onclick: null,
//     onerror: null,
//     onshow: null,
//     onclose: null
//   };

//   global.Notification = jest.fn().mockImplementation(() => mockNotification) as any;
//   // global.Notification.permission = 'granted';
//   global.Notification.requestPermission = jest.fn().mockResolvedValue('granted');

//   return mockNotification;
// };

// export const mockAudioPlayback = () => {
//   const mockAudio = {
//     play: jest.fn().mockResolvedValue(undefined),
//     pause: jest.fn(),
//     volume: 0.5,
//     currentTime: 0,
//     duration: 0
//   };

//   global.Audio = jest.fn().mockImplementation(() => mockAudio);

//   return mockAudio;
// };

// // Performance testing utilities
// export const measureNotificationRenderTime = async (renderFunction: () => void) => {
//   const startTime = performance.now();
//   renderFunction();
//   const endTime = performance.now();
//   return endTime - startTime;
// };

// export const simulateSlowNetwork = (delay: number = 2000) => {
//   return new Promise(resolve => setTimeout(resolve, delay));
// };
