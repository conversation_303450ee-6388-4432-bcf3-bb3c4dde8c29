export function truncateWords(text: string | undefined | null, maxWords: number = 6, ellipsis: boolean = true): string {
  if (!text) return '';
  const words = text.trim().split(/\s+/);
  if (words.length <= maxWords) return text.trim();
  const sliced = words.slice(0, maxWords).join(' ');
  return ellipsis ? `${sliced}…` : sliced;
}

export type BasicToastType = 'success' | 'error' | 'info' | 'warning' | 'default';

export function sanitizeToastMessage(msg: string, maxWords: number = 6): string {
  // Hard-limit to a specific number of words for clarity across the app
  return truncateWords(msg, maxWords, true);
}
