import { ApolloError } from '@apollo/client';
import { toast } from 'react-toastify';
import { sanitizeToastMessage } from './toastUtils';

export interface NotificationError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  retryable: boolean;
}

export class NotificationErrorHandler {
  private static instance: NotificationErrorHandler;
  private errorLog: NotificationError[] = [];
  private maxLogSize = 100;

  private constructor() {}

  public static getInstance(): NotificationErrorHandler {
    if (!NotificationErrorHandler.instance) {
      NotificationErrorHandler.instance = new NotificationErrorHandler();
    }
    return NotificationErrorHandler.instance;
  }

  public handleError(error: any, context: string = 'notification'): NotificationError {
    const notificationError = this.parseError(error, context);
    this.logError(notificationError);
    this.showUserFeedback(notificationError);
    return notificationError;
  }

  private parseError(error: any, _context: string): NotificationError {
    let code = 'UNKNOWN_ERROR';
    let message = 'An unexpected error occurred';
    let retryable = false;
    let details = error;

    if (error instanceof ApolloError) {
      if (error.networkError) {
        code = 'NETWORK_ERROR';
        message = 'Network connection failed. Please check your internet connection.';
        retryable = true;
        
        if (error.networkError.message.includes('WebSocket')) {
          code = 'WEBSOCKET_ERROR';
          message = 'Real-time connection failed. Notifications may be delayed.';
        }
      } else if (error.graphQLErrors?.length > 0) {
        const gqlError = error.graphQLErrors[0];
        code = gqlError.extensions?.code as string || 'GRAPHQL_ERROR';
        message = gqlError.message;
        retryable = this.isRetryableGraphQLError(code);
      }
    } else if (error.name === 'NotAllowedError') {
      code = 'PERMISSION_DENIED';
      message = 'Browser notifications are blocked. Please enable them in your browser settings.';
      retryable = false;
    } else if (error.name === 'AbortError') {
      code = 'REQUEST_ABORTED';
      message = 'Request was cancelled';
      retryable = true;
    } else if (error.message) {
      message = error.message;
      retryable = this.isRetryableError(error.message);
    }

    return {
      code,
      message,
      details,
      timestamp: new Date(),
      retryable
    };
  }

  private isRetryableGraphQLError(code: string): boolean {
    const retryableCodes = [
      'INTERNAL_SERVER_ERROR',
      'TIMEOUT',
      'RATE_LIMITED',
      'SERVICE_UNAVAILABLE'
    ];
    return retryableCodes.includes(code);
  }

  private isRetryableError(message: string): boolean {
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /connection/i,
      /temporary/i,
      /rate limit/i
    ];
    return retryablePatterns.some(pattern => pattern.test(message));
  }

  private logError(error: NotificationError): void {
    this.errorLog.unshift(error);
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[NotificationError] ${error.code}: ${error.message}`, error.details);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(error);
    }
  }

  private showUserFeedback(error: NotificationError): void {
    // Don't show toast for certain error types
    const silentErrors = ['PERMISSION_DENIED', 'REQUEST_ABORTED'];
    if (silentErrors.includes(error.code)) {
      return;
    }

    // Build a stable toastId to avoid duplicate popups for the same error context
    const safeMessage = (error.message || '').slice(0, 80).replace(/\s+/g, ' ').trim();
    const toastId = `notification-error-${error.code}-${safeMessage}`;

    // If already visible, do not show another identical toast
    if (toast.isActive(toastId)) {
      return;
    }

    const toastOptions = {
      position: 'top-right' as const,
      autoClose: error.retryable ? 5000 : 8000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      toastId,
    };

    if (error.retryable) {
      const msg = sanitizeToastMessage(error.message || 'Temporary issue, retrying.');
      toast.warning(msg, toastOptions);
    } else {
      const msg = sanitizeToastMessage(error.message || 'An unexpected error occurred');
      toast.error(msg, toastOptions);
    }
  }

  private sendToMonitoring(_error: NotificationError): void {
    // Implement your monitoring service integration here
    // Example: Sentry, LogRocket, etc.
    try {
      // window.gtag?.('event', 'exception', {
      //   description: `${error.code}: ${error.message}`,
      //   fatal: false
      // });
    } catch (monitoringError) {
      console.warn('Failed to send error to monitoring service:', monitoringError);
    }
  }

  public getErrorLog(): NotificationError[] {
    return [...this.errorLog];
  }

  public clearErrorLog(): void {
    this.errorLog = [];
  }

  public getErrorStats(): { total: number; retryable: number; recent: number } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    return {
      total: this.errorLog.length,
      retryable: this.errorLog.filter(e => e.retryable).length,
      recent: this.errorLog.filter(e => e.timestamp > oneHourAgo).length
    };
  }
}

// Retry utility with exponential backoff
export class RetryHandler {
  public static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 10000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }

        const errorHandler = NotificationErrorHandler.getInstance();
        const notificationError = errorHandler.handleError(error, 'retry');
        
        if (!notificationError.retryable) {
          throw error;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}

// Connection status monitor
export class ConnectionMonitor {
  private static instance: ConnectionMonitor;
  private isOnline: boolean = navigator.onLine;
  private listeners: ((online: boolean) => void)[] = [];

  private constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  public static getInstance(): ConnectionMonitor {
    if (!ConnectionMonitor.instance) {
      ConnectionMonitor.instance = new ConnectionMonitor();
    }
    return ConnectionMonitor.instance;
  }

  private handleOnline = () => {
    this.isOnline = true;
    this.notifyListeners();
    const toastId = 'connection-restored';
    if (!toast.isActive(toastId)) {
      toast.success('Connection restored', { autoClose: 3000, toastId });
    }
  };

  private handleOffline = () => {
    this.isOnline = false;
    this.notifyListeners();
    const toastId = 'connection-lost';
    if (!toast.isActive(toastId)) {
      toast.warning('Connection lost. Limited features.', { autoClose: false, toastId });
    }
  };

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.isOnline));
  }

  public addListener(listener: (online: boolean) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  public isConnected(): boolean {
    return this.isOnline;
  }
}

// Export singleton instances
export const notificationErrorHandler = NotificationErrorHandler.getInstance();
export const connectionMonitor = ConnectionMonitor.getInstance();
