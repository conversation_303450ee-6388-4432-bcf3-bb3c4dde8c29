// Training-related TypeScript interfaces

// Worker Training Status
export interface WorkerTrainingStatus {
	workerId: string;
	name: string;
	photo?: string;
	nationalId: string;
	primaryTrade: string;
	requiredTrainings: number;
	completedTrainings: number;
	complianceStatus: "compliant" | "expiring" | "expired" | "incomplete";
	nextExpiryDate?: Date;
	trainings: WorkerTraining[];
}

// Individual Training Record
export interface WorkerTraining {
	id: string;
	trainingProgramId: string;
	trainingProgramName: string;
	status: "completed" | "in-progress" | "assigned" | "expired";
	completedDate?: Date;
	expiryDate?: Date;
	certificateNumber?: string;
	instructor?: string;
	score?: number;
}

// Training Program
export interface TrainingProgram {
	id: string;
	name: string;
	description: string;
	category: string;
	duration: number; // in hours
	validityPeriod: number; // in months
	isRequired: boolean;
	requiredForTrades: string[];
	prerequisites: string[];
	approvedProviders?: string[]; // Pre-approved provider IDs
	status: "active" | "inactive" | "draft";
	createdAt: Date;
	updatedAt: Date;
}

// Training Session
export interface TrainingSession {
	id: string;
	title: string;
	description: string;
	trainingProgramId: string;
	date: Date;
	startTime: string;
	endTime: string;
	location: string;
	instructor: string;
	maxAttendees: number;
	attendees: number;
	status: "scheduled" | "in-progress" | "completed" | "cancelled";
	registeredWorkers: string[];
}

// Training Event (for calendar)
export interface TrainingEvent {
	id: string;
	title: string;
	start: Date;
	end: Date;
	location: string;
	status: "scheduled" | "in-progress" | "completed" | "cancelled";
	color: string;
	sessionId: string;
}

// Training Report
export interface TrainingReport {
	title: string;
	description: string;
	generatedAt: Date;
	chartData: any;
	chartType: "bar" | "line" | "pie" | "doughnut";
	tableData: any[];
	columns: {
		key: string;
		label: string;
	}[];
}

// Training Statistics
export interface TrainingStats {
	overallCompliance: number;
	validTrainings: number;
	expiringSoon: number;
	expired: number;
	totalWorkers: number;
	activePrograms: number;
}

// Training Assignment
export interface TrainingAssignment {
	workerId: string;
	trainingProgramId: string;
	assignedBy: string;
	assignedDate: Date;
	dueDate?: Date;
	priority: "low" | "medium" | "high";
	notes?: string;
}

// Training Completion Record
export interface TrainingCompletion {
	workerId: string;
	trainingProgramId: string;
	sessionId?: string;
	completedDate: Date;
	instructor: string;
	score?: number;
	certificateNumber?: string;
	notes?: string;
	recordedBy: string;
}

// Compliance Summary by Trade
export interface TradeComplianceData {
	trade: string;
	totalWorkers: number;
	compliantWorkers: number;
	compliancePercentage: number;
	expiringCount: number;
	expiredCount: number;
}

// Upcoming Training
export interface UpcomingTraining {
	id: string;
	title: string;
	date: Date;
	time: string;
	location: string;
	availableSpots: number;
	totalSpots: number;
}

// Expiring Certification
export interface ExpiringCertification {
	workerId: string;
	workerName: string;
	trainingName: string;
	expiryDate: Date;
	daysUntilExpiry: number;
	priority: "high" | "medium" | "low";
}
