// Notification Types - matching backend models

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum NotificationStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED'
}

export enum NotificationChannel {
  IN_APP = 'IN_APP',
  EMAIL = 'EMAIL',
  SMS = 'SMS'
}

export interface NotificationDelivery {
  id: number;
  channel: NotificationChannel;
  status: NotificationStatus;
  recipient?: string;
  sentAt?: string;
  deliveredAt?: string;
  failedAt?: string;
  errorMessage?: string;
  retryCount: number;
  nextRetryAt?: string;
}

export interface AppNotification {
  id: number;
  type: string;
  title: string;
  message: string;
  priority: NotificationPriority;
  status: NotificationStatus;
  entity?: string;
  operation?: string;
  metadata?: Metadata; // JSON string
  sentAt?: string;
  readAt?: string;
  expiresAt?: string;
  actionUrl?: string;
  actionLabel?: string;
  userId: number;
  tenantId: number;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  deliveries?: NotificationDelivery[];
}

export interface Metadata{
  workerId?: number;
  workerName?: string;
  trainingId?: number;
  trainingName?: string;
  siteId?: string;
  siteName?: string;
  daysUntilExpiry?: number;
  status?: string;
  requestId?: string;
}

export interface NotificationPreference {
  id: number;
  notificationType: string;
  inAppEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  minimumPriority: NotificationPriority;
  doNotDisturbEnabled: boolean;
  doNotDisturbStart?: string; // TimeSpan as string
  doNotDisturbEnd?: string; // TimeSpan as string
  userId: number;
  createdAt: string;
  updatedAt?: string;
}

export interface NotificationEvent {
  type: string;
  title: string;
  message: string;
  entity?: string;
  operation?: string;
  metadata?: Record<string, any>;
}

// Frontend-specific types
export interface NotificationHookResult {
  notifications: AppNotification[];
  loading: boolean;
  error?: Error;
  unreadCount: number;
  refetch: () => Promise<void>;
  markAsRead: (notificationId: number) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

export interface NotificationPreferencesHookResult {
  preferences: NotificationPreference[];
  loading: boolean;
  error?: Error;
  updatePreferences: (
    notificationType: string,
    preferences: Partial<NotificationPreference>
  ) => Promise<void>;
  sendTestNotification: (
    title?: string,
    message?: string,
    priority?: NotificationPriority
  ) => Promise<void>;
}

export interface NotificationSystemProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxNotifications?: number;
  autoHideDelay?: number;
  enableSound?: boolean;
  enableRealTime?: boolean;
}

// Utility types for parsing metadata
export interface TrainingNotificationMetadata {
  workerId?: number;
  workerName?: string;
  trainingId?: number;
  trainingName?: string;
  siteId?: string;
  siteName?: string;
  daysUntilExpiry?: number;
  status?: string;
}

export interface WorkerNotificationMetadata {
  workerId?: number;
  workerName?: string;
  siteId?: string;
  siteName?: string;
  roleId?: number;
  roleName?: string;
}

export interface SystemNotificationMetadata {
  component?: string;
  version?: string;
  severity?: string;
  details?: string;
}
