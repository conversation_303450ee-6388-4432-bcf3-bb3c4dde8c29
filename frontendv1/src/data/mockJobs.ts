// Centralized mock data for Jobs/Tasks that complies with the Job GraphQL schema
// This file contains realistic job data using only fields that exist in the Job schema

export interface MockJob {
  // Core Job fields from GraphQL schema
  id: number;
  title: string;
  description?: string;
  location?: string;
  ppEs?: string[];
  status: JobStatus;
  precautionsRequired?: string[];
  requiredPermits?: PermitType[];
  timeForCompletion?: string; // TimeSpan type
  startDate: string; // DateTime
  dueDate?: string; // DateTime
  calculatedDueDate: string; // DateTime
  categoryId?: number;
  category?: {
    id: number;
    description: string;
  };
  
  // Person references with proper Worker type structure
  requestedById?: number;
  requestedBy?: {
    id: number;
    name: string;
    company?: string;
  };
  requestedDate?: string;
  
  blockedById?: number;
  blockedBy?: {
    id: number;
    name: string;
    company?: string;
  };
  blockedDate?: string;
  
  reviewedById?: number;
  reviewedBy?: {
    id: number;
    name: string;
    company?: string;
  };
  reviewedDate?: string;
  
  approvedById?: number;
  approvedBy?: {
    id: number;
    name: string;
    company?: string;
  };
  approvedDate?: string;
  
  finishedById?: number;
  finishedBy?: {
    id: number;
    name: string;
    company?: string;
  };
  finishedDate?: string;
  
  chiefEngineerId?: number;
  chiefEngineer?: {
    id: number;
    name: string;
    company?: string;
  };
  
  // Additional schema fields
  requiredTrades?: any[];
  requiredTrainings?: any[];
  hazards?: any[];
  documents?: any[];
  workers?: {
    id: number;
    name: string;
    company?: string;
  }[];
  
  // Metadata
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
}

// JobStatus enum values from GraphQL schema
export type JobStatus = 
  | 'REQUESTED'        // When a job is requested by engineer
  | 'BLOCKED'          // When a job is blocked by site HSE
  | 'PENDING_APPROVAL' // When a job is pending approval by admin HSE
  | 'APPROVED'         // When a job is approved by admin HSE
  | 'DISAPPROVED'      // When a job is disapproved by admin HSE
  | 'FINISHED';        // When a job is marked as finished by engineer

// PermitType enum values from GraphQL schema
export type PermitType = 
  | 'GENERAL_WORK_PERMIT'
  | 'HOT_WORK_PERMIT'
  | 'CONFINED_SPACE_ENTRY_PERMIT'
  | 'WORK_AT_HEIGHT_PERMIT'
  | 'EXCAVATION_PERMIT';

// Mock job data that complies with Job GraphQL schema
export const mockJobs: MockJob[] = [
  // REQUESTED status - Job requested by engineer
  {
    id: 1,
    title: "Concrete Pouring - Foundation Level 1",
    description: "Pour concrete for foundation at Level 1, Zone A. Ensure proper curing and quality control measures.",
    location: "Zone A - Level 1",
    status: "REQUESTED",
    requiredPermits: ["GENERAL_WORK_PERMIT"],
    timeForCompletion: "8 hours",
    startDate: "2024-01-20T08:00:00Z",
    dueDate: "2024-01-20T16:00:00Z",
    calculatedDueDate: "2024-01-20T16:00:00Z",
    categoryId: 1,
    category: {
      id: 1,
      description: "Construction"
    },
    requestedById: 101,
    requestedBy: {
      id: 101,
      name: "John Smith",
      company: "ABC Construction"
    },
    requestedDate: "2024-01-18T14:30:00Z",
    chiefEngineerId: 201,
    chiefEngineer: {
      id: 201,
      name: "Sarah Johnson",
      company: "ABC Construction"
    },
    workers: [
      { id: 301, name: "Mike Wilson", company: "ABC Construction" },
      { id: 302, name: "David Brown", company: "ABC Construction" }
    ],
    createdAt: "2024-01-18T14:30:00Z",
    createdBy: "<EMAIL>",
    isDeleted: false
  },

  // PENDING_APPROVAL status - Job pending approval by admin HSE
  {
    id: 2,
    title: "Electrical Wiring - Floor 2",
    description: "Install electrical wiring for Floor 2 offices including power outlets and lighting circuits.",
    location: "Zone B - Floor 2",
    status: "PENDING_APPROVAL",
    requiredPermits: ["GENERAL_WORK_PERMIT", "HOT_WORK_PERMIT"],
    timeForCompletion: "12 hours",
    startDate: "2024-01-21T08:00:00Z",
    dueDate: "2024-01-21T20:00:00Z",
    calculatedDueDate: "2024-01-21T20:00:00Z",
    categoryId: 2,
    category: {
      id: 2,
      description: "Electrical"
    },
    requestedById: 102,
    requestedBy: {
      id: 102,
      name: "Ali Hassan",
      company: "ElectroTech Ltd"
    },
    requestedDate: "2024-01-19T09:15:00Z",
    reviewedById: 401,
    reviewedBy: {
      id: 401,
      name: "Mary Wanjiru",
      company: "Safety First HSE"
    },
    reviewedDate: "2024-01-19T16:45:00Z",
    chiefEngineerId: 202,
    chiefEngineer: {
      id: 202,
      name: "Peter Otieno",
      company: "ElectroTech Ltd"
    },
    workers: [
      { id: 303, name: "James Kim", company: "ElectroTech Ltd" },
      { id: 304, name: "Ann Kendi", company: "ElectroTech Ltd" }
    ],
    createdAt: "2024-01-19T09:15:00Z",
    createdBy: "<EMAIL>",
    updatedAt: "2024-01-19T16:45:00Z",
    updatedBy: "<EMAIL>",
    isDeleted: false
  },

  // APPROVED status - Job approved by admin HSE
  {
    id: 3,
    title: "Plumbing Installation - Building B",
    description: "Install main water line and valves on ground floor. Include pressure testing and leak detection.",
    location: "Building B - Ground Floor",
    status: "APPROVED",
    requiredPermits: ["GENERAL_WORK_PERMIT", "EXCAVATION_PERMIT"],
    timeForCompletion: "16 hours",
    startDate: "2024-01-22T07:00:00Z",
    dueDate: "2024-01-23T15:00:00Z",
    calculatedDueDate: "2024-01-23T15:00:00Z",
    categoryId: 3,
    category: {
      id: 3,
      description: "Plumbing"
    },
    requestedById: 103,
    requestedBy: {
      id: 103,
      name: "Samuel Kariuki",
      company: "AquaFlow Systems"
    },
    requestedDate: "2024-01-18T11:20:00Z",
    reviewedById: 401,
    reviewedBy: {
      id: 401,
      name: "Mary Wanjiru",
      company: "Safety First HSE"
    },
    reviewedDate: "2024-01-19T10:30:00Z",
    approvedById: 501,
    approvedBy: {
      id: 501,
      name: "Grace Muthoni",
      company: "Safety First HSE"
    },
    approvedDate: "2024-01-19T14:15:00Z",
    chiefEngineerId: 203,
    chiefEngineer: {
      id: 203,
      name: "Lydia Njoroge",
      company: "AquaFlow Systems"
    },
    workers: [
      { id: 305, name: "Robert Ochieng", company: "AquaFlow Systems" },
      { id: 306, name: "Jane Doe", company: "AquaFlow Systems" },
      { id: 307, name: "Tom Anderson", company: "AquaFlow Systems" }
    ],
    createdAt: "2024-01-18T11:20:00Z",
    createdBy: "<EMAIL>",
    updatedAt: "2024-01-19T14:15:00Z",
    updatedBy: "<EMAIL>",
    isDeleted: false
  },

  // BLOCKED status - Job blocked by site HSE
  {
    id: 4,
    title: "HVAC Ducting Installation - Level 3",
    description: "Install HVAC ducts in corridor and rooms. Requires coordination with electrical team.",
    location: "Building A - Level 3",
    status: "BLOCKED",
    requiredPermits: ["GENERAL_WORK_PERMIT", "WORK_AT_HEIGHT_PERMIT"],
    timeForCompletion: "20 hours",
    startDate: "2024-01-23T08:00:00Z",
    dueDate: "2024-01-24T20:00:00Z",
    calculatedDueDate: "2024-01-24T20:00:00Z",
    categoryId: 4,
    category: {
      id: 4,
      description: "HVAC"
    },
    requestedById: 104,
    requestedBy: {
      id: 104,
      name: "Michael Chen",
      company: "CoolAir HVAC"
    },
    requestedDate: "2024-01-17T13:45:00Z",
    reviewedById: 401,
    reviewedBy: {
      id: 401,
      name: "Mary Wanjiru",
      company: "Safety First HSE"
    },
    reviewedDate: "2024-01-18T09:20:00Z",
    blockedById: 401,
    blockedBy: {
      id: 401,
      name: "Mary Wanjiru",
      company: "Safety First HSE"
    },
    blockedDate: "2024-01-18T15:30:00Z",
    chiefEngineerId: 204,
    chiefEngineer: {
      id: 204,
      name: "Kevin Mutua",
      company: "CoolAir HVAC"
    },
    workers: [
      { id: 308, name: "Daniel Kiprotich", company: "CoolAir HVAC" },
      { id: 309, name: "Susan Wambui", company: "CoolAir HVAC" }
    ],
    createdAt: "2024-01-17T13:45:00Z",
    createdBy: "<EMAIL>",
    updatedAt: "2024-01-18T15:30:00Z",
    updatedBy: "<EMAIL>",
    isDeleted: false
  },

  // FINISHED status - Job marked as finished by engineer
  {
    id: 5,
    title: "Safety Signage Installation",
    description: "Install mandatory safety signage across site per safety plan. Include emergency exit signs and hazard warnings.",
    location: "All Zones",
    status: "FINISHED",
    requiredPermits: ["GENERAL_WORK_PERMIT"],
    timeForCompletion: "6 hours",
    startDate: "2024-01-15T08:00:00Z",
    dueDate: "2024-01-15T14:00:00Z",
    calculatedDueDate: "2024-01-15T14:00:00Z",
    categoryId: 5,
    category: {
      id: 5,
      description: "Safety"
    },
    requestedById: 105,
    requestedBy: {
      id: 105,
      name: "Patricia Akinyi",
      company: "SafeSign Solutions"
    },
    requestedDate: "2024-01-12T10:00:00Z",
    reviewedById: 401,
    reviewedBy: {
      id: 401,
      name: "Mary Wanjiru",
      company: "Safety First HSE"
    },
    reviewedDate: "2024-01-13T11:15:00Z",
    approvedById: 501,
    approvedBy: {
      id: 501,
      name: "Grace Muthoni",
      company: "Safety First HSE"
    },
    approvedDate: "2024-01-13T16:45:00Z",
    finishedById: 105,
    finishedBy: {
      id: 105,
      name: "Patricia Akinyi",
      company: "SafeSign Solutions"
    },
    finishedDate: "2024-01-15T13:30:00Z",
    chiefEngineerId: 205,
    chiefEngineer: {
      id: 205,
      name: "Francis Mwangi",
      company: "SafeSign Solutions"
    },
    workers: [
      { id: 310, name: "Joseph Kamau", company: "SafeSign Solutions" },
      { id: 311, name: "Lucy Nyong'o", company: "SafeSign Solutions" }
    ],
    createdAt: "2024-01-12T10:00:00Z",
    createdBy: "<EMAIL>",
    updatedAt: "2024-01-15T13:30:00Z",
    updatedBy: "<EMAIL>",
    isDeleted: false
  },

  // DISAPPROVED status - Job disapproved by admin HSE
  {
    id: 6,
    title: "Temporary Power Setup - West Wing",
    description: "Set up temporary power distribution for west wing construction activities.",
    location: "West Wing - Temporary Area",
    status: "DISAPPROVED",
    requiredPermits: ["GENERAL_WORK_PERMIT", "HOT_WORK_PERMIT"],
    timeForCompletion: "10 hours",
    startDate: "2024-01-24T08:00:00Z",
    dueDate: "2024-01-24T18:00:00Z",
    calculatedDueDate: "2024-01-24T18:00:00Z",
    categoryId: 2,
    category: {
      id: 2,
      description: "Electrical"
    },
    requestedById: 106,
    requestedBy: {
      id: 106,
      name: "George Omondi",
      company: "PowerTech Electrical"
    },
    requestedDate: "2024-01-19T08:30:00Z",
    reviewedById: 402,
    reviewedBy: {
      id: 402,
      name: "John Maina",
      company: "Safety First HSE"
    },
    reviewedDate: "2024-01-19T14:20:00Z",
    chiefEngineerId: 206,
    chiefEngineer: {
      id: 206,
      name: "Catherine Wanjiku",
      company: "PowerTech Electrical"
    },
    workers: [
      { id: 312, name: "Stephen Kibet", company: "PowerTech Electrical" },
      { id: 313, name: "Rose Chebet", company: "PowerTech Electrical" }
    ],
    createdAt: "2024-01-19T08:30:00Z",
    createdBy: "<EMAIL>",
    updatedAt: "2024-01-19T14:20:00Z",
    updatedBy: "<EMAIL>",
    isDeleted: false
  }
];

// Export job statistics for dashboard
export const mockJobStats = {
  totalJobs: 6,
  requestedJobs: 1,
  pendingApprovalJobs: 1,
  approvedJobs: 1,
  blockedJobs: 1,
  finishedJobs: 1,
  disapprovedJobs: 1,
  jobsCompletedToday: 1,
  averageCompletionTime: 8.5,
  onTimeCompletionRate: 85,
  productivityScore: 78
};

// Helper function to get jobs by status
export const getJobsByStatus = (status: JobStatus): MockJob[] => {
  return mockJobs.filter(job => job.status === status);
};

// Helper function to get recent jobs (sorted by creation date)
export const getRecentJobs = (limit: number = 10): MockJob[] => {
  return [...mockJobs]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, limit);
};
