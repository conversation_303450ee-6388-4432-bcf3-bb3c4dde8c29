// Time Management Mock Data aligned with Hikvision device-based workflow
// Covers: devices, site-device relationships, raw events, processed attendance,
// worker assignments syncing to devices, and helpers to drive UI components

import { TimeLog } from "../types";
import { TerminalStatus } from "../types/time";
import { mockCompanyWorkers } from "./workers";

// Device model (Hikvision terminal)
export type HikDevice = {
  id: string;
  siteId: string;
  name: string;
  location: string;
  model: string;
  ipAddress: string;
  port: number;
  status: "online" | "offline" | "error";
  lastSeen: string; // ISO
  firmwareVersion: string;
  serialNumber: string;
  capacity: {
    maxUsers: number;
    maxFaces: number;
    storageGB: number;
  };
  usage: {
    users: number;
    faces: number;
    storageUsedGB: number;
  };
};

// Site-device relationships
export const siteDevices: Record<string, HikDevice[]> = {
  site1: [
    {
      id: "term1",
      siteId: "site1",
      name: "Main Gate Terminal",
      location: "Site Entrance",
      model: "DS-K1T341",
      ipAddress: "*************",
      port: 80,
      status: "online",
      lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      firmwareVersion: "v3.2.1",
      serialNumber: "HK-ABC-001",
      capacity: { maxUsers: 3000, maxFaces: 3000, storageGB: 8 },
      usage: { users: 128, faces: 120, storageUsedGB: 1.2 },
    },
    {
      id: "term2",
      siteId: "site1",
      name: "Warehouse Terminal",
      location: "Equipment Storage",
      model: "DS-K1T341",
      ipAddress: "*************",
      port: 80,
      status: "online",
      lastSeen: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      firmwareVersion: "v3.2.1",
      serialNumber: "HK-ABC-002",
      capacity: { maxUsers: 3000, maxFaces: 3000, storageGB: 8 },
      usage: { users: 128, faces: 118, storageUsedGB: 0.9 },
    },
    {
      id: "term3",
      siteId: "site1",
      name: "Office Terminal",
      location: "Site Office",
      model: "DS-K1T341",
      ipAddress: "*************",
      port: 80,
      status: "offline",
      lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      firmwareVersion: "v3.2.1",
      serialNumber: "HK-ABC-003",
      capacity: { maxUsers: 3000, maxFaces: 3000, storageGB: 8 },
      usage: { users: 128, faces: 118, storageUsedGB: 0.4 },
    },
  ],
};

// Worker assignment to sites and device sync state
export type WorkerDeviceAssignment = {
  workerId: number;
  siteId: string;
  assignedAt: string; // ISO
  pushedToDevices: boolean;
  lastSyncAt?: string; // ISO
};

export const workerAssignments: WorkerDeviceAssignment[] = [
  { workerId: 1, siteId: "site1", assignedAt: "2024-12-01T00:00:00Z", pushedToDevices: true, lastSyncAt: new Date(Date.now() - 10 * 60 * 1000).toISOString() },
  { workerId: 2, siteId: "site1", assignedAt: "2024-11-01T00:00:00Z", pushedToDevices: true, lastSyncAt: new Date(Date.now() - 10 * 60 * 1000).toISOString() },
  { workerId: 3, siteId: "site1", assignedAt: "2025-01-05T00:00:00Z", pushedToDevices: false },
];

// Raw device events (ingestion layer)
export type RawEvent = {
  id: string; // internal PK
  deviceId: string; // FK to device
  siteId: string; // FK to site
  hikSerialNo: number; // serialNo from device
  employeeNo: string; // employeeNoString
  employeeName: string;
  timestamp: string; // ISO
  eventType: string; // e.g., AccessControllerEvent
  verifyMode: string; // face, card
  attendanceStatus: "checkIn" | "checkOut" | "alert";
  maskStatus?: string; // mask, unMask
  pictureUrl?: string;
  source: "PUSH" | "PULL";
  rawPayload: string; // JSON/XML string
};

// Helper to build ISO date for today with time HH:mm
const todayDateStr = (): string => new Date().toISOString().split("T")[0];
const isoAt = (time: string): string => `${todayDateStr()}T${time}:00+03:00`;

export const rawEvents: RawEvent[] = [
  {
    id: "evt-1001",
    deviceId: "term1",
    siteId: "site1",
    hikSerialNo: 12345,
    employeeNo: "EMP-001",
    employeeName: "David Kamau",
    timestamp: isoAt("07:55"),
    eventType: "AccessControllerEvent",
    verifyMode: "face",
    attendanceStatus: "checkIn",
    maskStatus: "unMask",
    pictureUrl: "/ISAPI/AccessControl/CaptureFaceData/picture?employeeNo=EMP-001",
    source: "PUSH",
    rawPayload: "<xml>...</xml>",
  },
  {
    id: "evt-1002",
    deviceId: "term1",
    siteId: "site1",
    hikSerialNo: 12346,
    employeeNo: "EMP-002",
    employeeName: "Mary Wanjiku",
    timestamp: isoAt("08:15"),
    eventType: "AccessControllerEvent",
    verifyMode: "face",
    attendanceStatus: "checkIn",
    maskStatus: "unMask",
    pictureUrl: "/ISAPI/AccessControl/CaptureFaceData/picture?employeeNo=EMP-002",
    source: "PUSH",
    rawPayload: "<xml>...</xml>",
  },
  {
    id: "evt-1003",
    deviceId: "term2",
    siteId: "site1",
    hikSerialNo: 12347,
    employeeNo: "EMP-001",
    employeeName: "David Kamau",
    timestamp: isoAt("17:05"),
    eventType: "AccessControllerEvent",
    verifyMode: "face",
    attendanceStatus: "checkOut",
    maskStatus: "unMask",
    pictureUrl: "/ISAPI/AccessControl/CaptureFaceData/picture?employeeNo=EMP-001",
    source: "PULL",
    rawPayload: "{ json: true }",
  },
];

// Processed attendance records created by matching check-ins/outs
export type AttendanceRecord = {
  id: string;
  workerId: number;
  workerName: string;
  siteId: string;
  deviceId: string; // from check-in device
  checkIn: string; // ISO
  checkOut?: string; // ISO
  checkInRawEventId: string;
  checkOutRawEventId?: string;
  hours?: number;
  isVerified: boolean;
};

export const attendanceRecords: AttendanceRecord[] = [
  {
    id: "att-1",
    workerId: 1,
    workerName: "David Kamau",
    siteId: "site1",
    deviceId: "term1",
    checkIn: isoAt("07:55"),
    checkOut: isoAt("17:05"),
    checkInRawEventId: "evt-1001",
    checkOutRawEventId: "evt-1003",
    hours: 8.17,
    isVerified: true,
  },
  {
    id: "att-2",
    workerId: 2,
    workerName: "Mary Wanjiku",
    siteId: "site1",
    deviceId: "term1",
    checkIn: isoAt("08:15"),
    checkInRawEventId: "evt-1002",
    isVerified: true,
  },
];

// Convert devices to TerminalStatus for UI panels
export const getTerminalsForSite = (siteId: string): TerminalStatus[] => {
  const devices = siteDevices[siteId] || [];
  // totalCheckInsToday is derived from rawEvents for the device
  const countsByDevice: Record<string, number> = rawEvents
    .filter((e) => e.siteId === siteId)
    .reduce((acc, e) => {
      acc[e.deviceId] = (acc[e.deviceId] || 0) + (e.attendanceStatus !== "alert" ? 1 : 0);
      return acc;
    }, {} as Record<string, number>);

  return devices.map((d) => ({
    id: d.id,
    name: d.name,
    location: d.location,
    status: d.status,
    lastSync: d.lastSeen,
    totalCheckInsToday: countsByDevice[d.id] || 0,
    ipAddress: d.ipAddress,
  }));
};

// Map processed attendance to front-end TimeLog for a given date
export const getTimeLogsForDate = (siteId: string, date: string): TimeLog[] => {
  const workersByEmpNo: Record<string, { trade: string; photo?: string }> = {};
  // Build a lookup by company employee number (EMP-XXX)
  mockCompanyWorkers.forEach((w) => {
    // Choose first trade name if available
    const trade = (w.trades && w.trades[0]?.name) || "Worker";
    workersByEmpNo[w.employeeNumber] = { trade, photo: w.photoUrl };
  });

  // Build a lookup by numeric workerId, too
  const workersById: Record<number, { name: string; trade: string; photo?: string }> = {};
  mockCompanyWorkers.forEach((w) => {
    const trade = (w.trades && w.trades[0]?.name) || "Worker";
    workersById[w.id] = { name: w.name, trade, photo: w.photoUrl };
  });

  return attendanceRecords
    .filter((r) => r.siteId === siteId && r.checkIn.startsWith(date))
    .map<TimeLog>((r) => {
      const w = workersById[r.workerId];
      // Convert to local HH:mm strings
      const toHM = (iso?: string) => (iso ? new Date(iso).toISOString().split("T")[1].slice(0, 5) : undefined);
      const totalHours = r.checkOut && r.checkIn ? hoursBetween(r.checkIn, r.checkOut) : undefined;
      return {
        id: r.id,
        workerId: String(r.workerId),
        workerName: w?.name || r.workerName,
        workerPhoto: w?.photo,
        workerTrade: w?.trade || "Worker",
        date,
        clockIn: toHM(r.checkIn),
        clockOut: toHM(r.checkOut),
        breakDuration: 60,
        totalHours,
        overtime: undefined, // Overtime dropped in new workflow
        status: r.checkOut ? "on-site" : "on-site",
        toolboxTalkAttended: true,
        isVerifiedByHikvision: r.isVerified,
        hikvisionPersonId: undefined,
        terminalId: r.deviceId,
      };
    });
};

function hoursBetween(startIso: string, endIso: string): number {
  const start = new Date(startIso).getTime();
  const end = new Date(endIso).getTime();
  const minutes = (end - start) / (1000 * 60);
  return Math.round((minutes - 60) / 60 * 100) / 100; // subtract 60 min break for display, rounded 2dp
}
