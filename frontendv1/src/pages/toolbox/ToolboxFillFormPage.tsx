import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { 
  ArrowLeft, 
  ArrowRight, 
  AlertCircle, 
  User, 
  FileText,
  AlertTriangle
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_TOOLBOX_BY_ID, GET_ALL_WORKERS } from '../../graphql/queries';
import { FILL_TOOLBOX } from '../../graphql/mutations';

interface Worker {
  id: number;
  name: string;
}

interface Toolbox {
  id: number;
  status: string;
  emergencyProcedures: string[];
  toolboxTrainingTopics: string[];
  conductorId?: number;
  conductor?: {
    id: number;
    name: string;
  };
  jobs: {
    id: number;
    title: string;
  }[];
}

interface FormData {
  emergencyProcedures: string;
  toolboxTrainingTopics: string;
  conductorId: number;
}

const ToolboxFillFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, toolboxId } = useParams<{ siteId: string; toolboxId: string }>();
  
  const [formData, setFormData] = useState<FormData>({
    emergencyProcedures: '',
    toolboxTrainingTopics: '',
    conductorId: 1 // Default as specified in requirements
  });

  const { data: toolboxData, loading: toolboxLoading, error: toolboxError } = useQuery(GET_TOOLBOX_BY_ID, {
    variables: { id: parseInt(toolboxId || '0') },
    skip: !toolboxId
  });

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [fillToolbox, { loading: submitting }] = useMutation(FILL_TOOLBOX);

  const toolbox: Toolbox | undefined = toolboxData?.toolboxById?.[0];
  const workers: Worker[] = workersData?.allWorkers || [];

  // Pre-fill form with existing data if available
  useEffect(() => {
    if (toolbox) {
      setFormData({
        emergencyProcedures: toolbox.emergencyProcedures.join('\n'),
        toolboxTrainingTopics: toolbox.toolboxTrainingTopics.join('\n'),
        conductorId: toolbox.conductorId || 1
      });
    }
  }, [toolbox]);

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.emergencyProcedures.trim()) {
      toast.error('Emergency procedures are required');
      return false;
    }

    if (!formData.toolboxTrainingTopics.trim()) {
      toast.error('Toolbox training topics are required');
      return false;
    }

    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await fillToolbox({
        variables: {
          input: {
            toolboxId: parseInt(toolboxId || '0'),
            emergencyProcedures: formData.emergencyProcedures.split('\n').filter(line => line.trim()),
            toolboxTrainingTopics: formData.toolboxTrainingTopics.split('\n').filter(line => line.trim()),
            conductorId: formData.conductorId
          }
        }
      });

      toast.success('Toolbox details saved successfully!');
      navigate(`/sites/${siteId}/toolbox/attendance/${toolboxId}`);
    } catch (error) {
      console.error('Error filling toolbox:', error);
      toast.error('Failed to save toolbox details. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Fill Details', path: `/sites/${siteId}/toolbox/fill/${toolboxId}` }
  ];

  if (toolboxLoading || workersLoading) {
    return (
      <FloatingCard title="Fill Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (toolboxError || !toolbox) {
    return (
      <FloatingCard title="Fill Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {toolboxError ? `Error loading toolbox: ${toolboxError.message}` : 'Toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Fill Toolbox Details" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Toolbox Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Toolbox Information</h3>
          <div className="text-sm text-blue-800">
            <p><strong>Status:</strong> {toolbox.status}</p>
            <p><strong>Jobs:</strong> {toolbox.jobs.map(job => job.title).join(', ')}</p>
          </div>
        </div>

        {/* Conductor Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <User className="h-4 w-4 inline mr-1" />
            Conductor *
          </label>
          <select
            value={formData.conductorId}
            onChange={(e) => handleInputChange('conductorId', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          >
            <option value={0}>Select Conductor</option>
            {workers.map(worker => (
              <option key={worker.id} value={worker.id}>
                {worker.name}
              </option>
            ))}
          </select>
        </div>

        {/* Emergency Procedures */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <AlertTriangle className="h-4 w-4 inline mr-1 text-orange-500" />
            Emergency Procedures Specific to the Task of the Day *
          </label>
          <textarea
            value={formData.emergencyProcedures}
            onChange={(e) => handleInputChange('emergencyProcedures', e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter emergency procedures specific to today's tasks... (one per line)"
            required
          />
          <p className="text-sm text-gray-500">
            Enter each procedure on a new line. Be specific to the tasks being performed today.
          </p>
        </div>

        {/* Toolbox Training Topics */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <FileText className="h-4 w-4 inline mr-1" />
            Toolbox Training Topics *
          </label>
          <textarea
            value={formData.toolboxTrainingTopics}
            onChange={(e) => handleInputChange('toolboxTrainingTopics', e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter training topics covered in this toolbox meeting... (one per line)"
            required
          />
          <p className="text-sm text-gray-500">
            Enter each training topic on a new line. Include safety topics, procedures, and any specific training relevant to today's work.
          </p>
        </div>

        {/* Form Validation Info */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Required Information</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Select a conductor for the toolbox meeting</li>
            <li>• Provide emergency procedures specific to today's tasks</li>
            <li>• List all training topics covered in the meeting</li>
            <li>• All fields marked with * are required</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/toolbox/photos/${toolboxId}`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Photos</span>
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <span>Continue to Attendance</span>
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 2 of 3: Fill Details</span>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </form>
    </FloatingCard>
  );
};

export default ToolboxFillFormPage;
