import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { 
  ArrowLeft, 
  Moon, 
  User, 
  Calendar,
  FileText,
  Save,
  AlertCircle
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_ALL_WORKERS } from '../../graphql/queries';
import { FILL_EVENING_TOOLBOX } from '../../graphql/mutations';

interface Worker {
  id: number;
  name: string;
}

interface FormData {
  date: string;
  conductorId: number;
  workCompleted: string;
  workPlanned: string;
  safetyObservations: string;
  incidents: string;
}

const EveningToolboxFillPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const [formData, setFormData] = useState<FormData>({
    date: new Date().toISOString().split('T')[0], // Today's date
    conductorId: 1, // Default as specified in requirements
    workCompleted: '',
    workPlanned: '',
    safetyObservations: '',
    incidents: ''
  });

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [fillEveningToolbox, { loading: submitting }] = useMutation(FILL_EVENING_TOOLBOX);

  const workers: Worker[] = workersData?.allWorkers || [];

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.date) {
      toast.error('Date is required');
      return false;
    }

    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return false;
    }

    if (!formData.workCompleted.trim()) {
      toast.error('Work completed is required');
      return false;
    }

    if (!formData.workPlanned.trim()) {
      toast.error('Work planned is required');
      return false;
    }

    if (!formData.safetyObservations.trim()) {
      toast.error('Safety observations are required');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await fillEveningToolbox({
        variables: {
          input: {
            date: formData.date,
            conductorId: formData.conductorId,
            workCompleted: formData.workCompleted,
            workPlanned: formData.workPlanned,
            safetyObservations: formData.safetyObservations,
            incidents: formData.incidents || null // Optional field
          }
        }
      });

      if (result.data?.fillEveningToolbox) {
        toast.success('Evening toolbox created successfully!');
        navigate(`/sites/${siteId}/toolbox/evening-toolbox`);
      }
    } catch (error) {
      console.error('Error creating evening toolbox:', error);
      toast.error('Failed to create evening toolbox. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Create', path: `/sites/${siteId}/toolbox/evening-toolbox/fill` }
  ];

  if (workersLoading) {
    return (
      <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="bg-indigo-50 p-4 rounded-lg">
          <div className="flex items-center space-x-3">
            <Moon className="h-8 w-8 text-indigo-600" />
            <div>
              <h3 className="text-lg font-semibold text-indigo-900">Evening Toolbox Meeting</h3>
              <p className="text-sm text-indigo-700">Record the day's work summary and safety observations</p>
            </div>
          </div>
        </div>

        {/* Date and Conductor */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date *
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <User className="h-4 w-4 inline mr-1" />
              Conductor *
            </label>
            <select
              value={formData.conductorId}
              onChange={(e) => handleInputChange('conductorId', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            >
              <option value={0}>Select Conductor</option>
              {workers.map(worker => (
                <option key={worker.id} value={worker.id}>
                  {worker.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Work Completed */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <FileText className="h-4 w-4 inline mr-1" />
            Work Completed Today *
          </label>
          <textarea
            value={formData.workCompleted}
            onChange={(e) => handleInputChange('workCompleted', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Describe the work that was completed today..."
            required
          />
          <p className="text-sm text-gray-500">
            Provide a detailed summary of all work activities completed during the day.
          </p>
        </div>

        {/* Work Planned */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <FileText className="h-4 w-4 inline mr-1" />
            Work Planned for Tomorrow *
          </label>
          <textarea
            value={formData.workPlanned}
            onChange={(e) => handleInputChange('workPlanned', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Describe the work planned for tomorrow..."
            required
          />
          <p className="text-sm text-gray-500">
            Outline the work activities and tasks planned for the next working day.
          </p>
        </div>

        {/* Safety Observations */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <AlertCircle className="h-4 w-4 inline mr-1 text-orange-500" />
            Safety Observations *
          </label>
          <textarea
            value={formData.safetyObservations}
            onChange={(e) => handleInputChange('safetyObservations', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Record any safety observations, near misses, or safety improvements..."
            required
          />
          <p className="text-sm text-gray-500">
            Document any safety observations, near misses, hazards identified, or safety improvements made.
          </p>
        </div>

        {/* Incidents */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <AlertCircle className="h-4 w-4 inline mr-1 text-red-500" />
            Incidents (Optional)
          </label>
          <textarea
            value={formData.incidents}
            onChange={(e) => handleInputChange('incidents', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Record any incidents that occurred during the day (leave blank if none)..."
          />
          <p className="text-sm text-gray-500">
            Document any incidents, accidents, or safety events that occurred. Leave blank if no incidents occurred.
          </p>
        </div>

        {/* Form Validation Info */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Required Information</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Select the date for this evening toolbox</li>
            <li>• Choose the conductor who led the meeting</li>
            <li>• Provide a summary of work completed today</li>
            <li>• Outline work planned for tomorrow</li>
            <li>• Record safety observations and any concerns</li>
            <li>• Document incidents only if any occurred</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Create Evening Toolbox</span>
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default EveningToolboxFillPage;
