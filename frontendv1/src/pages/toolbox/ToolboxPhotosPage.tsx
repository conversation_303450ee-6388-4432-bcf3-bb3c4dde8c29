import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { ArrowLeft, ArrowRight, AlertCircle } from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import PhotoCaptureComponent from '../../components/toolbox/PhotoCaptureComponent';
import { GET_TOOLBOX_BY_ID } from '../../graphql/queries';
import { ADD_ATTENDEE_PHOTOS } from '../../graphql/mutations';

interface Toolbox {
  id: number;
  status: string;
  attendeePictureFiles?: {
    id: number;
    fileName: string;
    url: string;
  }[];
  jobs: {
    id: number;
    title: string;
  }[];
}

const ToolboxPhotosPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, toolboxId } = useParams<{ siteId: string; toolboxId: string }>();
  const [capturedPhotos, setCapturedPhotos] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);

  const { data, loading, error } = useQuery(GET_TOOLBOX_BY_ID, {
    variables: { id: parseInt(toolboxId || '0') },
    skip: !toolboxId
  });

  const [addAttendeePhotos] = useMutation(ADD_ATTENDEE_PHOTOS);

  const toolbox: Toolbox | undefined = data?.toolboxById?.[0];

  const handlePhotoCapture = (file: File) => {
    setCapturedPhotos(prev => [...prev, file]);
  };

  const handlePhotoRemove = (index: number) => {
    setCapturedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (capturedPhotos.length === 0) {
      toast.error('Please capture at least one photo before proceeding');
      return;
    }

    setUploading(true);
    try {
      await addAttendeePhotos({
        variables: {
          input: {
            toolboxId: parseInt(toolboxId || '0'),
            photoFiles: capturedPhotos,
            startedById: 1 // Default as specified in requirements
          }
        }
      });

      toast.success('Photos uploaded successfully!');
      navigate(`/sites/${siteId}/toolbox/fill/${toolboxId}`);
    } catch (error) {
      console.error('Error uploading photos:', error);
      toast.error('Failed to upload photos. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleSkip = () => {
    navigate(`/sites/${siteId}/toolbox/fill/${toolboxId}`);
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Add Photos', path: `/sites/${siteId}/toolbox/photos/${toolboxId}` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Add Attendance Photos" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !toolbox) {
    return (
      <FloatingCard title="Add Attendance Photos" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {error ? `Error loading toolbox: ${error.message}` : 'Toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Add Attendance Photos" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Toolbox Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Toolbox Information</h3>
          <div className="text-sm text-blue-800">
            <p><strong>Status:</strong> {toolbox.status}</p>
            <p><strong>Jobs:</strong> {toolbox.jobs.map(job => job.title).join(', ')}</p>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Instructions</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Take photos of all attendees for the toolbox meeting</li>
            <li>• Ensure all faces are clearly visible</li>
            <li>• You can take multiple photos if needed</li>
            <li>• Photos will be used for attendance verification</li>
          </ul>
        </div>

        {/* Existing Photos */}
        {toolbox.attendeePictureFiles && toolbox.attendeePictureFiles.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-700">Existing Photos</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {toolbox.attendeePictureFiles.map((photo) => (
                <div key={photo.id} className="relative">
                  <img
                    src={photo.url}
                    alt={photo.fileName}
                    className="w-full h-32 object-cover rounded-lg border border-gray-200"
                  />
                  <div className="absolute bottom-2 left-2 right-2">
                    <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {photo.fileName}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Photo Capture Component */}
        <PhotoCaptureComponent
          onPhotoCapture={handlePhotoCapture}
          onPhotoRemove={handlePhotoRemove}
          capturedPhotos={capturedPhotos}
          maxPhotos={20}
        />

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          <div className="flex space-x-3">
            <button
              onClick={handleSkip}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Skip Photos
            </button>

            <button
              onClick={handleSubmit}
              disabled={capturedPhotos.length === 0 || uploading}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Uploading...</span>
                </>
              ) : (
                <>
                  <span>Continue</span>
                  <ArrowRight className="h-4 w-4" />
                </>
              )}
            </button>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 1 of 3: Add Photos</span>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ToolboxPhotosPage;
