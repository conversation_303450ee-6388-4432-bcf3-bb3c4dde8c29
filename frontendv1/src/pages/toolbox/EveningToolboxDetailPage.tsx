import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { 
  ArrowLeft, 
  AlertCircle, 
  Calendar,
  User,
  FileText,
  Moon,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_EVENING_TOOLBOX_BY_ID } from '../../graphql/queries';

interface EveningToolbox {
  id: number;
  date: string;
  workCompleted: string;
  workPlanned: string;
  safetyObservations: string;
  incidents?: string;
  conductor?: {
    id: number;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

const EveningToolboxDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, eveningToolboxId } = useParams<{ siteId: string; eveningToolboxId: string }>();

  const { data, loading, error } = useQuery(GET_EVENING_TOOLBOX_BY_ID, {
    variables: { id: parseInt(eveningToolboxId || '0') },
    skip: !eveningToolboxId
  });

  const eveningToolbox: EveningToolbox | undefined = data?.eveningToolboxById?.[0];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Details', path: `/sites/${siteId}/toolbox/evening-toolbox/${eveningToolboxId}` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !eveningToolbox) {
    return (
      <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {error ? `Error loading evening toolbox: ${error.message}` : 'Evening toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header Info */}
        <div className="bg-indigo-50 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Moon className="h-8 w-8 text-indigo-600" />
              <h2 className="text-2xl font-bold text-indigo-900">Evening Toolbox #{eveningToolbox.id}</h2>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-indigo-800">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span><strong>Date:</strong> {formatDate(eveningToolbox.date)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span><strong>Created:</strong> {formatDateTime(eveningToolbox.createdAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span><strong>Updated:</strong> {formatDateTime(eveningToolbox.updatedAt)}</span>
            </div>
          </div>
        </div>

        {/* Conductor Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Conductor
          </h3>
          {eveningToolbox.conductor ? (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{eveningToolbox.conductor.name}</p>
                <p className="text-sm text-gray-500">Evening Toolbox Conductor</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No conductor assigned</p>
          )}
        </div>

        {/* Work Completed */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Work Completed Today
          </h3>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-gray-700 whitespace-pre-wrap">{eveningToolbox.workCompleted}</p>
          </div>
        </div>

        {/* Work Planned */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            Work Planned for Tomorrow
          </h3>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-gray-700 whitespace-pre-wrap">{eveningToolbox.workPlanned}</p>
          </div>
        </div>

        {/* Safety Observations */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
            Safety Observations
          </h3>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <p className="text-gray-700 whitespace-pre-wrap">{eveningToolbox.safetyObservations}</p>
          </div>
        </div>

        {/* Incidents */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            {eveningToolbox.incidents ? (
              <XCircle className="h-5 w-5 mr-2 text-red-500" />
            ) : (
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
            )}
            Incidents
          </h3>
          {eveningToolbox.incidents ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-gray-700 whitespace-pre-wrap">{eveningToolbox.incidents}</p>
            </div>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <p className="text-green-800 font-medium">No incidents reported</p>
              </div>
              <p className="text-green-700 text-sm mt-1">
                No incidents, accidents, or safety events occurred during this period.
              </p>
            </div>
          )}
        </div>

        {/* Summary Statistics */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-gray-900">Work Status</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Work completed and planned documented
              </p>
            </div>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                <span className="font-medium text-gray-900">Safety</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Safety observations recorded
              </p>
            </div>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                {eveningToolbox.incidents ? (
                  <XCircle className="h-5 w-5 text-red-500" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                )}
                <span className="font-medium text-gray-900">Incidents</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {eveningToolbox.incidents ? 'Incidents reported' : 'No incidents'}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Evening Toolbox</span>
          </button>

          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/fill`)}
            className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            Create New Evening Toolbox
          </button>
        </div>
      </div>
    </FloatingCard>
  );
};

export default EveningToolboxDetailPage;
