import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { 
  ArrowLeft, 
  AlertCircle, 
  Calendar,
  User,
  Users,
  FileText,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Clock
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_TOOLBOX_BY_ID } from '../../graphql/queries';

interface Toolbox {
  id: number;
  status: string;
  emergencyProcedures: string[];
  toolboxTrainingTopics: string[];
  conductor?: {
    id: number;
    name: string;
  };
  attendees?: {
    id: number;
    name: string;
    company: string;
  }[];
  attendeePictureFiles?: {
    id: number;
    fileName: string;
    url: string;
  }[];
  jobs: {
    id: number;
    title: string;
    description?: string;
    location?: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

const ToolboxDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, toolboxId } = useParams<{ siteId: string; toolboxId: string }>();

  const { data, loading, error } = useQuery(GET_TOOLBOX_BY_ID, {
    variables: { id: parseInt(toolboxId || '0') },
    skip: !toolboxId
  });

  const toolbox: Toolbox | undefined = data?.toolboxById?.[0];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CLOSED':
        return 'bg-green-100 text-green-800';
      case 'PENDING_ATTENDANCE':
        return 'bg-yellow-100 text-yellow-800';
      case 'STARTED':
        return 'bg-blue-100 text-blue-800';
      case 'DRAFTED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Details', path: `/sites/${siteId}/toolbox/details/${toolboxId}` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !toolbox) {
    return (
      <FloatingCard title="Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {error ? `Error loading toolbox: ${error.message}` : 'Toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Toolbox Details" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header Info */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-blue-900">Toolbox #{toolbox.id}</h2>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(toolbox.status)}`}>
              {toolbox.status}
            </span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span><strong>Created:</strong> {formatDate(toolbox.createdAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span><strong>Updated:</strong> {formatDate(toolbox.updatedAt)}</span>
            </div>
          </div>
        </div>

        {/* Conductor Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Conductor
          </h3>
          {toolbox.conductor ? (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{toolbox.conductor.name}</p>
                <p className="text-sm text-gray-500">Toolbox Conductor</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No conductor assigned</p>
          )}
        </div>

        {/* Jobs Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Associated Jobs ({toolbox.jobs.length})
          </h3>
          {toolbox.jobs.length > 0 ? (
            <div className="space-y-3">
              {toolbox.jobs.map((job) => (
                <div key={job.id} className="border border-gray-100 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900">{job.title}</h4>
                  {job.description && (
                    <p className="text-sm text-gray-600 mt-1">{job.description}</p>
                  )}
                  {job.location && (
                    <div className="flex items-center space-x-1 mt-2">
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{job.location}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No jobs associated with this toolbox</p>
          )}
        </div>

        {/* Emergency Procedures */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
            Emergency Procedures
          </h3>
          {toolbox.emergencyProcedures.length > 0 ? (
            <ul className="space-y-2">
              {toolbox.emergencyProcedures.map((procedure, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{procedure}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No emergency procedures specified</p>
          )}
        </div>

        {/* Training Topics */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Training Topics
          </h3>
          {toolbox.toolboxTrainingTopics.length > 0 ? (
            <ul className="space-y-2">
              {toolbox.toolboxTrainingTopics.map((topic, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" />
                  <span className="text-gray-700">{topic}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No training topics specified</p>
          )}
        </div>

        {/* Attendees */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Attendees ({toolbox.attendees?.length || 0})
          </h3>
          {toolbox.attendees && toolbox.attendees.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {toolbox.attendees.map((attendee) => (
                <div key={attendee.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{attendee.name}</p>
                    <p className="text-sm text-gray-500 truncate">{attendee.company}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No attendees recorded</p>
          )}
        </div>

        {/* Attendance Photos */}
        {toolbox.attendeePictureFiles && toolbox.attendeePictureFiles.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Attendance Photos ({toolbox.attendeePictureFiles.length})</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {toolbox.attendeePictureFiles.map((photo) => (
                <div key={photo.id} className="relative">
                  <img
                    src={photo.url}
                    alt={photo.fileName}
                    className="w-full h-32 object-cover rounded-lg border border-gray-200"
                  />
                  <div className="absolute bottom-2 left-2 right-2">
                    <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {photo.fileName}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          {toolbox.status !== 'CLOSED' && (
            <button
              onClick={() => {
                if (toolbox.status === 'DRAFTED') {
                  navigate(`/sites/${siteId}/toolbox/photos/${toolboxId}`);
                } else if (toolbox.status === 'STARTED') {
                  navigate(`/sites/${siteId}/toolbox/fill/${toolboxId}`);
                } else if (toolbox.status === 'PENDING_ATTENDANCE') {
                  navigate(`/sites/${siteId}/toolbox/attendance/${toolboxId}`);
                }
              }}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Continue Toolbox
            </button>
          )}
        </div>
      </div>
    </FloatingCard>
  );
};

export default ToolboxDetailsPage;
