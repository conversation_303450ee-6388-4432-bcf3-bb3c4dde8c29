import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  Calendar,
  FileText,
  Grid,
  Bell,
  Shield,
  History
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import TrainingDashboard from '../components/training/TrainingDashboard';
import WorkerTrainingStatus from '../components/training/WorkerTrainingStatus';
import TrainingCalendar from '../components/training/TrainingCalendar';
import TrainingReports from '../components/training/TrainingReports';
import TrainingExpirationAlerts from '../components/training/TrainingExpirationAlerts';
import TaskEligibilityGating from '../components/training/TaskEligibilityGating';
import AuditTrailVisualization from '../components/training/AuditTrailVisualization';
import TrainingMatrix from "../components/training/TrainingMatrix";
import TrainingLibrary from "../components/training/TrainingLibrary";


const TrainingPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

  const validTabs = ['dashboard', 'worker-status', 'calendar', 'alerts', 'eligibility', 'audit', 'reports', 'matrix'];

	// Handle URL hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("dashboard");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};



  const tabs: Tab[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="h-4 w-4" />,
      content: <TrainingDashboard siteId={siteId || ''} onNavigateToTab={handleNavigateToTab} />
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: <Bell className="h-4 w-4" />,
      content: <TrainingExpirationAlerts siteId={siteId || ''} showSettings={true} />
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: <Calendar className="h-4 w-4" />,
      content: <TrainingCalendar siteId={siteId || ''} />
    },
    {
      id: 'library',
      label: 'Library',
      icon: <Grid className="h-4 w-4" />,
      content: <TrainingLibrary siteId={siteId || ""} />
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <FileText className="h-4 w-4" />,
      content: <TrainingReports siteId={siteId || ''} />
    }
  ];

	return (
		<FloatingCard>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default TrainingPage;
