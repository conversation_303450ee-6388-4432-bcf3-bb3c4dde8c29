import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "react-router-dom";
import {
	LayoutDashboard,
	Clock,
	History,
	FileText,
	BarChart3,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import InspectionsDashboard from "../components/inspections/InspectionsDashboard";
import ScheduledInspections from "../components/inspections/ScheduledInspections";
import InspectionHistory from "../components/inspections/InspectionHistory";
import InspectionForms from "../components/inspections/InspectionForms";
import InspectionReports from "../components/inspections/InspectionReports";
import { SiteInfo } from "../types";

// Mock site data - replace with actual data fetching
const mockSite: SiteInfo = {
	id: "1",
	name: "Westlands Construction Site",
	location: "Westlands, Nairobi",
	projectManager: "<PERSON>",
	status: "active",
	startDate: new Date("2024-01-15"),
	endDate: new Date("2024-12-30"),
	progress: 65,
	workforce: 45,
	safetyScore: 92,
	tenantId: "",
	healthStatus: "green",
	workersOnSite: 0,
	activePermits: 0,
	openIncidents: 0,
	createdAt: new Date('2024-09-01T00:00:00Z')
};

const InspectionsPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [site] = useState<SiteInfo>(mockSite);
	const [activeTab, setActiveTab] = useState("dashboard");

	// Mock user role - replace with actual user context
	const userRole = "inspector"; // Could be 'admin', 'supervisor', 'inspector', etc.

	const validTabs = [
		"dashboard",
		"pending",
		"history",
		"forms",
		"reports",
	];

	// Handle URL hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("dashboard");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Inspections", path: `/sites/${siteId}/inspections` },
	];

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: (
				<InspectionsDashboard
					siteId={siteId || ""}
					onNavigateToTab={handleNavigateToTab}
				/>
			),
		},
		{
			id: "pending",
			label: "Pending",
			icon: <Clock className="h-4 w-4" />,
			content: <ScheduledInspections siteId={siteId || ""} />,
		},
		{
			id: "history",
			label: "History",
			icon: <History className="h-4 w-4" />,
			content: <InspectionHistory siteId={siteId || ""} />,
		},
		{
			id: "forms",
			label: "Forms",
			icon: <FileText className="h-4 w-4" />,
			content: <InspectionForms siteId={siteId || ""} />,
		},
		{
			id: "reports",
			label: "Reports",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <InspectionReports siteId={siteId || ""} />,
		},
	];

	return (
		<FloatingCard title="Inspections" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default InspectionsPage;
