import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import {
  Bell,
  Search,
  Check,
  Clock,
  User,
  CheckCircle,
  Calendar,
  AlertTriangle,
  ExternalLink,
  Star,
  Loader2,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useNotifications } from '../hooks/useNotifications';
import {
  AppNotification,
  NotificationPriority
} from '../types/notifications';
import FloatingCard from '../components/layout/FloatingCard';

// Extended notification interface for page-specific features
interface ExtendedNotification extends AppNotification {
  isStarred?: boolean;
  category: 'training' | 'workforce' | 'safety' | 'system';
}

const NotificationsPage: React.FC = () => {
  // Use the real notification hook
  const {
    notifications: rawNotifications,
    loading,
    error,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refetch
  } = useNotifications({
    take: 100, // Get more notifications for the full page
    enableRealTime: true,
    autoRefresh: true
  });

  const [selected, setSelected] = useState<'all' | 'unread' | 'starred' | 'training' | 'workforce' | 'safety' | 'system'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'priority'>('newest');
  const [starredNotifications, setStarredNotifications] = useState<Set<number>>(new Set());

  // Utility functions
  // const parseMetadata = (notification: Notification): any => {
  //   if (!notification.metadata) return {};

  //   try {
  //     return JSON.parse(notification.metadata);
  //   } catch {
  //     return {};
  //   }
  // };

  const getNotificationCategory = (type: string): 'training' | 'workforce' | 'safety' | 'system' => {
    if (type.includes('training')) return 'training';
    if (type.includes('worker') || type.includes('overtime') || type.includes('attendance')) return 'workforce';
    if (type.includes('safety') || type.includes('permit') || type.includes('incident')) return 'safety';
    return 'system';
  };

  const toggleStar = (notificationId: number) => {
    setStarredNotifications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId);
      } else {
        newSet.add(notificationId);
      }
      return newSet;
    });
  };

  // Transform raw notifications to extended notifications
  const notifications: ExtendedNotification[] = useMemo(() => {
    return rawNotifications.map(notification => ({
      ...notification,
      isStarred: starredNotifications.has(notification.id),
      category: getNotificationCategory(notification.type)
    }));
  }, [rawNotifications, starredNotifications]);

  // Filter and sort notifications
  const filteredNotifications = useMemo(() => {
    let filtered = [...notifications];

    // Apply filter
    switch (selected) {
      case 'unread':
        filtered = filtered.filter(n => !n.readAt);
        break;
      case 'starred':
        filtered = filtered.filter(n => n.isStarred);
        break;
      case 'training':
      case 'workforce':
      case 'safety':
      case 'system':
        filtered = filtered.filter(n => n.category === selected);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    // Apply search
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(term) ||
        n.message.toLowerCase().includes(term) ||
        n.type.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'priority':
          const priorityOrder: Record<NotificationPriority, number> = {
            [NotificationPriority.CRITICAL]: 4,
            [NotificationPriority.HIGH]: 3,
            [NotificationPriority.MEDIUM]: 2,
            [NotificationPriority.LOW]: 1
          };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        default: // newest
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    return filtered;
  }, [notifications, selected, searchTerm, sortBy]);



  const getNotificationIcon = (type: string) => {
    const iconClass = "h-5 w-5";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconClass} />;
      case 'training_expired':
        return <AlertTriangle className={iconClass} />;
      case 'training_assigned':
      case 'training_completed':
      case 'worker_added':
        return <User className={iconClass} />;
      case 'system_alert':
        return <AlertCircle className={iconClass} />;
      case 'permit_expiring':
      case 'permit_expired':
        return <CheckCircle className={iconClass} />;
      case 'safety_incident':
        return <AlertTriangle className={iconClass} />;
      case 'reminder':
        return <Calendar className={iconClass} />;
      default:
        return <Bell className={iconClass} />;
    }
  };

  const getPriorityStyles = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return {
          icon: 'text-red-600 bg-red-100',
          border: 'border-l-red-500',
          badge: 'bg-red-100 text-red-800'
        };
      case NotificationPriority.HIGH:
        return {
          icon: 'text-orange-600 bg-orange-100',
          border: 'border-l-orange-500',
          badge: 'bg-orange-100 text-orange-800'
        };
      case NotificationPriority.MEDIUM:
        return {
          icon: 'text-yellow-600 bg-yellow-100',
          border: 'border-l-yellow-500',
          badge: 'bg-yellow-100 text-yellow-800'
        };
      case NotificationPriority.LOW:
        return {
          icon: 'text-blue-600 bg-blue-100',
          border: 'border-l-blue-500',
          badge: 'bg-blue-100 text-blue-800'
        };
      default:
        return {
          icon: 'text-gray-600 bg-gray-100',
          border: 'border-l-gray-500',
          badge: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'training': return <User className="h-4 w-4" />;
      case 'workforce': return <Clock className="h-4 w-4" />;
      case 'safety': return <AlertTriangle className="h-4 w-4" />;
      case 'system': return <Bell className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const handleMarkAsRead = async (notificationId: number) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getCount = (filter: string) => {
    switch (filter) {
      case 'unread': return notifications.filter(n => !n.readAt).length;
      case 'starred': return notifications.filter(n => n.isStarred).length;
      case 'training': return notifications.filter(n => n.category === 'training').length;
      case 'workforce': return notifications.filter(n => n.category === 'workforce').length;
      case 'safety': return notifications.filter(n => n.category === 'safety').length;
      case 'system': return notifications.filter(n => n.category === 'system').length;
      default: return notifications.length;
    }
  };

  return (
    <FloatingCard title="Notifications">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-gray-600">Stay updated with important alerts and updates</p>
          </div>
          <div className="flex items-center space-x-3">
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors"
              >
                <Check className="h-4 w-4 mr-2" />
                Mark all read
              </button>
            )}
            <Link
              to="/notifications/preferences"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Preferences
            </Link>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          {[
            { label: 'Total', count: notifications.length, color: 'bg-blue-100 text-blue-800' },
            { label: 'Unread', count: getCount('unread'), color: 'bg-red-100 text-red-800' },
            { label: 'Starred', count: getCount('starred'), color: 'bg-yellow-100 text-yellow-800' },
            { label: 'High Priority', count: notifications.filter(n => n.priority === NotificationPriority.HIGH || n.priority === NotificationPriority.CRITICAL).length, color: 'bg-orange-100 text-orange-800' }
          ].map((stat) => (
            <div key={stat.label} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">{stat.label}</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${stat.color}`}>
                  {stat.count}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Sidebars */}
        <div className="col-span-3">
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <h3 className="text-sm font-semibold text-gray-900 mb-4">s</h3>

            {/* Search */}
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>

            {/*Options */}
            <div className="space-y-1">
              {[
                { key: 'all', label: 'All Notifications', icon: Bell },
                { key: 'unread', label: 'Unread', icon: Bell },
                { key: 'starred', label: 'Starred', icon: Star },
                { key: 'training', label: 'Training', icon: User },
                { key: 'workforce', label: 'Workforce', icon: Clock },
                { key: 'safety', label: 'Safety', icon: AlertTriangle },
                { key: 'system', label: 'System', icon: Bell }
              ].map((filter) => {
                const IconComponent = filter.icon;
                const count = getCount(filter.key);
                return (
                  <button
                    key={filter.key}
                    onClick={() => setSelected(filter.key as any)}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${selected === filter.key
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-700 hover:bg-gray-100'
                      }`}
                  >
                    <div className="flex items-center space-x-2">
                      <IconComponent className="h-4 w-4" />
                      <span>{filter.label}</span>
                    </div>
                    {count > 0 && (
                      <span className="text-xs bg-gray-200 text-gray-600 px-2 py-0.5 rounded-full">
                        {count}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Sort Options */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="text-xs font-semibold text-gray-900 mb-2">Sort by</h4>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="newest">Newest first</option>
                <option value="oldest">Oldest first</option>
                <option value="priority">Priority</option>
              </select>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="col-span-9">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
            {loading ? (
              <div className="p-12 text-center">
                <Loader2 className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Loading notifications...</h3>
                <p className="text-gray-500">Please wait while we fetch your notifications</p>
              </div>
            ) : error ? (
              <div className="p-12 text-center">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load notifications</h3>
                <p className="text-gray-500 mb-4">There was an error loading your notifications</p>
                <button
                  onClick={() => refetch()}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                >
                  Try again
                </button>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="p-12 text-center">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search terms' : 'You\'re all caught up!'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {filteredNotifications.map((notification) => {
                  const priorityStyles = getPriorityStyles(notification.priority);
                  return (
                    <div
                      key={notification.id}
                      className={`relative p-6 border-l-4 ${priorityStyles.border} hover:bg-gray-50 transition-colors ${!notification.readAt ? 'bg-blue-50/30' : 'bg-white'
                        }`}
                    >
                      {/* Unread indicator */}
                      {!notification.readAt && (
                        <div className="absolute top-6 right-6">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        </div>
                      )}

                      <div className="flex items-start space-x-4">
                        {/* Icon */}
                        <div className={`flex-shrink-0 p-3 rounded-xl ${priorityStyles.icon}`}>
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          {/* Header */}
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                {notification.title}
                              </h3>
                              <div className="flex items-center space-x-3 mb-2">
                                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${priorityStyles.badge}`}>
                                  {notification.priority.charAt(0).toUpperCase() + notification.priority.slice(1)}
                                </span>
                                <span className="inline-flex items-center text-xs text-gray-500">
                                  {getCategoryIcon(notification.category)}
                                  <span className="ml-1 capitalize">{notification.category}</span>
                                </span>
                                <span className="text-xs text-gray-500">
                                  {formatTimestamp(notification.createdAt)}
                                </span>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => toggleStar(notification.id)}
                                className={`p-2 rounded-lg transition-colors ${notification.isStarred
                                  ? 'text-yellow-500 hover:text-yellow-600'
                                  : 'text-gray-400 hover:text-gray-600'
                                  }`}
                                title={notification.isStarred ? 'Remove star' : 'Add star'}
                              >
                                <Star className={`h-4 w-4 ${notification.isStarred ? 'fill-current' : ''}`} />
                              </button>
                              {!notification.readAt && (
                                <button
                                  onClick={() => handleMarkAsRead(notification.id)}
                                  className="p-2 rounded-lg text-gray-400 hover:text-gray-600 transition-colors"
                                  title="Mark as read"
                                >
                                  <Check className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </div>

                          {/* Message */}
                          <p className="text-gray-700 mb-4 leading-relaxed">
                            {notification.message}
                          </p>

                          {/* Metadata */}
                          {notification.metadata && (
                            <div className="bg-gray-50 rounded-lg p-3 mb-4">
                              <div className="grid grid-cols-2 gap-3 text-sm">
                                {notification.metadata.workerName && (
                                  <div>
                                    <span className="font-medium text-gray-600">Worker:</span>
                                    <span className="ml-2 text-gray-900">{notification.metadata.workerName}</span>
                                  </div>
                                )}
                                {notification.metadata.trainingName && (
                                  <div>
                                    <span className="font-medium text-gray-600">Training:</span>
                                    <span className="ml-2 text-gray-900">{notification.metadata.trainingName}</span>
                                  </div>
                                )}
                                {notification.metadata.siteName && (
                                  <div>
                                    <span className="font-medium text-gray-600">Site:</span>
                                    <span className="ml-2 text-gray-900">{notification.metadata.siteName}</span>
                                  </div>
                                )}
                                {notification.metadata.requestId && (
                                  <div>
                                    <span className="font-medium text-gray-600">Request ID:</span>
                                    <span className="ml-2 text-gray-900">{notification.metadata.requestId}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Actions */}
                          {notification.actionUrl && (
                            <div className="flex items-center space-x-3">
                              <Link
                                to={notification.actionUrl}
                                onClick={() => markAsRead(notification.id)}
                                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                              >
                                {notification.actionLabel || 'View Details'}
                                <ExternalLink className="h-4 w-4 ml-2" />
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default NotificationsPage;
