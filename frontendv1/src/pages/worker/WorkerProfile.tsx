import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import FloatingCard from '../../components/layout/FloatingCard';
import VSCodeInterface, { VSCodeTab } from '../../components/shared/VSCodeInterface';
import createWorkerExplorerItems from '../../components/workers/WorkerExplorer';
import WorkerTabs from '../../components/workers/WorkerTabs';
import { SiteInfo, TimeLog } from '../../types';
import { mockCompanyWorkers, CompanyWorker } from '../../data/workers';

const mockSite: SiteInfo = { id: 'site1', name: 'Westlands Construction Site', healthStatus: 'green', workersOnSite: 42, activePermits: 8, openIncidents: 0, projectManager: '<PERSON>', location: 'Waiyaki Way, Westlands, Nairobi', timeline: 'Jan 2025 - Dec 2026', currentPhase: 'Foundation', progressPercentage: 25, tenantId: '', status: 'active', createdAt: new Date() };

const mockTimeLogs: TimeLog[] = [
  { id: 'log1', workerId: 'w1', workerName: '<PERSON> <PERSON>mau', workerTrade: 'Carpenter', date: '2025-05-30', clockIn: '07:55', clockOut: '17:05', breakDuration: 60, totalHours: 8, overtime: 0, status: 'on-site', toolboxTalkAttended: true, isVerifiedByHikvision: true, hikvisionPersonId: 'hik_person_w1', terminalId: 'term1' },
];

const WorkerProfile = () => {
  const navigate = useNavigate();
  const { siteId, workerId } = useParams<{ siteId: string; workerId: string }>();
  const [site] = useState<SiteInfo>(mockSite);
  const [timeLogs] = useState<TimeLog[]>(mockTimeLogs);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<VSCodeTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  const workerId_num = parseInt(workerId || '1');
  const worker = mockCompanyWorkers.find(w => w.id === workerId_num) || null;

  useEffect(() => {
    if (worker) {
      const aboutTab: VSCodeTab = { id: 'worker-about', title: 'About', type: 'details', data: { type: 'about', worker }, closable: false };
      setOpenTabs([aboutTab]);
      setActiveTabId('worker-about');
      setSelectedItem('worker-about');
    }
  }, [worker]);

  if (!worker) {
    return (
      <FloatingCard title="Error Loading Worker" breadcrumbs={[]}> 
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">Worker not found</p>
          <button onClick={() => navigate(-1)} className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Go Back</button>
        </div>
      </FloatingCard>
    );
  }

  const breadcrumbs = [ { name: 'Dashboard', path: '/' }, { name: site.name, path: `/sites/${siteId}/dashboard` }, { name: 'Workers', path: `/sites/${siteId}/workers` }, { name: worker?.name || 'Worker', path: `/sites/${siteId}/workers/${workerId}` } ];

  const explorerItems = createWorkerExplorerItems(worker);
  const { renderTabContent } = WorkerTabs({ worker, timeLogs, siteId, onPhotoUpload: async () => '', onPhotoDelete: async () => true });

  const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
    setSelectedItem(itemId);
    const existingTab = openTabs.find(tab => tab.id === itemId);
    if (!existingTab) {
      const title = itemId === 'worker-about' ? 'About' : itemId.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
      const newTab: VSCodeTab = { id: itemId, title, type: itemData?.type || itemType, data: itemData, closable: itemId !== 'worker-about' };
      setOpenTabs(prev => [...prev, newTab]);
    }
    setActiveTabId(itemId);
  };
  const handleTabClose = (tabId: string) => { if (tabId === 'worker-about') return; setOpenTabs(prev => prev.filter(tab => tab.id !== tabId)); if (activeTabId === tabId) { const remaining = openTabs.filter(tab => tab.id !== tabId); setActiveTabId(remaining.length > 0 ? remaining[remaining.length - 1].id : null); } };
  const handleTabChange = (tabId: string) => setActiveTabId(tabId);

  return (
    <FloatingCard
      title={`${worker?.name || 'Worker'} - Profile`}
      breadcrumbs={breadcrumbs}
      layout="custom"
      topBarShowBack
      topBarOnBack={() => navigate(-1)}
    >
      <div className="h-full flex flex-col min-w-0">
        <div className="flex-1 min-h-0">
          <VSCodeInterface
            explorerItems={explorerItems}
            tabs={openTabs}
            activeTabId={activeTabId}
            selectedItem={selectedItem}
            onItemSelect={handleItemSelect}
            onTabChange={handleTabChange}
            onTabClose={handleTabClose}
            renderTabContent={renderTabContent}
            hideExplorer={true}
          />
        </div>
      </div>
    </FloatingCard>
  );
};

export default WorkerProfile;
