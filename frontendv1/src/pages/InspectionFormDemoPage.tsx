import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ClipboardCheck, FileText, Eye } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { inspectionFormTypes } from '../data/inspectionFormTemplate';

const InspectionFormDemoPage: React.FC = () => {
  const navigate = useNavigate();

  const handleFormSelect = (formId: string) => {
    navigate(`/inspections/form/${formId}`);
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <FloatingCard title="Inspection Form Demo">
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <ClipboardCheck className="h-6 w-6 text-green-600" />
                <h1 className="text-2xl font-bold text-gray-900">Inspection Forms Demo</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-6 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            {/* Introduction */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="flex justify-center mb-4">
                <ClipboardCheck className="h-16 w-16 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Equipment Inspection Forms
              </h2>
              <p className="text-gray-600 mb-8 text-lg">
                Select an equipment type below to access its inspection form. Each form contains specific 
                safety and operational checks required for that equipment.
              </p>
            </div>

            {/* Available Forms */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Available Inspection Forms</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {inspectionFormTypes.map((form) => (
                  <div
                    key={form.id}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer group"
                    onClick={() => handleFormSelect(form.id)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                          {form.name}
                        </h4>
                        <p className="text-sm text-gray-500 mt-1">
                          Form ID: {form.id}
                        </p>
                      </div>
                      <FileText className="h-6 w-6 text-gray-400 group-hover:text-green-600 transition-colors" />
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <ClipboardCheck className="h-4 w-4 mr-2" />
                        {form.information.length} inspection points
                      </div>
                    </div>

                    <button
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 group-hover:bg-green-700 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFormSelect(form.id);
                      }}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Open Form
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Form Features */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Form Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">YES/NO Responses</h4>
                      <p className="text-sm text-gray-600">Radio button selection for each inspection point</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Remarks Section</h4>
                      <p className="text-sm text-gray-600">Text area for detailed comments on each item</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Approval Section</h4>
                      <p className="text-sm text-gray-600">Overall approval status with remarks</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Inspector Details</h4>
                      <p className="text-sm text-gray-600">Name, date, time, and signature capture</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">General Comments</h4>
                      <p className="text-sm text-gray-600">Overall observations and recommendations</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <ClipboardCheck className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Responsive Design</h4>
                      <p className="text-sm text-gray-600">Works on mobile and desktop devices</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Usage Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start">
                <FileText className="h-6 w-6 text-blue-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-blue-800 mb-2">
                    How to Use the Inspection Forms
                  </h3>
                  <div className="text-sm text-blue-700 space-y-2">
                    <p>1. Select an equipment type from the list above</p>
                    <p>2. Review each inspection point and select YES or NO</p>
                    <p>3. Add remarks for any items that need attention</p>
                    <p>4. Fill in the approval section and general comments</p>
                    <p>5. Enter inspector details (name, date, time, signature)</p>
                    <p>6. Submit the form to save the inspection results</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Technical Details */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Technical Implementation
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Built with React and TypeScript for type safety</p>
                <p>• Uses URL parameters to load specific forms by ID</p>
                <p>• Data sourced from inspectionFormTemplate.ts</p>
                <p>• Responsive table design with proper form controls</p>
                <p>• Form state management with React hooks</p>
                <p>• Follows the Vue template structure you provided</p>
                <p>• Ready for backend integration and data persistence</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default InspectionFormDemoPage;
