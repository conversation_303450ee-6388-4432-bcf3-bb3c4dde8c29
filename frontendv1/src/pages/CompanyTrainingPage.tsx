import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import {
  LayoutDashboard,
  GraduationCap,
  CalendarDays,
  BarChart3
} from "lucide-react";
import TrainingDashboard from "../components/training/TrainingDashboard";
import TrainingProgramMaster from "../components/training/TrainingProgramMaster";
import TrainingCalendar from "../components/training/TrainingCalendar";
 

export default function CompanyTrainingPage() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("dashboard");

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get("tab");
    if (tab) setActiveTab(tab);
  }, [location]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const url = new URL(window.location.href);
    url.searchParams.set("tab", tabId);
    window.history.pushState({}, "", url.toString());
  };

  const tabs: Tab[] = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <LayoutDashboard className="h-4 w-4" />,
      content: <TrainingDashboard siteId={"company"} onNavigateToTab={handleTabChange} />
    },
    {
      id: "programs",
      label: "Programs",
      icon: <GraduationCap className="h-4 w-4" />,
      content: <TrainingProgramMaster />
    },
    {
      id: "sessions",
      label: "Sessions",
      icon: <CalendarDays className="h-4 w-4" />,
      content: <TrainingCalendar siteId={"company"} />
    },
    
    {
      id: "reports",
      label: "Reports",
      icon: <BarChart3 className="h-4 w-4" />,
      content: (
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <BarChart3 className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Training Reports</h3>
          <p className="text-gray-600">Cross-site training compliance and analytics</p>
        </div>
      )
    }
  ];

  return (
    <FloatingCard>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Company Training Management</h1>
        <p className="text-gray-600 mt-1">
          Register company training programs, Schedule sessions, and Track documents.
        </p>
      </div>

      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        variant="default"
      />
    </FloatingCard>
  );
}




