import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect, useMemo } from 'react';
import FloatingCard from '../../components/layout/FloatingCard';
import VSCodeInterface, { VSCodeTab } from '../../components/shared/VSCodeInterface';
import createEquipmentExplorerItems from '../../components/equipment/EquipmentExplorer';
import EquipmentTabs from '../../components/equipment/EquipmentTabs';
import { CompanyEquipment, mockCompanyEquipment } from '../../data/equipmentMockData';

const EquipmentProfile = () => {
  const navigate = useNavigate();
  const { siteId, equipmentId } = useParams<{ siteId: string; equipmentId: string }>();
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<VSCodeTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  const equipment: CompanyEquipment | undefined = useMemo(() => {
    return mockCompanyEquipment.find(eq => eq.id === (equipmentId || ''));
  }, [equipmentId]);

  useEffect(() => {
    if (equipment) {
      const aboutTab: VSCodeTab = { id: 'equipment-about', title: 'About', type: 'details', data: { type: 'about', equipment }, closable: false };
      setOpenTabs([aboutTab]);
      setActiveTabId('equipment-about');
      setSelectedItem('equipment-about');
    }
  }, [equipment]);

  if (!equipment) {
    return (
      <FloatingCard title="Error Loading Equipment" breadcrumbs={[]}> 
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">Equipment not found</p>
          <button onClick={() => navigate(-1)} className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Go Back</button>
        </div>
      </FloatingCard>
    );
  }

  const breadcrumbs = siteId
    ? [
        { name: 'Dashboard', path: '/' },
        { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
        { name: 'Equipment', path: `/sites/${siteId}/equipment` },
        { name: equipment.name, path: `/sites/${siteId}/equipment/${equipmentId}` },
      ]
    : [
        { name: 'Dashboard', path: '/' },
        { name: 'Equipment', path: `/company-equipment` },
        { name: equipment.name, path: `/equipment/${equipmentId}` },
      ];

  const explorerItems = createEquipmentExplorerItems(equipment);
  const { renderTabContent } = EquipmentTabs({ equipment, siteId });

  const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
    setSelectedItem(itemId);
    const existingTab = openTabs.find(tab => tab.id === itemId);
    if (!existingTab) {
      const title = itemId === 'equipment-about' ? 'About' : itemId.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
      const newTab: VSCodeTab = { id: itemId, title, type: itemData?.type || itemType, data: itemData, closable: itemId !== 'equipment-about' };
      setOpenTabs(prev => [...prev, newTab]);
    }
    setActiveTabId(itemId);
  };

  const handleTabClose = (tabId: string) => { 
    if (tabId === 'equipment-about') return; 
    setOpenTabs(prev => prev.filter(tab => tab.id !== tabId)); 
    if (activeTabId === tabId) { 
      const remaining = openTabs.filter(tab => tab.id !== tabId); 
      setActiveTabId(remaining.length > 0 ? remaining[remaining.length - 1].id : null); 
    } 
  };

  const handleTabChange = (tabId: string) => setActiveTabId(tabId);

  return (
    <FloatingCard
      title={`${equipment.name} - Equipment`}
      breadcrumbs={breadcrumbs}
      layout="custom"
      topBarShowBack
      topBarOnBack={() => navigate(-1)}
    >
      <div className="h-full flex flex-col min-w-0">
        <div className="flex-1 min-h-0">
          <VSCodeInterface
            explorerItems={explorerItems}
            tabs={openTabs}
            activeTabId={activeTabId}
            selectedItem={selectedItem}
            onItemSelect={handleItemSelect}
            onTabChange={handleTabChange}
            onTabClose={handleTabClose}
            renderTabContent={renderTabContent}
            hideExplorer={true}
          />
        </div>
      </div>
    </FloatingCard>
  );
};

export default EquipmentProfile;
