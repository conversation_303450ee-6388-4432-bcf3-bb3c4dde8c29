import React, { useEffect, useMemo, useState } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { Search, Filter, ArrowLeft, Upload, CheckCircle, Clock, XCircle, AlertTriangle, Factory } from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import UniversalFilterModal, { FilterValues } from '../../components/common/UniversalFilterModal';
import ActiveFiltersBar from '../../components/common/ActiveFiltersBar';
import { mockCompanyEquipment } from '../../data/equipmentMockData';

const SiteImportEquipmentPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();

  // Toolbar and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Persist selection, filters and search per site using sessionStorage
  const storageSelKey = `siteImportEqSelection:${siteId || 'default'}`;
  const storageFiltersKey = `siteImportEqFilters:${siteId || 'default'}`;
  const storageSearchKey = `siteImportEqSearch:${siteId || 'default'}`;

  // Restore on mount
  useEffect(() => {
    try {
      const rawSel = sessionStorage.getItem(storageSelKey);
      if (rawSel) {
        const parsed = JSON.parse(rawSel);
        if (Array.isArray(parsed)) setSelectedIds(parsed.filter((s: any) => typeof s === 'string'));
      }
      const rawFilters = sessionStorage.getItem(storageFiltersKey);
      if (rawFilters) {
        const parsed = JSON.parse(rawFilters);
        if (parsed && typeof parsed === 'object') setActiveFilters(parsed);
      }
      const rawSearch = sessionStorage.getItem(storageSearchKey);
      if (rawSearch) setSearchTerm(rawSearch);
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [siteId]);

  // Persist on change
  useEffect(() => { try { sessionStorage.setItem(storageSelKey, JSON.stringify(selectedIds)); } catch {} }, [selectedIds, storageSelKey]);
  useEffect(() => { try { sessionStorage.setItem(storageFiltersKey, JSON.stringify(activeFilters)); } catch {} }, [activeFilters, storageFiltersKey]);
  useEffect(() => { try { sessionStorage.setItem(storageSearchKey, searchTerm); } catch {} }, [searchTerm, storageSearchKey]);

  // Basic filter config (category, ownership, availability, compliance)
  const filterConfig = useMemo(() => {
    const categories = Array.from(new Set(mockCompanyEquipment.map(e => e.category)));
    return [
      {
        id: 'category',
        label: 'Category',
        type: 'multiselect' as const,
        options: categories.map(cat => ({ value: cat, label: cat, count: mockCompanyEquipment.filter(e => e.category === cat).length }))
      },
      {
        id: 'ownership',
        label: 'Ownership',
        type: 'dropdown' as const,
        placeholder: 'Select ownership',
        options: [
          { value: 'company', label: 'Company Owned', count: mockCompanyEquipment.filter(e => e.ownershipType === 'company').length },
          { value: 'rented', label: 'Rented', count: mockCompanyEquipment.filter(e => e.ownershipType === 'rented').length },
          { value: 'contracted', label: 'Contracted', count: mockCompanyEquipment.filter(e => e.ownershipType === 'contracted').length },
        ],
      },
      {
        id: 'availability',
        label: 'Availability',
        type: 'dropdown' as const,
        placeholder: 'Select availability',
        options: [
          { value: 'available', label: 'Available (No Assignment)', count: mockCompanyEquipment.filter(e => !e.currentSiteId).length },
          { value: 'assigned', label: 'Assigned to any Site', count: mockCompanyEquipment.filter(e => !!e.currentSiteId).length },
        ],
      },
      {
        id: 'compliance',
        label: 'Compliance',
        type: 'dropdown' as const,
        placeholder: 'Select compliance',
        options: [
          { value: 'compliant', label: 'Compliant', count: mockCompanyEquipment.filter(e => e.complianceStatus === 'compliant').length },
          { value: 'warning', label: 'Warning', count: mockCompanyEquipment.filter(e => e.complianceStatus === 'warning').length },
          { value: 'critical', label: 'Critical', count: mockCompanyEquipment.filter(e => e.complianceStatus === 'critical').length },
          { value: 'overdue', label: 'Overdue', count: mockCompanyEquipment.filter(e => e.complianceStatus === 'overdue').length },
        ],
      },
    ];
  }, []);

  // Compute filtered equipment from company list
  const filteredEquipment = useMemo(() => {
    let list = mockCompanyEquipment;

    const term = searchTerm.trim().toLowerCase();
    if (term) {
      list = list.filter(e =>
        e.name.toLowerCase().includes(term) ||
        e.equipmentNumber.toLowerCase().includes(term) ||
        (e.serialNumber || '').toLowerCase().includes(term) ||
        (e.model || '').toLowerCase().includes(term)
      );
    }

    if (activeFilters.category && Array.isArray(activeFilters.category) && activeFilters.category.length > 0) {
      list = list.filter(e => activeFilters.category!.some((c: string) => e.category === c));
    }

    if (activeFilters.ownership) {
      list = list.filter(e => e.ownershipType === activeFilters.ownership);
    }

    if (activeFilters.availability === 'available') {
      list = list.filter(e => !e.currentSiteId);
    } else if (activeFilters.availability === 'assigned') {
      list = list.filter(e => !!e.currentSiteId);
    }

    if (activeFilters.compliance) {
      list = list.filter(e => e.complianceStatus === activeFilters.compliance);
    }

    return list;
  }, [searchTerm, activeFilters]);

  // Sort selection to top
  const displayEquipment = useMemo(() => {
    const selectedSet = new Set(selectedIds);
    return [...filteredEquipment].sort((a, b) => {
      const aSel = selectedSet.has(a.id);
      const bSel = selectedSet.has(b.id);
      if (aSel && !bSel) return -1;
      if (!aSel && bSel) return 1;
      return 0;
    });
  }, [filteredEquipment, selectedIds]);

  const isAllSelected = displayEquipment.length > 0 && selectedIds.length === displayEquipment.length;
  const toggleSelectAll = () => {
    if (isAllSelected) setSelectedIds([]);
    else setSelectedIds(displayEquipment.map(e => e.id));
  };
  const toggleSelectOne = (id: string) => setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);

  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) return Object.values(value).some(v => v !== '' && v !== null);
    return value !== '' && value !== null && value !== false;
  }).length;

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
    { name: 'Equipment', path: `/sites/${siteId}/equipment` },
    { name: 'Import', path: `/sites/${siteId}/equipment/import` },
  ];

  const complianceIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'critical': return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'overdue': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const handleImportSelected = () => {
    alert(`Importing ${selectedIds.length} equipment to site ${siteId} (placeholder)`);
    navigate(`/sites/${siteId}/equipment`);
  };

  return (
    <FloatingCard title={`Import Equipment`} breadcrumbs={breadcrumbs} topBarShowBack={true} topBarOnBack={() => navigate(-1)}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {/* Toolbar */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-3">
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeft className="h-4 w-4 mr-1" /> Back
            </button>
          </div>
          <div className="flex items-center w-full md:w-auto gap-3">
            <div className="relative flex-1 md:flex-initial md:w-80">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-500" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                placeholder="Search company equipment..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <button
              onClick={() => setIsFilterOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <Filter className="h-4 w-4 mr-2" /> Filters
              {activeFilterCount > 0 && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {activeFilterCount}
                </span>
              )}
            </button>
            <button
              onClick={handleImportSelected}
              disabled={selectedIds.length === 0}
              className={`inline-flex items-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${selectedIds.length === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
            >
              <Upload className="h-4 w-4 mr-2" /> Import Selected
            </button>
          </div>
        </div>

        {/* Active Filters */}
        <ActiveFiltersBar
          values={activeFilters}
          config={filterConfig}
          onRemove={(filterId) => {
            const newFilters = { ...activeFilters } as Record<string, any>;
            delete newFilters[filterId];
            setActiveFilters(newFilters);
          }}
          onClear={() => setActiveFilters({})}
        />

        {/* Table */}
        <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg mt-4">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={toggleSelectAll}
                    aria-label="Select all"
                    className="h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ownership</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {displayEquipment.map(eq => (
                <tr key={eq.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedIds.includes(eq.id)}
                      onChange={() => toggleSelectOne(eq.id)}
                      aria-label={`Select ${eq.name}`}
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center text-blue-700 text-xs font-semibold">
                          {eq.category?.slice(0, 2) || 'EQ'}
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          <Link to={`/sites/${siteId}/equipment/${eq.id}`} className="hover:text-green-500">
                            {eq.name}
                          </Link>
                        </div>
                        <div className="text-xs text-gray-500">{eq.equipmentNumber}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{eq.category}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize flex items-center gap-1">
                    <Factory className="h-4 w-4 text-gray-500" /> {eq.ownershipType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {complianceIcon(eq.complianceStatus)}
                      <span className="ml-1 capitalize">{eq.complianceStatus}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <Link
                        to={`/sites/${siteId}/equipment/${eq.id}`}
                        className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                        style={{ borderRadius: '5px' }}
                      >
                        View
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {displayEquipment.length === 0 && (
            <div className="px-6 py-12 text-center text-sm text-gray-600">No company equipment match your filters.</div>
          )}
        </div>
      </div>

      {/* Filters Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Company Equipment"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={(values) => setActiveFilters(values)}
        onClearFilters={() => setActiveFilters({})}
        size="xl"
      />
    </FloatingCard>
  );
};

export default SiteImportEquipmentPage;
