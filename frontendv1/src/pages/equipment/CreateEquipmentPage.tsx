import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import FloatingCard from '../../components/layout/FloatingCard';
import { PhotoCapture } from '../../components/ui/photo-capture';
import CreateEquipmentForm from '../../components/equipment/CreateEquipmentForm';
import { equipmentCategories, CompanyEquipment } from '../../data/equipmentMockData';

 type TabType = 'details' | 'specs' | 'ownership' | 'maintenance' | 'documents';

const CreateEquipmentPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [activeTab, setActiveTab] = useState<TabType>('details');
  const tabs: { key: TabType; label: string }[] = [
    { key: 'details', label: 'Details' },
    { key: 'specs', label: 'Specifications' },
    { key: 'ownership', label: 'Ownership' },
    { key: 'maintenance', label: 'Maintenance' },
    { key: 'documents', label: 'Documents' },
  ];

  const [profileName, setProfileName] = useState<string>('');
  const [equipmentNumber, setEquipmentNumber] = useState<string>('');
  const [category, setCategory] = useState<string | undefined>(equipmentCategories[0]);
  const [profilePhotoFile, setProfilePhotoFile] = useState<File | null>(null);

  const [submitSignal, setSubmitSignal] = useState<number>(0);

  const breadcrumbs = siteId
    ? [
        { name: 'Dashboard', path: '/' },
        { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
        { name: 'Equipment', path: `/sites/${siteId}/equipment` },
        { name: 'Add Equipment', path: `/sites/${siteId}/equipment/create` },
      ]
    : [
        { name: 'Dashboard', path: '/' },
        { name: 'Company Equipment', path: '/company-equipment' },
        { name: 'Add Equipment', path: '/equipment/create' },
      ];

  return (
    <FloatingCard
      title={siteId ? `Add equipment to Site ${siteId}` : 'Create Equipment'}
      breadcrumbs={breadcrumbs}
      topBarShowBack={true}
      topBarOnBack={() => navigate(-1)}
      topBarMinimal={true}
      topBarRightActions={(
        <div className="flex items-center gap-2">
          <button
            onClick={() => { if (siteId) navigate(`/sites/${siteId}/equipment`); else navigate('/company-equipment'); }}
            className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              if (activeTab === 'details') setSubmitSignal(s => s + 1);
              else toast.info('Nothing to save on this step yet.');
            }}
            className="inline-flex items-center px-5 h-9 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
          >
            Save
          </button>
        </div>
      )}
    >
      <div className="flex gap-6">
        <aside className="hidden lg:block w-80 flex-shrink-0 sticky top-0 self-start">
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 h-[calc(100vh-180px)] flex flex-col justify-between">
            <div className="flex flex-col items-center text-center gap-4">
              <PhotoCapture
                currentPhoto={profilePhotoFile || null}
                onPhotoChange={(file) => { setProfilePhotoFile(file); }}
                className="border-none shadow-none"
                photoSize="lg"
              />
              <div>
                <div className="text-base font-semibold text-gray-900">{profileName || 'New Equipment'}</div>
                <div className="text-xs text-gray-500">{equipmentNumber || 'Number TBD'}</div>
                <div className="mt-1 text-xs inline-flex items-center px-2.5 py-0.5 rounded-full bg-blue-50 text-blue-700 border border-blue-200">{category || 'Uncategorized'}</div>
              </div>
            </div>

            <div className="mt-8 flex-1 overflow-auto pr-1">
              <ol className="ml-0 space-y-3">
                {tabs.map((t, idx) => {
                  const isActive = activeTab === t.key;
                  const activeIndex = tabs.findIndex(x => x.key === activeTab);
                  const isComplete = idx < activeIndex;
                  return (
                    <li key={t.key} className="ml-0">
                      <div role="button" tabIndex={0} onClick={() => setActiveTab(t.key)} onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') setActiveTab(t.key); }} className="w-full text-left flex items-center gap-3 cursor-pointer select-none" aria-current={isActive ? 'step' : undefined}>
                        <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full border text-xs font-semibold ${isActive ? 'bg-green-600 text-white border-green-600' : isComplete ? 'bg-green-100 text-green-700 border-green-200' : 'bg-white text-gray-600 border-gray-300'}`}>
                          {isComplete ? '✓' : idx + 1}
                        </span>
                        <span className={`text-sm font-medium ${isActive ? 'text-gray-900' : 'text-gray-700'}`}>{t.label}</span>
                      </div>
                    </li>
                  );
                })}
              </ol>
            </div>

            <div className="pt-4 border-t border-gray-100 text-xs text-gray-500">Changes are saved when you click Save. You can navigate steps anytime.</div>
          </div>
        </aside>

        <section className="flex-1 min-w-0">
          <div className="bg-white rounded-xl border border-gray-200 w-full">
            <div className="px-3 border-b border-gray-200 rounded-t-xl">
              <nav className="flex gap-2">
                {tabs.map((t) => (
                  <button key={t.key} onClick={() => setActiveTab(t.key)} className={`h-12 px-4 -mb-px border-b-2 text-sm font-medium ${activeTab === t.key ? 'border-green-600 text-gray-900' : 'border-transparent text-gray-600 hover:text-gray-800'}`}>
                    {t.label}
                  </button>
                ))}
              </nav>
            </div>
            <div className="p-6 w-full">
              {activeTab === 'details' && (
                <CreateEquipmentForm
                  submitSignal={submitSignal}
                  onSuccess={(eq: CompanyEquipment) => {
                    toast.success(`Created ${eq.name}`);
                    if (siteId) navigate(`/sites/${siteId}/equipment`); else navigate('/company-equipment');
                  }}
                  onCancel={() => { if (siteId) navigate(`/sites/${siteId}/equipment`); else navigate('/company-equipment'); }}
                  onBasicInfoChange={({ name, equipmentNumber, category }) => { setProfileName(name); setEquipmentNumber(equipmentNumber); setCategory(category); }}
                  onPrev={() => { const idx = tabs.findIndex(t => t.key === activeTab); if (idx > 0) setActiveTab(tabs[idx - 1].key); }}
                  onNext={() => { const idx = tabs.findIndex(t => t.key === activeTab); if (idx < tabs.length - 1) setActiveTab(tabs[idx + 1].key); else if (activeTab === 'details') setSubmitSignal(s => s + 1); }}
                  siteId={siteId}
                />
              )}

              {activeTab !== 'details' && (
                <div className="space-y-4">
                  {activeTab === 'specs' && (
                    <div className="text-sm text-gray-700">You will be able to manage equipment specifications here.</div>
                  )}
                  {activeTab === 'ownership' && (
                    <div className="text-sm text-gray-700">You will be able to manage ownership details here.</div>
                  )}
                  {activeTab === 'maintenance' && (
                    <div className="text-sm text-gray-700">You will be able to manage maintenance schedules here.</div>
                  )}
                  {activeTab === 'documents' && (
                    <div className="text-sm text-gray-700">This section is coming soon. You will be able to manage equipment documents here.</div>
                  )}
                </div>
              )}
            </div>
          </div>
        </section>
      </div>
    </FloatingCard>
  );
};

export default CreateEquipmentPage;
