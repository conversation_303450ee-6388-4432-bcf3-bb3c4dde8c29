import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';

// Import layout components
import FloatingCard from '../components/layout/FloatingCard';
import { But<PERSON> } from '../components/ui/button';
import { PhotoCapture } from '../components/ui/photo-capture';

// Import step components
import BasicInfoStep from '../components/site-creation/BasicInfoStep';
import SiteBoundaryStep from '../components/site-creation/SiteBoundaryStep';
import AreasStep from '../components/site-creation/AreasStep';
import StakeholdersStep from '../components/site-creation/StakeholdersStep';
import RegulatoryStep from '../components/site-creation/RegulatoryStep';

// Import supporting components
// We will reuse the exact left progress markup from Worker/Equipment pages instead of StepSidebar

// Types for site creation data
export interface SiteCreationData {
  basicInfo: {
    siteName: string;
    projectType: string;
    constructionType: string;
    plannedStartDate: string;
    plannedEndDate: string;
    estimatedBudget: number;
    currency: string;
    clientName: string;
    projectManagerName: string;
    description?: string;
  };
  siteBoundary: {
    searchQuery: string;
    displayName: string;
    addressStreet: string;
    addressCity: string;
    addressCounty: string;
    addressPostalCode: string;
    addressCountry: string;
    latitude: number;
    longitude: number;
    accuracy: string;
    osmPlaceId: string;
    osmType: string;
    osmId: string;
    geometry: {
      type: 'Polygon';
      coordinates: number[][][];
    } | null;
    drawingComplete: boolean;
    lastModified: string;
    calculatedArea?: number;
    calculatedPerimeter?: number;
  };
  areas: {
    areaNames: string[];
  };
  stakeholders: {
    clientEmail: string;
    clientPhone: string;
    projectManagerEmail: string;
    projectManagerPhone: string;
    mainContractorName: string;
    mainContractorEmail: string;
    mainContractorCompany: string;
    architectName: string;
    architectEmail: string;
    architectCompany: string;
    additionalStakeholders: any[];
  };
  regulatory: {
    buildingPermitRequired: boolean;
    buildingPermitStatus: string;
    buildingPermitNumber: string;
    buildingPermitAuthority: string;
    buildingPermitDate: string;
    buildingPermitExpiry: string;
    buildingPermitDocument: any;
    nemaEiaRequired: boolean;
    nemaEiaStatus: string;
    nemaEiaNumber: string;
    nemaEiaDate: string;
    nemaEiaDocument: any;
    ncaRegistrationRequired: boolean;
    ncaRegistrationStatus: string;
    ncaRegistrationNumber: string;
    ncaRegistrationDate: string;
    ncaRegistrationDocument: any;
    fireSafetyRequired: boolean;
    fireSafetyStatus: string;
    fireSafetyNumber: string;
    fireSafetyDate: string;
    fireSafetyDocument: any;
    overallComplianceStatus: string;
    additionalApprovals: any[];
  };
}

const STEPS = [
  {
    id: 1,
    name: 'Basic Info',
    description: 'Project details & timeline',
    component: BasicInfoStep
  },
  {
    id: 2,
    name: 'Site Boundary',
    description: 'Location & boundary drawing',
    component: SiteBoundaryStep
  },
  {
    id: 3,
    name: 'Areas',
    description: 'Define site areas',
    component: AreasStep
  },
  {
    id: 4,
    name: 'Stakeholders',
    description: 'Key contacts & roles',
    component: StakeholdersStep
  },
  {
    id: 5,
    name: 'Regulatory',
    description: 'Required approvals',
    component: RegulatoryStep
  }
];

// Simple workflow state management hook
const useSiteCreationWorkflow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [siteData, setSiteData] = useState<SiteCreationData>({
    basicInfo: {
      siteName: '',
      projectType: '',
      constructionType: '',
      plannedStartDate: '',
      plannedEndDate: '',
      estimatedBudget: 0,
      currency: 'KES',
      clientName: '',
      projectManagerName: '',
      description: ''
    },
    siteBoundary: {
      searchQuery: '',
      displayName: '',
      addressStreet: '',
      addressCity: '',
      addressCounty: '',
      addressPostalCode: '',
      addressCountry: 'Kenya',
      latitude: 0,
      longitude: 0,
      accuracy: '',
      osmPlaceId: '',
      osmType: '',
      osmId: '',
      geometry: null,
      drawingComplete: false,
      lastModified: new Date().toISOString(),
      calculatedArea: undefined,
      calculatedPerimeter: undefined
    },
    areas: {
      areaNames: []
    },
    stakeholders: {
      clientEmail: '',
      clientPhone: '',
      projectManagerEmail: '',
      projectManagerPhone: '',
      mainContractorName: '',
      mainContractorEmail: '',
      mainContractorCompany: '',
      architectName: '',
      architectEmail: '',
      architectCompany: '',
      additionalStakeholders: []
    },
    regulatory: {
      buildingPermitRequired: true,
      buildingPermitStatus: 'Not Started',
      buildingPermitNumber: '',
      buildingPermitAuthority: '',
      buildingPermitDate: '',
      buildingPermitExpiry: '',
      buildingPermitDocument: null,
      nemaEiaRequired: false,
      nemaEiaStatus: 'Not Started',
      nemaEiaNumber: '',
      nemaEiaDate: '',
      nemaEiaDocument: null,
      ncaRegistrationRequired: true,
      ncaRegistrationStatus: 'Not Started',
      ncaRegistrationNumber: '',
      ncaRegistrationDate: '',
      ncaRegistrationDocument: null,
      fireSafetyRequired: true,
      fireSafetyStatus: 'Not Started',
      fireSafetyNumber: '',
      fireSafetyDate: '',
      fireSafetyDocument: null,
      overallComplianceStatus: 'Incomplete',
      additionalApprovals: []
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const goToStep = useCallback((stepId: number) => {
    setCurrentStep(stepId);
  }, []);

  const nextStep = useCallback(() => {
    if (currentStep < STEPS.length) {
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep, completedSteps]);

  const previousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const updateStepData = useCallback((stepId: number, stepData: any) => {
    const stepKey = getStepDataKey(stepId);
    setSiteData(prev => ({
      ...prev,
      [stepKey]: { ...prev[stepKey], ...stepData }
    }));
    setHasUnsavedChanges(true);
  }, []);

  const getStepDataKey = (stepId: number): keyof SiteCreationData => {
    const keyMap: Record<number, keyof SiteCreationData> = {
      1: 'basicInfo',
      2: 'siteBoundary',
      3: 'areas',
      4: 'stakeholders',
      5: 'regulatory'
    };
    return keyMap[stepId] || 'basicInfo';
  };

  const saveDraft = useCallback(async () => {
    setIsSaving(true);
    try {
      // Mock save operation
      await new Promise(resolve => setTimeout(resolve, 500));
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to save draft:', error);
    } finally {
      setIsSaving(false);
    }
  }, []);

  const createSite = useCallback(async (): Promise<string> => {
    setIsLoading(true);
    try {
      // Mock site creation
      await new Promise(resolve => setTimeout(resolve, 2000));
      const siteId = `site_${Date.now()}`;
      return siteId;
    } catch (error) {
      console.error('Failed to create site:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearSession = useCallback(() => {
    setCurrentStep(1);
    setCompletedSteps([]);
    setHasUnsavedChanges(false);
  }, []);

  return {
    currentStep,
    completedSteps,
    siteData,
    isLoading,
    isSaving,
    goToStep,
    nextStep,
    previousStep,
    updateStepData,
    saveDraft,
    createSite,
    hasUnsavedChanges: () => hasUnsavedChanges,
    clearSession
  };
};

const NewSitePage: React.FC = () => {
  const navigate = useNavigate();
  // Local preview state for site photo capture and name
  const [sitePhotoFile, setSitePhotoFile] = useState<File | null>(null);



  // Use site creation workflow hook
  const {
    currentStep,
    completedSteps,
    siteData,
    isLoading,
    isSaving,
    goToStep,
    nextStep,
    previousStep,
    updateStepData,
    saveDraft,
    createSite,
    hasUnsavedChanges,
    clearSession
  } = useSiteCreationWorkflow();



  // Auto-save every 30 seconds
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (hasUnsavedChanges()) {
        saveDraft();
      }
    }, 30000);

    return () => clearInterval(autoSaveInterval);
  }, [hasUnsavedChanges, saveDraft]);

  const handleTabClick = useCallback((stepId: number) => {
    // Allow navigation to any step when user clicks a tab for easier movement between steps
    goToStep(stepId);
  }, [goToStep]);

  const handleStepComplete = useCallback((stepData: any) => {
    updateStepData(currentStep, stepData);
    // Auto-save after step completion
    setTimeout(() => saveDraft(), 500);
  }, [currentStep, updateStepData, saveDraft]);

  const handleSave = useCallback(() => {
    // Manual save action to mirror Worker/Equipment Save behavior
    saveDraft();
  }, [saveDraft]);

  const handleCreateSite = useCallback(async () => {
    try {
      const siteId = await createSite();
      navigate(`/sites/${siteId}/dashboard`);
    } catch (error) {
      console.error('Site creation failed:', error);
    }
  }, [createSite, navigate]);

  // Step navigation similar to worker flow
  const handlePrevious = useCallback(() => {
    if (currentStep > 1) previousStep();
  }, [currentStep, previousStep]);

  const handleNext = useCallback(() => {
    if (currentStep < STEPS.length) nextStep();
  }, [currentStep, nextStep]);

  // Calculate progress percentage
  const progressPercentage = (completedSteps.length / STEPS.length) * 100;

  // Tabs config (match Worker/Equipment top nav style)
  const tabs = STEPS.map(s => ({ key: s.id, label: s.name }));
  const activeTab = currentStep;

  return (
    <>
      <FloatingCard
        title="Create Site"
        breadcrumbs={[
          { name: 'Dashboard', path: '/' },
          { name: 'Create Site', path: '/sites/new' }
        ]}
        topBarMinimal={true}
        topBarRightActions={(
          <div className="flex items-center gap-2">
            {isSaving && (
              <span className="hidden md:inline-flex items-center text-xs text-gray-500 mr-2">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Auto-saving...
              </span>
            )}
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="inline-flex items-center px-5 h-9 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              Save
            </button>
          </div>
        )}
      >
        <div className="flex gap-6">
          {/* Left Aside: Photo + Progress (reuse exact markup from Worker/Equipment) */}
          <aside className="hidden lg:block w-80 flex-shrink-0 sticky top-0 self-start">
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 h-[calc(100vh-180px)] flex flex-col justify-between">
              <div className="flex flex-col items-center text-center gap-4">
                <PhotoCapture
                  currentPhoto={sitePhotoFile || null}
                  onPhotoChange={(file) => { setSitePhotoFile(file); }}
                  className="border-none shadow-none"
                  photoSize="lg"
                />
                <div>
                  <div className="text-base font-semibold text-gray-900">{siteData.basicInfo.siteName || 'New Site'}</div>
                  <div className="text-xs text-gray-500">{siteData.basicInfo.projectType || 'Project Type TBD'}</div>
                </div>
              </div>

              <div className="mt-8 flex-1 overflow-auto pr-1">
                <ol className="ml-0 space-y-3">
                  {tabs.map((t, idx) => {
                    const isActive = activeTab === t.key;
                    const activeIndex = tabs.findIndex(x => x.key === activeTab);
                    const isComplete = idx < activeIndex;
                    return (
                      <li key={t.key} className="ml-0">
                        <div
                          role="button"
                          tabIndex={0}
                          onClick={() => handleTabClick(t.key)}
                          onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') handleTabClick(t.key); }}
                          className="w-full text-left flex items-center gap-3 cursor-pointer select-none"
                          aria-current={isActive ? 'step' : undefined}
                        >
                          <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full border text-xs font-semibold ${isActive ? 'bg-green-600 text-white border-green-600' : isComplete ? 'bg-green-100 text-green-700 border-green-200' : 'bg-white text-gray-600 border-gray-300'}`}>
                            {isComplete ? '✓' : idx + 1}
                          </span>
                          <span className={`text-sm font-medium ${isActive ? 'text-gray-900' : 'text-gray-700'}`}>{t.label}</span>
                        </div>
                      </li>
                    );
                  })}
                </ol>
              </div>

              <div className="pt-4 border-t border-gray-100 text-xs text-gray-500">Changes are saved automatically and when you click Save. You can navigate steps anytime.</div>
            </div>
          </aside>

          {/* Right Panel: Tabs + Content */}
          <section className="flex-1 min-w-0">
            <div className="bg-white rounded-xl border border-gray-200 w-full">
              <div className="px-2 border-b border-gray-200 rounded-t-xl">
                <nav className="flex gap-2">
                  {tabs.map((t) => (
                    <button
                      key={t.key}
                      onClick={() => handleTabClick(t.key)}
                      className={`h-12 px-4 -mb-px border-b-2 text-sm font-medium ${activeTab === t.key ? 'border-green-600 text-gray-900' : 'border-transparent text-gray-600 hover:text-gray-800'}`}
                    >
                      {t.label}
                    </button>
                  ))}
                </nav>
              </div>
              <div className="p-3 w-full">
                {currentStep === 1 && (
                  <BasicInfoStep
                    data={siteData.basicInfo}
                    onComplete={handleStepComplete}
                    onNext={() => {}}
                    onPrevious={() => {}}
                    isFirstStep={true}
                    isLastStep={false}
                  />
                )}
                {currentStep === 2 && (
                  <SiteBoundaryStep
                    data={siteData.siteBoundary}
                    onComplete={handleStepComplete}
                    onNext={() => {}}
                    onPrevious={() => {}}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 3 && (
                  <AreasStep
                    data={siteData.areas}
                    onComplete={handleStepComplete}
                    onNext={() => {}}
                    onPrevious={() => {}}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 4 && (
                  <StakeholdersStep
                    data={siteData.stakeholders}
                    onComplete={handleStepComplete}
                    onNext={() => {}}
                    onPrevious={() => {}}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 5 && (
                  <RegulatoryStep
                    data={siteData.regulatory}
                    onComplete={handleStepComplete}
                    onNext={() => {}}
                    onPrevious={() => {}}
                    isFirstStep={false}
                    isLastStep={true}
                    onCreateSite={handleCreateSite}
                    isCreating={isLoading}
                  />
                )}
              </div>
              {/* Bottom Navigation Controls - match worker form placement and style */}
              <div className="flex items-center pt-6 border-t border-gray-200 px-4 pb-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                >
                  ← Previous
                </Button>
                <div className="flex-1" />
                {currentStep < STEPS.length ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    className="min-w-[120px]"
                  >
                    Next →
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={handleCreateSite}
                    disabled={isLoading}
                    className="min-w-[120px]"
                  >
                    {isLoading ? 'Creating…' : 'Create Site'}
                  </Button>
                )}
              </div>
            </div>
          </section>
        </div>
      </FloatingCard>
    </>
  );
};

export default NewSitePage;
