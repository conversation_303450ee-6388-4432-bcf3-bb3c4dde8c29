import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import FloatingCard from '../components/layout/FloatingCard';
import VSCodeInterface, { VSCodeTab } from '../components/shared/VSCodeInterface';
import createPermitExplorerItems from '../components/permits/PermitExplorer';
import PermitTabs from '../components/permits/PermitTabs';
import { Permit } from '../types/permits';
import { SiteInfo } from '../types';
import { X } from 'lucide-react';

// Mock site data
const mockSite: SiteInfo = {
  id: 'site1',
  name: 'Construction Site Alpha',
  location: 'Downtown District',
  status: 'active',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31'),
  // description: 'Major construction project',
  projectManager: '<PERSON>',
  tenantId: '',
  healthStatus: 'green',
  workersOnSite: 0,
  activePermits: 0,
  openIncidents: 0,
  createdAt: new Date('2024-09-01T00:00:00Z')
};

// Mock permit data - this would normally come from an API
const getMockPermit = (permitId: string, permitType: string): Permit | null => {
  // Generate permit type configuration based on the permitType parameter
  const getPermitTypeConfig = (type: string) => {
    switch (type) {
      case 'hot-work':
        return {
          id: 'hot-work',
          name: 'Hot Work Permit',
          description: 'Permit for welding, cutting, and other hot work operations',
          category: 'Safety Critical',
          defaultValidityHours: 8,
          requiredTrainings: ['Hot Work Safety', 'Fire Prevention'],
          requiredCertifications: ['Welding Certification'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      case 'confined-space':
        return {
          id: 'confined-space',
          name: 'Confined Space Entry Permit',
          description: 'Permit for entry into confined spaces with restricted access',
          category: 'Safety Critical',
          defaultValidityHours: 8,
          requiredTrainings: ['Confined Space Entry', 'Gas Testing'],
          requiredCertifications: ['Confined Space Entry Certificate'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      case 'work-at-height':
        return {
          id: 'work-at-height',
          name: 'Work at Height Permit',
          description: 'Permit for work activities conducted at height above 2 meters',
          category: 'Safety Critical',
          defaultValidityHours: 8,
          requiredTrainings: ['Height Safety', 'Fall Protection'],
          requiredCertifications: ['Height Safety Certificate'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      case 'excavation':
        return {
          id: 'excavation',
          name: 'Excavation Permit',
          description: 'Permit for excavation and trenching activities',
          category: 'Safety Critical',
          defaultValidityHours: 8,
          requiredTrainings: ['Excavation Safety', 'Utility Location'],
          requiredCertifications: ['Excavation Safety Certificate'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      case 'general-work':
        return {
          id: 'general-work',
          name: 'General Work Permit',
          description: 'General permit for standard work activities',
          category: 'Standard',
          defaultValidityHours: 8,
          requiredTrainings: ['General Safety'],
          requiredCertifications: ['General Safety Certificate'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      default:
        return {
          id: 'general-work',
          name: 'General Work Permit',
          description: 'General permit for standard work activities',
          category: 'Standard',
          defaultValidityHours: 8,
          requiredTrainings: ['General Safety'],
          requiredCertifications: ['General Safety Certificate'],
          template: {} as any,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
    }
  };

  const permitTypeConfig = getPermitTypeConfig(permitType);

  const permits: Permit[] = [
    {
      id: permitId,
      permitNumber: `PRM-2024-${permitId.slice(-3)}`,
      title: permitType === 'hot-work' ? 'Structural Steel Welding - Level 3' :
             permitType === 'confined-space' ? 'Tank Inspection and Maintenance' :
             permitType === 'work-at-height' ? 'Roof Maintenance Work' :
             permitType === 'excavation' ? 'Foundation Excavation Work' :
             'General Maintenance Work',
      description: permitType === 'hot-work' ? 'Welding operations for structural steel installation' :
                   permitType === 'confined-space' ? 'Inspection and maintenance of storage tank interior' :
                   permitType === 'work-at-height' ? 'Roof inspection, repair, and maintenance work' :
                   permitType === 'excavation' ? 'Excavation work for foundation installation' :
                   'General maintenance and repair work',
      location: permitType === 'hot-work' ? 'Zone A - Level 3' :
                permitType === 'confined-space' ? 'Storage Tank Area' :
                permitType === 'work-at-height' ? 'Main Building - Rooftop' :
                permitType === 'excavation' ? 'Construction Site - Zone A' :
                'General Work Area',
      siteId: 'site-1',
      requestedDate: new Date('2024-01-15T08:00:00'),
      validFrom: new Date('2024-01-15T09:00:00'),
      validUntil: new Date('2024-01-15T17:00:00'),
      actualStartTime: new Date('2024-01-15T09:00:00'),
      status: 'open',
      priority: 'high',
      requestedBy: 'supervisor-1',
      requestedByName: 'John Smith',
      supervisorId: 'supervisor-1',
      supervisorName: 'John Smith',
      engineerId: 'engineer-1',
      engineerName: 'Sarah Wilson',
      taskReference: 'TSK-2024-001',
      permitType: permitTypeConfig,
      assignedWorkers: [
        {
          workerId: 'worker-1',
          workerName: permitType === 'hot-work' ? 'Mike Johnson' :
            permitType === 'confined-space' ? 'John Doe' :
              permitType === 'work-at-height' ? 'Sarah Connor' :
                permitType === 'excavation' ? 'Robert Smith' :
                  'Alex Wilson',
          primaryTrade: permitType === 'hot-work' ? 'Welder' :
            permitType === 'confined-space' ? 'Entry Supervisor' :
              permitType === 'work-at-height' ? 'Height Safety Technician' :
                permitType === 'excavation' ? 'Excavation Operator' :
                  'General Worker',
          role: 'supervisor',
          hasRequiredTraining: false,
          hasRequiredCertifications: false
        },
        {
          workerId: 'worker-2',
          workerName: permitType === 'hot-work' ? 'David Brown' :
            permitType === 'confined-space' ? 'Jane Smith' :
              permitType === 'work-at-height' ? 'Mike Davis' :
                permitType === 'excavation' ? 'Lisa Johnson' :
                  'Chris Taylor',
          primaryTrade: permitType === 'hot-work' ? 'Welder Assistant' :
            permitType === 'confined-space' ? 'Entry Attendant' :
              permitType === 'work-at-height' ? 'Safety Observer' :
                permitType === 'excavation' ? 'Spotter' :
                  'Assistant',
          role: 'supervisor',
          hasRequiredTraining: false,
          hasRequiredCertifications: false
        }
      ],
      approvals: [
        {
          id: 'approval-1',
          approverId: 'supervisor-1',
          approverName: 'John Smith',
          approverRole: 'Site Supervisor',
          status: 'approved',
          approvedAt: new Date('2024-01-15T08:30:00'),
          comments: 'All safety measures in place',
          stepId: ''
        }
      ],
      riskAssessment: {} as any,
      dailyRiskAssessments: [],
      formData: {},
      attachments: [],
      createdAt: new Date('2024-01-15T08:00:00'),
      updatedAt: new Date('2024-01-15T08:30:00'),
      history: []
    }
  ];

  return permits.find(p => p.id === permitId) || null;
};

const MainPermitDetailPage: React.FC = () => {
  const { siteId, permitType, permitId } = useParams<{
    siteId: string;
    permitType: string;
    permitId: string;
  }>();

  
  const [site] = useState<SiteInfo>(mockSite);
  const [permit, setPermit] = useState<Permit | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<VSCodeTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isReturnModalOpen, setIsReturnModalOpen] = useState(false);

  useEffect(() => {
    // Fetch permit details
    if (permitId && permitType) {
      const mockPermit = getMockPermit(permitId, permitType);
      setPermit(mockPermit);

      // Auto-open permit form tab (primary document)
      if (mockPermit) {
        const permitFormTab: VSCodeTab = {
          id: 'permit-form',
          title: 'Permit Form',
          type: 'permit-form',
          data: mockPermit,
          closable: false
        };
        setOpenTabs([permitFormTab]);
        setActiveTabId('permit-form');
      }
    }
  }, [permitId, permitType, siteId]);

  const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
    setSelectedItem(itemId);

    // Create a new tab for the selected item
    const existingTab = openTabs.find(tab => tab.id === itemId);
    if (!existingTab) {
      const newTab: VSCodeTab = {
        id: itemId,
        title: itemId === 'permit-form' ? 'Permit Form' :
               itemId === 'permit-history' ? 'Permit History' :
               itemData?.worker?.workerName ||
               itemData?.document?.name ||
               itemData?.photo?.name ||
               itemData?.approval?.approverRole + ' Approval' ||
               itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        type: itemType,
        data: itemData,
        closable: itemId !== 'permit-form'
      };

      setOpenTabs(prev => [...prev, newTab]);
    }
    setActiveTabId(itemId);
  };

  const handleTabClose = (tabId: string) => {
    // Prevent closing the main permit form tab
    if (tabId === 'permit-form') {
      return;
    }

    setOpenTabs(prev => prev.filter(tab => tab.id !== tabId));

    // If closing the active tab, switch to another tab
    if (activeTabId === tabId) {
      const remainingTabs = openTabs.filter(tab => tab.id !== tabId);
      setActiveTabId(remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : null);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTabId(tabId);
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Permits', path: `/sites/${siteId}/permits` },
    { name: permit?.permitNumber || 'Permit Details', path: `/sites/${siteId}/permits/${permitType}/${permitId}` },
  ];

  if (!permit) {
    return (
      <FloatingCard title="Permit Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading permit details...</div>
        </div>
      </FloatingCard>
    );
  }

  const explorerItems = createPermitExplorerItems(permit);
  const { renderTabContent } = PermitTabs({ permit });

  const topBarActions = (
    <div className="flex space-x-2">
      <button
        onClick={() => setIsReturnModalOpen(true)}
        className="px-3 py-1 text-xs bg-green-50 border border-green-300 text-green-700 rounded hover:bg-green-100 transition-colors font-medium"
        style={{ borderRadius: '5px' }}
      >
        Return
      </button>
      <button
        onClick={() => setIsCancelModalOpen(true)}
        className="px-3 py-1 text-xs bg-red-50 border border-red-300 text-red-700 rounded hover:bg-red-100 transition-colors font-medium"
        style={{ borderRadius: '5px' }}
      >
        Cancel
      </button>
    </div>
  );

  return (
    <FloatingCard title="Permit Details" breadcrumbs={breadcrumbs} layout="custom" topBarRightActions={topBarActions}>
      <VSCodeInterface
        explorerItems={explorerItems}
        tabs={openTabs}
        activeTabId={activeTabId}
        selectedItem={selectedItem}
        onItemSelect={handleItemSelect}
        onTabChange={handleTabChange}
        onTabClose={handleTabClose}
        renderTabContent={renderTabContent}
      />
      {/* Cancel Modal */}
      {isCancelModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Cancel Permit</h2>
              <button onClick={() => setIsCancelModalOpen(false)} className="text-gray-400 hover:text-gray-600 transition-colors">
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <p className="text-sm text-gray-700">You are about to cancel permit: <strong>{permit.title}</strong></p>
              <p className="text-sm text-gray-700">Permit Number: <strong>{permit.permitNumber}</strong></p>
              <div className="flex justify-end space-x-2 pt-2">
                <button onClick={() => setIsCancelModalOpen(false)} className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors" style={{ borderRadius: '5px' }}>Close</button>
                <button onClick={() => { console.log('Cancelled permit:', permit.id); setIsCancelModalOpen(false); }} className="px-3 py-1 text-xs bg-red-50 border border-red-300 text-red-700 rounded hover:bg-red-100 transition-colors font-medium" style={{ borderRadius: '5px' }}>Confirm Cancel</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Return Modal */}
      {isReturnModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Return Permit</h2>
              <button onClick={() => setIsReturnModalOpen(false)} className="text-gray-400 hover:text-gray-600 transition-colors">
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <p className="text-sm text-gray-700">You are about to return/submit permit: <strong>{permit.title}</strong></p>
              <p className="text-sm text-gray-700">Permit Number: <strong>{permit.permitNumber}</strong></p>
              <div className="flex justify-end space-x-2 pt-2">
                <button onClick={() => setIsReturnModalOpen(false)} className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors" style={{ borderRadius: '5px' }}>Close</button>
                <button onClick={() => { console.log('Returned permit:', permit.id); setIsReturnModalOpen(false); }} className="px-3 py-1 text-xs bg-green-50 border border-green-300 text-green-700 rounded hover:bg-green-100 transition-colors font-medium" style={{ borderRadius: '5px' }}>Confirm Return</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </FloatingCard>
  );
};

export default MainPermitDetailPage;
