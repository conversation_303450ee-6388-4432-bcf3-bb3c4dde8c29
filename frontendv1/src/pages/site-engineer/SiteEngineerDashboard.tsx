import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  CheckSquare,
  FileText,
  Sun,
  Plus,
  Clock,
  ArrowRight,

  Calendar,
  MapPin,
  User,
  Bell
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';

// Mock data for dashboard
const DASHBOARD_DATA = {
  tasks: {
    total: 12,
    pending: 5,
    inProgress: 4,
    completed: 3,
    overdue: 2
  },
  permits: {
    total: 8,
    approved: 6,
    pending: 2,
    expired: 0
  },
  weather: {
    current: 24,
    condition: 'Partly Cloudy',
    alerts: 1
  }
};

const RECENT_TASKS = [
  {
    id: 'task-001',
    name: 'Electrical Panel Installation',
    category: 'Electrical',
    status: 'in-progress',
    dueDate: '2024-08-06',
    location: 'Building A - Ground Floor',
    priority: 'high'
  },
  {
    id: 'task-002',
    name: 'Foundation Excavation',
    category: 'Excavation',
    status: 'pending',
    dueDate: '2024-08-07',
    location: 'Site Compound',
    priority: 'normal'
  },
  {
    id: 'task-003',
    name: 'Safety Inspection',
    category: 'Safety',
    status: 'completed',
    dueDate: '2024-08-05',
    location: 'Main Building',
    priority: 'normal'
  }
];

const SiteEngineerDashboard: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);

  // Truncate site name for better header layout
  const truncatedSiteName = site.name.length > 25 ? `${site.name.substring(0, 25)}...` : site.name;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50';
      case 'in-progress': return 'text-blue-600 bg-blue-50';
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'overdue': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'normal': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-[#eaede8]">
      {/* Floating Card Container */}
      <div className="p-2.5 min-w-0">
        <div className="bg-[#fdfdf9] rounded-[10px] h-[calc(100vh-20px)] flex flex-col overflow-hidden min-w-0">
          
          {/* Header */}
          <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
            {/* Site Name */}
            <div className="flex-1 min-w-0">
              <h1 className="text-base sm:text-lg font-semibold text-gray-900 truncate" title={site.name}>
                {truncatedSiteName}
              </h1>
            </div>

            {/* Right Side - Notifications and Account */}
            <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
              {/* Notification Bell */}
              <button
                onClick={() => navigate(`/sites/${siteId}/engineer/notifications`)}
                className="relative p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
                {/* Mobile: Simple red dot */}
                <span className="absolute -top-0.5 -right-0.5 bg-red-500 rounded-full h-2 w-2 sm:hidden"></span>
                {/* Desktop: Badge with number */}
                <span className="hidden sm:flex absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 items-center justify-center font-medium">
                  3
                </span>
              </button>

              {/* Account Icon */}
              <button
                onClick={() => navigate(`/sites/${siteId}/engineer/account`)}
                className="p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <User className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-auto">
            <div className="px-4 sm:px-6 py-4 sm:py-6">
              
              {/* Dashboard Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 sm:mb-8">
                {/* Tasks Card */}
                <button
                  onClick={() => navigate(`/sites/${siteId}/engineer/tasks`)}
                  className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Tasks</p>
                      <p className="mt-2 text-2xl font-semibold text-gray-900">{DASHBOARD_DATA.tasks.total}</p>
                      <p className="text-xs text-gray-500 mt-1">{DASHBOARD_DATA.tasks.inProgress} in progress</p>
                      {DASHBOARD_DATA.tasks.overdue > 0 && (
                        <p className="text-xs text-red-600 font-medium mt-1">{DASHBOARD_DATA.tasks.overdue} overdue</p>
                      )}
                    </div>
                    <div className="text-blue-600">
                      <CheckSquare className="h-6 w-6" />
                    </div>
                  </div>
                </button>

                {/* Permits Card */}
                <button
                  onClick={() => navigate(`/sites/${siteId}/engineer/permits`)}
                  className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Permits</p>
                      <p className="mt-2 text-2xl font-semibold text-gray-900">{DASHBOARD_DATA.permits.total}</p>
                      <p className="text-xs text-gray-500 mt-1">{DASHBOARD_DATA.permits.approved} approved</p>
                      {DASHBOARD_DATA.permits.pending > 0 && (
                        <p className="text-xs text-yellow-600 font-medium mt-1">{DASHBOARD_DATA.permits.pending} pending</p>
                      )}
                    </div>
                    <div className="text-green-600">
                      <FileText className="h-6 w-6" />
                    </div>
                  </div>
                </button>

                {/* Weather Card */}
                <button
                  onClick={() => navigate(`/sites/${siteId}/engineer/weather`)}
                  className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Weather</p>
                      <p className="mt-2 text-2xl font-semibold text-gray-900">{DASHBOARD_DATA.weather.current}°C</p>
                      <p className="text-xs text-gray-500 mt-1">{DASHBOARD_DATA.weather.condition}</p>
                      {DASHBOARD_DATA.weather.alerts > 0 && (
                        <p className="text-xs text-red-600 font-medium mt-1">{DASHBOARD_DATA.weather.alerts} alert</p>
                      )}
                    </div>
                    <div className="text-yellow-600">
                      <Sun className="h-6 w-6" />
                    </div>
                  </div>
                </button>
              </div>

              {/* Quick Actions */}
              <div className="mb-6 sm:mb-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Quick Actions</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* New Task */}
                  <button
                    onClick={() => navigate(`/sites/${siteId}/engineer/tasks/create`)}
                    className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Plus className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-gray-900">New Task</h3>
                        <p className="text-xs text-gray-500">Create a new task request</p>
                      </div>
                    </div>
                  </button>

                  {/* Overtime Request */}
                  <button
                    onClick={() => navigate(`/sites/${siteId}/engineer/overtime`)}
                    className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Clock className="h-6 w-6 text-orange-600" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-gray-900">Overtime</h3>
                        <p className="text-xs text-gray-500">Request overtime hours</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Recent Tasks */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Tasks</h2>
                  <button
                    onClick={() => navigate(`/sites/${siteId}/engineer/tasks`)}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1"
                  >
                    <span>View all</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>
                </div>
                
                <div className="space-y-3">
                  {RECENT_TASKS.map((task) => (
                    <div key={task.id} className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-medium text-gray-900 text-sm sm:text-base pr-2 leading-tight">{task.name}</h3>
                            {/* Mobile: Show priority as colored dot */}
                            <div className="flex items-center space-x-2 flex-shrink-0">
                              <div className={`w-2 h-2 rounded-full sm:hidden ${task.priority === 'high' ? 'bg-red-500' : 'bg-blue-500'}`}></div>
                              {/* Desktop: Show priority badge */}
                              <span className={`hidden sm:inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                                {task.priority}
                              </span>
                            </div>
                          </div>

                          {/* Mobile: Simplified info */}
                          <div className="sm:hidden">
                            <div className="text-xs text-gray-500 mb-1">{task.category}</div>
                            <div className="flex items-center justify-between text-xs text-gray-600">
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3" />
                                <span className="truncate max-w-[120px]">{task.location}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                              </div>
                            </div>
                          </div>

                          {/* Desktop: Full info */}
                          <div className="hidden sm:flex items-center space-x-4 text-sm text-gray-600">
                            <span className="text-gray-500">{task.category}</span>
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-3 w-3" />
                              <span>{task.location}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>

                        {/* Status indicator */}
                        <div className="ml-2 flex-shrink-0">
                          {/* Mobile: Status dot */}
                          <div className={`w-3 h-3 rounded-full sm:hidden ${
                            task.status === 'completed' ? 'bg-green-500' :
                            task.status === 'in-progress' ? 'bg-blue-500' :
                            task.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-500'
                          }`}></div>
                          {/* Desktop: Status badge */}
                          <span className={`hidden sm:inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                            {task.status.replace('-', ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SiteEngineerDashboard;
