import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import FloatingCard from '../components/layout/FloatingCard';
import PhotoCapture from '../components/shared/PhotoCapture';
import { inspectionFormTypes, FormStructureType, inspectionTypeMap } from '../data/inspectionFormTemplate';
import { CREATE_INSPECTION } from '../graphql/mutations';

interface InspectionFormData {
  approved: {
    value: boolean;
    remarks: string;
  };
  generalComments: string;
  inspectedBy: string;
  date: string;
  time: string;
  signature: string;
  responses: { [key: number]: { response: string; remarks: string; photos: File[] } };
}

const InspectionFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formTemplate, setFormTemplate] = useState<typeof inspectionFormTypes[0] | null>(null);
  const [formData, setFormData] = useState<InspectionFormData>({
    approved: { value: false, remarks: '' },
    generalComments: '',
    inspectedBy: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    signature: '',
    responses: {}
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [createInspection] = useMutation(CREATE_INSPECTION);

  useEffect(() => {
    if (!id) return;
    const template = inspectionFormTypes.find(form => form.id === id);
    if (template) {
      setFormTemplate(template);
      const initialResponses: { [key: number]: { response: string; remarks: string; photos: File[] } } = {};
      template.information.forEach(item => {
        initialResponses[item.serialNo] = { response: '', remarks: '', photos: [] };
      });
      setFormData(prev => ({ ...prev, responses: initialResponses }));
    }
  }, [id]);

  const handleCheckboxChange = (serialNo: number, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      responses: { ...prev.responses, [serialNo]: { ...prev.responses[serialNo], response: value ? 'YES' : 'NO' } }
    }));
  };

  const handleRemarksChange = (serialNo: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      responses: { ...prev.responses, [serialNo]: { ...prev.responses[serialNo], remarks: value } }
    }));
  };

  const handleApprovedChange = (value: boolean) => {
    setFormData(prev => ({ ...prev, approved: { ...prev.approved, value } }));
  };

  const handleInputChange = (field: keyof InspectionFormData, value: string) => {
    if (field === 'approved' || field === 'date' || field === 'time') return;
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleApprovedRemarksChange = (value: string) => {
    setFormData(prev => ({ ...prev, approved: { ...prev.approved, remarks: value } }));
  };

  const handlePhotosChange = (serialNo: number, photos: File[]) => {
    setFormData(prev => ({
      ...prev,
      responses: { ...prev.responses, [serialNo]: { ...prev.responses[serialNo], photos } }
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      if (!formData.inspectedBy.trim()) {
        toast.error('Inspector name is required');
        return;
      }
      const inspectionType = inspectionTypeMap[id || ''] || 'AIR_COMPRESSOR_MACHINE_INSPECTION';
      const inspectionItems = Object.entries(formData.responses).map(([serialNo, response]) => ({
        description: formTemplate?.information.find(item => item.serialNo === parseInt(serialNo))?.description || '',
        isTrue: response.response === 'YES',
        remarks: response.remarks || '',
        imageFiles: response.photos
      }));
      const result = await createInspection({
        variables: {
          input: {
            inspectionItems,
            approved: formData.approved.value,
            comments: formData.generalComments,
            inspectedById: 1,
            inspectionType
          }
        }
      });
      if (result.data?.createInspection) {
        toast.success('Inspection form submitted successfully!');
        navigate(-1);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Error submitting form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => navigate(-1);

  if (!formTemplate) {
    return (
      <FloatingCard title="Inspection Form">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Form Not Found</h2>
            <p className="text-gray-600 mb-4">The inspection form with ID "{id}" was not found.</p>
            <button onClick={handleBack} className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard
      title="Inspection Form"
      topBarRightActions={(
        <div className="flex items-center gap-2">
          <button onClick={handleSubmit} disabled={isSubmitting} className="inline-flex items-center px-5 h-9 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed">
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              'Submit'
            )}
          </button>
        </div>
      )}
    >
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button onClick={handleBack} className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{formTemplate.name}</h1>
                <p className="text-xs text-gray-500">Please complete all items as applicable.</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 m-4">
          <p className="text-blue-800">
            <strong><em>Note: </em></strong>
            Please tick <strong><em>YES</em></strong> or <strong><em>NO</em></strong> for all the descriptions as per the condition of the equipment onsite
          </p>
        </div>

        {/* Form Content */}
        <div className="w-full p-4">
          <div className="space-y-3">
            {/* Inspection Items */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-base font-semibold text-gray-900 mb-3">Inspection Checklist</h3>
              <div className="space-y-4">
                {formTemplate.information.map((item: FormStructureType) => (
                  <div key={item.serialNo} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                    {/* Item Header */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-2">
                          <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-600 text-xs font-medium rounded-full mr-3 flex-shrink-0">
                            {item.serialNo}
                          </span>
                          <h4 className="text-sm font-medium text-gray-900">Inspection Point {item.serialNo}</h4>
                        </div>
                        <p className="text-sm text-gray-700 leading-relaxed break-words">{item.description}</p>
                      </div>
                    </div>

                    {/* Response Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      {/* Answer */}
                      <div className="min-w-0">
                        <label className="block text-xs font-medium text-gray-700 mb-2">Answer</label>
                        <div className="flex space-x-4">
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              name={`response-${item.serialNo}`}
                              value="YES"
                              checked={formData.responses[item.serialNo]?.response === 'YES'}
                              onChange={() => handleCheckboxChange(item.serialNo, true)}
                              className="form-radio h-4 w-4 text-green-600"
                            />
                            <span className="ml-2 text-sm text-gray-700">YES</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              name={`response-${item.serialNo}`}
                              value="NO"
                              checked={formData.responses[item.serialNo]?.response === 'NO'}
                              onChange={() => handleCheckboxChange(item.serialNo, false)}
                              className="form-radio h-4 w-4 text-red-600"
                            />
                            <span className="ml-2 text-sm text-gray-700">NO</span>
                          </label>
                        </div>
                      </div>

                      {/* Remarks */}
                      <div className="min-w-0">
                        <label className="block text-xs font-medium text-gray-700 mb-2">Remarks</label>
                        <textarea
                          rows={2}
                          className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-green-500 focus:border-green-500 resize-none"
                          placeholder="Enter remarks..."
                          value={formData.responses[item.serialNo]?.remarks || ''}
                          onChange={(e) => handleRemarksChange(item.serialNo, e.target.value)}
                        />
                      </div>

                      {/* Photos */}
                      <div className="min-w-0">
                        <label className="block text-xs font-medium text-gray-700 mb-2">Photos</label>
                        <div className="w-full min-w-0">
                          <PhotoCapture
                            onPhotosChange={(photos) => handlePhotosChange(item.serialNo, photos)}
                            maxPhotos={3}
                            maxFileSize={5}
                            className="w-full min-w-0"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Approval Section */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-base font-semibold text-gray-900 mb-3">Overall Approval</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="min-w-0">
                  <label className="block text-xs font-medium text-gray-700 mb-2">Approval Status</label>
                  <div className="flex space-x-4">
                    <label className="inline-flex items-center">
                      <input type="radio" name="approved" value="true" checked={formData.approved.value === true} onChange={() => handleApprovedChange(true)} className="form-radio h-4 w-4 text-green-600" />
                      <span className="ml-2 text-sm text-gray-700">APPROVED</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input type="radio" name="approved" value="false" checked={formData.approved.value === false} onChange={() => handleApprovedChange(false)} className="form-radio h-4 w-4 text-red-600" />
                      <span className="ml-2 text-sm text-gray-700">NOT APPROVED</span>
                    </label>
                  </div>
                </div>
                <div className="min-w-0">
                  <label className="block text-xs font-medium text-gray-700 mb-2">Approval Remarks</label>
                  <textarea rows={3} className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-green-500 focus:border-green-500 resize-none" placeholder="Remarks" value={formData.approved.remarks} onChange={(e) => handleApprovedRemarksChange(e.target.value)} />
                </div>
              </div>
            </div>

            {/* General Comments */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-base font-semibold text-gray-900 mb-3">General Comments</h3>
              <textarea rows={4} className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-green-500 focus:border-green-500 resize-none" placeholder="General Comments:" value={formData.generalComments} onChange={(e) => handleInputChange('generalComments', e.target.value)} />
            </div>

            {/* Inspector Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-base font-semibold text-gray-900 mb-3">Inspector Details</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-3 min-w-0">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Inspected By *</label>
                    <input type="text" className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-green-500 focus:border-green-500" placeholder="Inspector name" value={formData.inspectedBy} onChange={(e) => handleInputChange('inspectedBy', e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm bg-gray-100 text-gray-600 cursor-not-allowed" value={formData.date} readOnly disabled />
                    <p className="text-xs text-gray-500 mt-1">Auto-generated (current date)</p>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Time</label>
                    <input type="time" className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm bg-gray-100 text-gray-600 cursor-not-allowed" value={formData.time} readOnly disabled />
                    <p className="text-xs text-gray-500 mt-1">Auto-generated (current time)</p>
                  </div>
                </div>

                <div className="min-w-0">
                  <label className="block text-xs font-medium text-gray-700 mb-1">Signature</label>
                  <textarea rows={6} className="w-full h-32 border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-green-500 focus:border-green-500 resize-none" placeholder="Digital signature or signature description" value={formData.signature} onChange={(e) => handleInputChange('signature', e.target.value)} />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => navigate(-1)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Form'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default InspectionFormPage;
