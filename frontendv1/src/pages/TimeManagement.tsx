import React, { useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import FloatingCard from "../components/layout/FloatingCard";
import AttendanceTab from "../components/time/AttendanceTab";
import SiteDevices from "../components/time/SiteDevices";
import { SiteInfo } from "../types";

// Mock data
const mockSite: SiteInfo = {
	id: "site1",
	name: "Westlands Construction Site",
	healthStatus: "green",
	workersOnSite: 42,
	activePermits: 8,
	openIncidents: 0,
	projectManager: "<PERSON>",
	location: "Waiyaki Way, Westlands, Nairobi",
	timeline: "Jan 2025 - Dec 2026",
	currentPhase: "Foundation",
	progressPercentage: 25,
	tenantId: "",
	status: "active",
	createdAt: new Date()
};

const TimeManagement: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const navigate = useNavigate();
	const [site] = useState<SiteInfo>(mockSite);

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Time Management", path: `/sites/${siteId}/time` },
	];

	const isDevicesView = location.hash === '#devices';

	return (
		<FloatingCard title="Time Management" breadcrumbs={breadcrumbs} topBarShowBack={true} topBarOnBack={() => navigate(-1)}>
			{isDevicesView ? (
				<SiteDevices siteId={siteId || ""} />
			) : (
				<AttendanceTab siteId={siteId || ""} />
			)}
		</FloatingCard>
	);
};

export default TimeManagement;
