import React, { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import {
  Filter,
  Search,
  Calendar,
  User,
  Users,
  Clock,
  AlertTriangle,
  Shield,
  FileText,
  ClipboardCheck,
  Eye,
  Plus,
  RefreshCw
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { GET_ALL_JOBS } from '../graphql/queries';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  requiredPermits: string[];
  timeForCompletion: string;
  startDate: string;
  dueDate: string;
  calculatedDueDate: string;
  requestedBy: {
    id: number;
    name: string;
  } | null;
  reviewedBy: {
    id: number;
    name: string;
  } | null;
  approvedBy: {
    id: number;
    name: string;
  } | null;
  finishedBy: {
    id: number;
    name: string;
  } | null;
  chiefEngineer: {
    id: number;
    name: string;
  } | null;
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  requestedDate: string | null;
  reviewedDate: string | null;
  approvedDate: string | null;
  finishedDate: string | null;
  blockedDate: string | null;
  createdAt: string;
  updatedAt: string;
}

const JOB_STATUSES = [
  { value: '', label: 'All Statuses' },
  { value: 'REQUESTED', label: 'Requested' },
  { value: 'BLOCKED', label: 'Blocked' },
  { value: 'PENDING_APPROVAL', label: 'Pending Approval' },
  { value: 'APPROVED', label: 'Approved' },
  { value: 'DISAPPROVED', label: 'Disapproved' },
  { value: 'FINISHED', label: 'Finished' }
];

const STATUS_COLORS = {
  REQUESTED: 'bg-yellow-100 text-yellow-800',
  BLOCKED: 'bg-red-100 text-red-800',
  PENDING_APPROVAL: 'bg-blue-100 text-blue-800',
  APPROVED: 'bg-green-100 text-green-800',
  DISAPPROVED: 'bg-red-100 text-red-800',
  FINISHED: 'bg-gray-100 text-gray-800'
};

const PERMIT_LABELS = {
  GENERAL_WORK: 'General Work',
  HOT_WORK: 'Hot Work',
  CONFINED_SPACE: 'Confined Space',
  WORK_AT_HEIGHT: 'Work at Height',
  EXCAVATION: 'Excavation'
};

const AllTasksPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());

  const { data: jobsData, loading: jobsLoading, refetch } = useQuery(GET_ALL_JOBS);

  const jobs: Job[] = jobsData?.allJobs || [];

  const filteredJobs = useMemo(() => {
    return jobs.filter(job => {
      const matchesSearch = !searchTerm || 
        job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.chiefEngineer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.requestedBy?.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = !statusFilter || job.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [jobs, searchTerm, statusFilter]);

  const toggleTaskExpansion = (jobId: number) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const getStatusLabel = (status: string) => {
    const statusObj = JOB_STATUSES.find(s => s.value === status);
    return statusObj?.label || status;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'All Tasks', path: `/sites/${siteId}/tasks/all` }
  ];

  if (jobsLoading) {
    return (
      <FloatingCard title="All Tasks" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="All Tasks" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate(`/sites/${siteId}/tasks/new`)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Task
            </button>
            <button
              onClick={() => refetch()}
              className="flex items-center px-3 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
          <div className="text-sm text-gray-500">
            Showing {filteredJobs.length} of {jobs.length} tasks
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Search tasks by title, description, or people..."
              />
            </div>
          </div>
          
          {/* Status Filter */}
          <div className="sm:w-48">
            <div className="relative">
              <Filter className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
              >
                {JOB_STATUSES.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Tasks List */}
        {filteredJobs.length === 0 ? (
          <div className="text-center py-12">
            <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {jobs.length === 0 ? 'No Tasks Found' : 'No Matching Tasks'}
            </h3>
            <p className="text-gray-500">
              {jobs.length === 0 
                ? 'There are currently no tasks in the system.' 
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredJobs.map((job) => {
              const isExpanded = expandedTasks.has(job.id);
              
              return (
                <div key={job.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="px-6 py-4 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            STATUS_COLORS[job.status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800'
                          }`}>
                            {getStatusLabel(job.status)}
                          </span>
                        </div>
                        <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                          {job.requestedBy && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              Requested by: {job.requestedBy.name}
                            </div>
                          )}
                          {job.chiefEngineer && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              Chief Engineer: {job.chiefEngineer.name}
                            </div>
                          )}
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Start: {formatDate(job.startDate)}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            Duration: {job.timeForCompletion} days
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Due: {formatDate(job.dueDate)}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => toggleTaskExpansion(job.id)}
                        className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {isExpanded ? 'Collapse' : 'View Details'}
                      </button>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="px-6 py-4 space-y-6 bg-white border-t border-gray-200">
                      {/* Task Description */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">{job.description}</p>
                      </div>

                      {/* Workers */}
                      {/* {job.workers && job.workers.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <Users className="h-4 w-4 inline mr-1" />
                            Assigned Workers ({job.workers.length})
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            {job.workers.map((worker) => (
                              <div key={worker.id} className="bg-gray-50 p-2 rounded text-sm border border-gray-200">
                                <p className="font-medium">{worker.name}</p>
                                <p className="text-gray-600">{worker.company}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )} */}

                      {/* Required Permits */}
                      {job.requiredPermits && job.requiredPermits.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <ClipboardCheck className="h-4 w-4 inline mr-1" />
                            Required Permits
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {job.requiredPermits.map((permit, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {PERMIT_LABELS[permit as keyof typeof PERMIT_LABELS] || permit}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Hazards and Control Measures */}
                      {job.hazards && job.hazards.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <AlertTriangle className="h-4 w-4 inline mr-1" />
                            Hazards & Control Measures
                          </h4>
                          <div className="space-y-4">
                            {job.hazards.map((hazard) => (
                              <div key={hazard.id} className="border border-gray-200 rounded-md p-4">
                                <div className="mb-3">
                                  <h5 className="text-sm font-medium text-gray-900 mb-1">Hazard</h5>
                                  <p className="text-sm text-gray-700 bg-orange-50 p-2 rounded">{hazard.description}</p>
                                </div>
                                {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                                      <Shield className="h-4 w-4 inline mr-1" />
                                      Control Measures
                                    </h5>
                                    <ul className="space-y-1">
                                      {hazard.controlMeasures.map((measure) => (
                                        <li key={measure.id} className="flex items-start space-x-2">
                                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                            measure.closed ? 'bg-green-500' : 'bg-yellow-500'
                                          }`}></div>
                                          <span className="text-sm text-gray-700">{measure.description}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Documents */}
                      {job.documents && job.documents.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <FileText className="h-4 w-4 inline mr-1" />
                            Documents
                          </h4>
                          <div className="space-y-2">
                            {job.documents.map((doc) => (
                              <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                                <div className="flex items-center space-x-3">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm font-medium">{doc.name}</span>
                                </div>
                                <a
                                  href={doc.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  View
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Timeline */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Timeline</h4>
                        <div className="bg-gray-50 p-3 rounded-md text-sm space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Created:</span>
                            <span className="font-medium">{formatDateTime(job.createdAt)}</span>
                          </div>
                          {job.requestedDate && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Requested:</span>
                              <span className="font-medium">{formatDateTime(job.requestedDate)}</span>
                            </div>
                          )}
                          {job.reviewedDate && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Reviewed:</span>
                              <span className="font-medium">{formatDateTime(job.reviewedDate)}</span>
                            </div>
                          )}
                          {job.approvedDate && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Approved:</span>
                              <span className="font-medium">{formatDateTime(job.approvedDate)}</span>
                            </div>
                          )}
                          {job.finishedDate && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Finished:</span>
                              <span className="font-medium">{formatDateTime(job.finishedDate)}</span>
                            </div>
                          )}
                          {job.blockedDate && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Blocked:</span>
                              <span className="font-medium text-red-600">{formatDateTime(job.blockedDate)}</span>
                            </div>
                          )}
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Last Updated:</span>
                            <span className="font-medium">{formatDateTime(job.updatedAt)}</span>
                          </div>
                        </div>
                      </div>

                      {/* People Involved */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">People Involved</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {job.requestedBy && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-xs text-gray-500 mb-1">Requested By</p>
                              <p className="text-sm font-medium">{job.requestedBy.name}</p>
                            </div>
                          )}
                          {job.chiefEngineer && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-xs text-gray-500 mb-1">Chief Engineer</p>
                              <p className="text-sm font-medium">{job.chiefEngineer.name}</p>
                            </div>
                          )}
                          {job.reviewedBy && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-xs text-gray-500 mb-1">Reviewed By</p>
                              <p className="text-sm font-medium">{job.reviewedBy.name}</p>
                            </div>
                          )}
                          {job.approvedBy && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-xs text-gray-500 mb-1">Approved By</p>
                              <p className="text-sm font-medium">{job.approvedBy.name}</p>
                            </div>
                          )}
                          {job.finishedBy && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-xs text-gray-500 mb-1">Finished By</p>
                              <p className="text-sm font-medium">{job.finishedBy.name}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </FloatingCard>
  );
};

export default AllTasksPage;
