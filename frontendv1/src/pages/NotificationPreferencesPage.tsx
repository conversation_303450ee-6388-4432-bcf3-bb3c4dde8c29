import React, { useState } from 'react';
import { 
  Bell, 
  Mail, 
  Smartphone, 
  TestTube,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useNotificationPreferences, DEFAULT_NOTIFICATION_TYPES } from '../hooks/useNotificationPreferences';
import { NotificationPriority } from '../types/notifications';
import FloatingCard from '../components/layout/FloatingCard';

const NotificationPreferencesPage: React.FC = () => {
  const { 
    preferences, 
    loading, 
    error, 
    updatePreferences, 
    sendTestNotification 
  } = useNotificationPreferences();

  const [saving, setSaving] = useState<string | null>(null);
  const [testSending, setTestSending] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handlePreferenceUpdate = async (
    notificationType: string, 
    field: string, 
    value: any
  ) => {
    setSaving(notificationType);
    try {
      await updatePreferences(notificationType, { [field]: value });
      setSuccessMessage(`${notificationType} preferences updated successfully`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Failed to update preferences:', error);
    } finally {
      setSaving(null);
    }
  };

  const handleSendTest = async () => {
    setTestSending(true);
    try {
      await sendTestNotification();
      setSuccessMessage('Test notification sent successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Failed to send test notification:', error);
    } finally {
      setTestSending(false);
    }
  };

  const getPreferenceForType = (type: string) => {
    return preferences.find(p => p.notificationType === type) || {
      notificationType: type,
      inAppEnabled: true,
      emailEnabled: true,
      smsEnabled: false,
      minimumPriority: NotificationPriority.LOW,
      doNotDisturbEnabled: false,
      doNotDisturbStart: undefined,
      doNotDisturbEnd: undefined
    };
  };

  const formatNotificationType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getNotificationDescription = (type: string) => {
    const descriptions: Record<string, string> = {
      'training_expiring': 'Notifications when worker training certifications are about to expire',
      'training_expired': 'Notifications when worker training certifications have expired',
      'training_assigned': 'Notifications when training is assigned to workers',
      'training_completed': 'Notifications when workers complete training',
      'worker_added': 'Notifications when new workers are added to the system',
      'worker_updated': 'Notifications when worker information is updated',
      'system_alert': 'Important system alerts and maintenance notifications',
      'permit_expiring': 'Notifications when work permits are about to expire',
      'permit_expired': 'Notifications when work permits have expired',
      'attendance_alert': 'Notifications about attendance issues or anomalies',
      'safety_incident': 'Notifications about safety incidents and reports'
    };
    return descriptions[type] || 'Notifications for this event type';
  };

  if (loading) {
    return (
      <FloatingCard title="Notification Preferences">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-3 text-gray-600">Loading preferences...</span>
        </div>
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Notification Preferences">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-400" />
          <span className="ml-3 text-red-600">Failed to load preferences</span>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Notification Preferences">
      {/* Header */}
      <div className="mb-8">
        <p className="text-gray-600 mb-4">
          Configure how and when you receive notifications for different types of events.
        </p>
        
        {successMessage && (
          <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg mb-4">
            <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
            <span className="text-green-800">{successMessage}</span>
          </div>
        )}

        <div className="flex items-center space-x-4">
          <button
            onClick={handleSendTest}
            disabled={testSending}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400 rounded-lg transition-colors"
          >
            {testSending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <TestTube className="h-4 w-4 mr-2" />
            )}
            Send Test Notification
          </button>
        </div>
      </div>

      {/* Notification Types */}
      <div className="space-y-6">
        {DEFAULT_NOTIFICATION_TYPES.map((type) => {
          const preference = getPreferenceForType(type);
          const isSaving = saving === type;

          return (
            <div key={type} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {formatNotificationType(type)}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {getNotificationDescription(type)}
                  </p>
                </div>
                {isSaving && (
                  <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                )}
              </div>

              {/* Channel Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* In-App Notifications */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Bell className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm font-medium text-gray-900">In-App</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preference.inAppEnabled}
                      onChange={(e) => handlePreferenceUpdate(type, 'inAppEnabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>

                {/* Email Notifications */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm font-medium text-gray-900">Email</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preference.emailEnabled}
                      onChange={(e) => handlePreferenceUpdate(type, 'emailEnabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>

                {/* SMS Notifications */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Smartphone className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm font-medium text-gray-900">SMS</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preference.smsEnabled}
                      onChange={(e) => handlePreferenceUpdate(type, 'smsEnabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>
              </div>

              {/* Priority Setting */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Priority Level
                </label>
                <select
                  value={preference.minimumPriority}
                  onChange={(e) => handlePreferenceUpdate(type, 'minimumPriority', e.target.value as NotificationPriority)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value={NotificationPriority.LOW}>Low and above</option>
                  <option value={NotificationPriority.MEDIUM}>Medium and above</option>
                  <option value={NotificationPriority.HIGH}>High and above</option>
                  <option value={NotificationPriority.CRITICAL}>Critical only</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Only notifications at or above this priority level will be delivered
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </FloatingCard>
  );
};

export default NotificationPreferencesPage;
