import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  Mail,
  Phone,
  Globe,
  Building,
  User,
  X,
  Search,
  Plus,
  Save,
  AlertCircle,
  CheckCircle,
  Info,
  ArrowLeft
} from 'lucide-react';
import { toast } from 'react-toastify';
import FloatingCard from '../components/layout/FloatingCard';
import {
  trainingPrograms,
  mockWorkers,
  mockTrainingProviders,
  searchWorkers,
  getWorkersByDepartment,
  type TrainingSession,
  type Worker,
  type TrainingProvider
} from '../data/trainingData';
import { useTopBarConfig } from '../hooks/useTopBarContext';

interface TrainingSessionSchedulingPageProps {}

const TrainingSessionSchedulingPage: React.FC<TrainingSessionSchedulingPageProps> = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const { setTitle, setShowBack, setMinimal } = useTopBarConfig();

  // Form state
  const [formData, setFormData] = useState({
    programId: '',
    providerId: '', // Changed to use preloaded provider
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    modeOfDelivery: 'onsite' as 'online' | 'onsite' | 'hybrid',
    maxParticipants: 20,
    trainingLocation: ''
  });

  // Worker selection state
  const [selectedWorkers, setSelectedWorkers] = useState<Worker[]>([]);
  const [workerSearchQuery, setWorkerSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Worker[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Form validation and submission state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Get selected program and provider details
  const selectedProgram = trainingPrograms.find(p => p.id === formData.programId);
  const selectedProvider = mockTrainingProviders.find(p => p.id === formData.providerId);

  // Search workers function
  const handleWorkerSearch = (query: string) => {
    setWorkerSearchQuery(query);
    if (query.length >= 2) {
      const results = searchWorkers(query);
      setSearchResults(results.filter(worker => 
        !selectedWorkers.some(selected => selected.id === worker.id)
      ));
      setShowSearchResults(true);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  };

  // Add worker to selection
  const addWorker = (worker: Worker) => {
    if (!selectedWorkers.some(w => w.id === worker.id)) {
      setSelectedWorkers([...selectedWorkers, worker]);
    }
    setWorkerSearchQuery('');
    setShowSearchResults(false);
  };

  // Remove worker from selection
  const removeWorker = (workerId: string) => {
    setSelectedWorkers(selectedWorkers.filter(w => w.id !== workerId));
  };

  // Add workers by department
  const addWorkersByDepartment = (department: string) => {
    const departmentWorkers = getWorkersByDepartment(department);
    const newWorkers = departmentWorkers.filter(worker => 
      !selectedWorkers.some(selected => selected.id === worker.id)
    );
    setSelectedWorkers([...selectedWorkers, ...newWorkers]);
  };

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.programId) {
      newErrors.programId = 'Please select a training program';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Please select a start date';
    }

    if (!formData.startTime) {
      newErrors.startTime = 'Please select a start time';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'Please select an end date';
    }

    if (!formData.endTime) {
      newErrors.endTime = 'Please select an end time';
    }

    if (formData.maxParticipants < 1) {
      newErrors.maxParticipants = 'Maximum participants must be at least 1';
    }

    if (selectedWorkers.length > formData.maxParticipants) {
      newErrors.workers = `Cannot enroll ${selectedWorkers.length} workers when maximum capacity is ${formData.maxParticipants}`;
    }

    if (!formData.providerId) {
      newErrors.providerId = 'Please select a training provider';
    }

    if (!formData.trainingLocation) {
      newErrors.trainingLocation = 'Please enter training location';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the validation errors before submitting.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create new training session
      const newSession: TrainingSession = {
        id: `ts-${Date.now()}`,
        programId: formData.programId,
        programName: selectedProgram?.name || '',
        startDate: new Date(`${formData.startDate}T${formData.startTime}`),
        endDate: new Date(`${formData.endDate}T${formData.endTime}`),
        modeOfDelivery: formData.modeOfDelivery,
        maxParticipants: formData.maxParticipants,
        currentEnrollment: selectedWorkers.length,
        provider: {
          name: selectedProvider?.name || '',
          accreditations: selectedProvider?.accreditations || [],
          contactDetails: selectedProvider?.contactDetails || {
            email: '',
            phone: '',
            address: ''
          },
          trainingLocation: formData.trainingLocation
        },
        status: 'scheduled',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log('New training session created:', newSession);
      console.log('Enrolled workers:', selectedWorkers);

      toast.success('Training session created successfully!');

      // Redirect to training calendar after success
      setTimeout(() => {
        navigate(`/sites/${siteId}/training#calendar`);
      }, 1500);

    } catch (error) {
      console.error('Error creating training session:', error);
      toast.error('Failed to create training session. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Set up TopBar configuration
  useEffect(() => {
    setTitle('Schedule Training Session');
    setShowBack(true);
    setMinimal(true); // Enable minimal mode to hide distracting elements
  }, [setTitle, setShowBack, setMinimal]);

  // Get unique departments for quick selection
  const departments = Array.from(new Set(mockWorkers.map(w => w.department)));

  const handleBack = () => {
    navigate(`/sites/${siteId}/training#calendar`);
  };

  return (
    <FloatingCard
      title="Schedule Training Session"
      topBarShowBack={true}
      topBarOnBack={handleBack}
      topBarMinimal={true}
      topBarRightActions={
        <>
          <button
            onClick={handleBack}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Training Session
              </>
            )}
          </button>
        </>
      }
    >
      <div className="space-y-6">
        {/* Page Description */}
        <div className="text-gray-600">
          Create a new training session and enroll workers
        </div>

        {/* Training Logistics Section */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Training Logistics</h2>
            <p className="text-sm text-gray-600 mt-1">What, when, and where the training will take place</p>
          </div>

        <div className="p-6 space-y-6">
          {/* Program Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Training Program *
            </label>
            <select
              value={formData.programId}
              onChange={(e) => {
                setFormData({ ...formData, programId: e.target.value, providerId: '' });
              }}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.programId ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select a registered training program</option>
              {trainingPrograms.map(program => (
                <option key={program.id} value={program.id}>
                  {program.name}
                </option>
              ))}
            </select>
            {errors.programId && (
              <p className="text-red-600 text-sm mt-1">{errors.programId}</p>
            )}
            {selectedProgram && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">{selectedProgram.description}</p>
                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                  <span>Duration: {selectedProgram.duration} hours</span>
                  <span>Category: {selectedProgram.category}</span>
                  <span>Validity: {selectedProgram.validityPeriod} months</span>
                </div>
              </div>
            )}
          </div>

          {/* Provider Selection */}
          {formData.programId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Provider *
              </label>
              <select
                value={formData.providerId}
                onChange={(e) => setFormData({ ...formData, providerId: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.providerId ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select a pre-approved provider</option>
                {mockTrainingProviders
                  .filter(provider => {
                    const isActive = provider.isActive;
                    const isApproved = selectedProgram?.approvedProviders?.includes(provider.id) || false;
                    return isActive && isApproved;
                  })
                  .map(provider => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name} (Rating: {provider.rating}/5)
                    </option>
                  ))}
              </select>
              {errors.providerId && (
                <p className="text-red-600 text-sm mt-1">{errors.providerId}</p>
              )}
              {selectedProvider && (
                <div className="mt-2 p-3 bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-600 space-y-1">
                    <div><strong>Specializations:</strong> {selectedProvider.specializations.join(', ')}</div>
                    <div><strong>Accreditations:</strong> {selectedProvider.accreditations.join(', ')}</div>
                    <div><strong>Contact:</strong> {selectedProvider.contactDetails.email} | {selectedProvider.contactDetails.phone}</div>
                    <div><strong>Rating:</strong> {selectedProvider.rating}/5</div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.startDate ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.startDate && (
                <p className="text-red-600 text-sm mt-1">{errors.startDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time *
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.startTime ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.startTime && (
                <p className="text-red-600 text-sm mt-1">{errors.startTime}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Date *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.endDate ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.endDate && (
                <p className="text-red-600 text-sm mt-1">{errors.endDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Time *
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.endTime ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.endTime && (
                <p className="text-red-600 text-sm mt-1">{errors.endTime}</p>
              )}
            </div>
          </div>

          {/* Mode of Delivery */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mode of Delivery *
            </label>
            <div className="grid grid-cols-3 gap-4">
              {[
                { value: 'onsite', label: 'On-site', icon: Building },
                { value: 'online', label: 'Online', icon: Globe },
                { value: 'hybrid', label: 'Hybrid', icon: Users }
              ].map(({ value, label, icon: Icon }) => (
                <label
                  key={value}
                  className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                    formData.modeOfDelivery === value
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="modeOfDelivery"
                    value={value}
                    checked={formData.modeOfDelivery === value}
                    onChange={(e) => setFormData({ ...formData, modeOfDelivery: e.target.value as any })}
                    className="sr-only"
                  />
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">{label}</span>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Capacity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Participants *
            </label>
            <input
              type="number"
              min="1"
              value={formData.maxParticipants}
              onChange={(e) => setFormData({ ...formData, maxParticipants: parseInt(e.target.value) || 1 })}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.maxParticipants ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errors.maxParticipants && (
              <p className="text-red-600 text-sm mt-1">{errors.maxParticipants}</p>
            )}
          </div>

          {/* Training Location */}
          {formData.providerId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Location *
              </label>
              <input
                type="text"
                value={formData.trainingLocation}
                onChange={(e) => setFormData({ ...formData, trainingLocation: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.trainingLocation ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Training Room A, Main Building"
              />
              {errors.trainingLocation && (
                <p className="text-red-600 text-sm mt-1">{errors.trainingLocation}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Specify the exact location where the training will be conducted
              </p>
            </div>
          )}
          </div>
        </div>

        {/* Worker Selection Section */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Worker Selection</h2>
            <p className="text-sm text-gray-600 mt-1">Select workers to enroll in this training session</p>
          </div>

        <div className="p-6 space-y-6">
          {/* Search and Add Workers */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Workers
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={workerSearchQuery}
                onChange={(e) => handleWorkerSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Search by name, email, or employee ID..."
              />
            </div>

            {/* Search Results */}
            {showSearchResults && searchResults.length > 0 && (
              <div className="mt-2 border border-gray-200 rounded-md bg-white shadow-lg max-h-60 overflow-y-auto">
                {searchResults.map(worker => (
                  <div
                    key={worker.id}
                    className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    onClick={() => addWorker(worker)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{worker.name}</div>
                        <div className="text-sm text-gray-500">{worker.email}</div>
                        <div className="text-xs text-gray-400">{worker.department} • {worker.position}</div>
                      </div>
                      <Plus className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {showSearchResults && searchResults.length === 0 && workerSearchQuery.length >= 2 && (
              <div className="mt-2 p-3 text-center text-gray-500 text-sm">
                No workers found matching "{workerSearchQuery}"
              </div>
            )}
          </div>

          {/* Quick Add by Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Add by Department
            </label>
            <div className="flex flex-wrap gap-2">
              {departments.map(department => (
                <button
                  key={department}
                  onClick={() => addWorkersByDepartment(department)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                >
                  Add {department}
                </button>
              ))}
            </div>
          </div>

          {/* Selected Workers */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selected Workers ({selectedWorkers.length}/{formData.maxParticipants})
            </label>
            {errors.workers && (
              <p className="text-red-600 text-sm mb-2">{errors.workers}</p>
            )}
            
            {selectedWorkers.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No workers selected</p>
                <p className="text-sm text-gray-400">Search and add workers above</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {selectedWorkers.map(worker => (
                  <div
                    key={worker.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{worker.name}</div>
                        <div className="text-sm text-gray-500">{worker.department} • {worker.position}</div>
                        <div className="text-xs text-gray-400">{worker.email}</div>
                      </div>
                    </div>
                    <button
                      onClick={() => removeWorker(worker.id)}
                      className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Worker Summary */}
          {selectedWorkers.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-blue-800">Enrollment Summary</h3>
                  <div className="mt-2 text-sm text-blue-700 space-y-1">
                    <p>• {selectedWorkers.length} workers selected for enrollment</p>
                    <p>• Maximum capacity: {formData.maxParticipants} participants</p>
                    {selectedWorkers.length > formData.maxParticipants && (
                      <p className="text-red-600 font-medium">
                        ⚠️ Too many workers selected for the specified capacity
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>


      </div>
    </FloatingCard>
  );
};

export default TrainingSessionSchedulingPage;
