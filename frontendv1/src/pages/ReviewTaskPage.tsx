import React, { useState } from 'react';
import { TrainingDocUploader } from '../components/workers/CreateWorkerForm';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ClipboardCheck,
  Save,
  X,
  Plus,
  Minus,
  Ban
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { REVIEW_JOB, BLOCK_JOB } from '../graphql/mutations';
import { GET_JOB_BY_ID, GET_ALL_TRAININGS } from '../graphql/queries';
import { DocumentFileInput, PermitType, ReviewJobInput } from '../types/graphql';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  timeForCompletion: string;
  startDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  chiefEngineer: {
    id: number;
    name: string;
  };
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  requestedDate: string;
  createdAt: string;
}

interface Hazard {
  description: string;
  controlMeasures: string[];
}

// interface DocumentFile {
//   name: string;
//   file: File;
// }

interface TaskReview {
  jobId: number;
  reviewedById: number;
  hazards: Hazard[];
  requiredPermits: PermitType[];
  ppEs: string[];
  precautionsRequired: string[];
  modesOfAccessToBeUsed: string[];
  trainingIds: number[];
  fireExtinguishers: string[];
  excavationProtectionSystems: string[];
  depthOfExcavation: string;
  excavationEquipmentsToBeUsed: string[];
  natureOfHotWork: string[];
  documents: DocumentFileInput[];
  isBlocked: boolean;
}

const PERMIT_TYPES: PermitType[] = [
  "GENERAL_WORK_PERMIT",
  "HOT_WORK_PERMIT",
  "CONFINED_SPACE_ENTRY_PERMIT",
  "WORK_AT_HEIGHT_PERMIT",
  "EXCAVATION_PERMIT",
];

const PERMIT_LABELS = {
  "GENERAL_WORK_PERMIT": "General Work Permit",
  "HOT_WORK_PERMIT": "Hot Work Permit",
  "CONFINED_SPACE_ENTRY_PERMIT": 'Confined Space Entry Permit',
  "WORK_AT_HEIGHT_PERMIT": 'Work at Height Permit',
  "EXCAVATION_PERMIT": 'Excavation Permit',
};

const FIRE_EXTINGUISHER_TYPES = [
  'Water',
  'Foam',
  'CO2 (Carbon Dioxide)',
  'Dry Chemical Powder',
  'Wet Chemical',
  'Clean Agent (Halon/FM-200)',
  'Class K (Kitchen)',
];

const ReviewTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, id } = useParams<{ siteId: string; id: string }>();

  const [taskReview, setTaskReview] = useState<TaskReview>({
    jobId: parseInt(id || '0'),
    reviewedById: 1, // Default reviewer ID - should be updated based on current user
    hazards: [],
    requiredPermits: [],
    ppEs: [''], // Start with one empty field
    precautionsRequired: [''], // Start with one empty field
    modesOfAccessToBeUsed: [], // Start empty for optional field
    trainingIds: [],
    fireExtinguishers: [],
    excavationProtectionSystems: [],
    depthOfExcavation: '',
    excavationEquipmentsToBeUsed: [],
    natureOfHotWork: [],
    documents: [],
    isBlocked: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const [job, setJob] = useState<Job | null>(null);

  const { data: jobData, loading: jobLoading } = useQuery(GET_JOB_BY_ID, {
    variables: { id: parseInt(id || '0') },
    skip: !id,
    onError: (error) => {
      console.error('Error fetching job:', error);
      toast.error('Failed to load job details.');
    },

  });

  const { data: trainingsData, loading: trainingsLoading } = useQuery(GET_ALL_TRAININGS, {
    onError: (error) => {
      console.error('Error fetching trainings:', error);
    }
  });
  const job: Job | null = jobData?.jobById[0] || null;

  const [reviewJob] = useMutation(REVIEW_JOB, {
    onCompleted: () => {
      toast.success('Task reviewed successfully!');
      navigate(`/sites/${siteId}/tasks`);
    },
    onError: (error) => {
      toast.error(`Failed to review task: ${error.message}`);
    }
  });

  const [blockJob] = useMutation(BLOCK_JOB, {
    onCompleted: () => {
      toast.success('Task blocked successfully!');
      navigate(`/sites/${siteId}/tasks`);
    },
    onError: (error) => {
      toast.error(`Failed to block task: ${error.message}`);
    }
  });

  // const job: Job | null = jobData?.jobById || null;

  // Helper functions for managing the single task review
  const updateTaskReview = (updates: Partial<TaskReview>) => {
    setTaskReview(prev => ({ ...prev, ...updates }));
  };

  const addHazard = () => {
    updateTaskReview({
      hazards: [...taskReview.hazards, { description: '', controlMeasures: [''] }]
    });
  };

  const removeHazard = (hazardIndex: number) => {
    updateTaskReview({
      hazards: taskReview.hazards.filter((_, index) => index !== hazardIndex)
    });
  };

  const updateHazard = (hazardIndex: number, description: string) => {
    const updatedHazards = [...taskReview.hazards];
    updatedHazards[hazardIndex] = { ...updatedHazards[hazardIndex], description };
    updateTaskReview({ hazards: updatedHazards });
  };

  const addControlMeasure = (hazardIndex: number) => {
    const updatedHazards = [...taskReview.hazards];
    updatedHazards[hazardIndex] = {
      ...updatedHazards[hazardIndex],
      controlMeasures: [...updatedHazards[hazardIndex].controlMeasures, '']
    };
    updateTaskReview({ hazards: updatedHazards });
  };

  const removeControlMeasure = (hazardIndex: number, measureIndex: number) => {
    const updatedHazards = [...taskReview.hazards];
    updatedHazards[hazardIndex] = {
      ...updatedHazards[hazardIndex],
      controlMeasures: updatedHazards[hazardIndex].controlMeasures.filter((_, index) => index !== measureIndex)
    };
    updateTaskReview({ hazards: updatedHazards });
  };

  const updateControlMeasure = (hazardIndex: number, measureIndex: number, value: string) => {
    const updatedHazards = [...taskReview.hazards];
    const updatedMeasures = [...updatedHazards[hazardIndex].controlMeasures];
    updatedMeasures[measureIndex] = value;
    updatedHazards[hazardIndex] = {
      ...updatedHazards[hazardIndex],
      controlMeasures: updatedMeasures
    };
    updateTaskReview({ hazards: updatedHazards });
  };

  const handleDocumentUpload = (file: File, name: string) => {
    updateTaskReview({
      documents: [...taskReview.documents, { name, file, isPublic: true }]
    });
  };

  const removeDocument = (docIndex: number) => {
    updateTaskReview({
      documents: taskReview.documents.filter((_, index) => index !== docIndex)
    });
  };

  const togglePermit = (permit: PermitType) => {
    const updatedPermits = taskReview.requiredPermits.includes(permit)
      ? taskReview.requiredPermits.filter(p => p !== permit)
      : [...taskReview.requiredPermits, permit];
    updateTaskReview({ requiredPermits: updatedPermits });
  };

  const toggleBlockStatus = () => {
    updateTaskReview({ isBlocked: !taskReview.isBlocked });
  };

  const handleSaveChanges = async () => {
    if (!job) {
      toast.error('Job not found.');
      return;
    }

    if (taskReview.isBlocked) {
      // Block the job
      setIsSubmitting(true);
      try {
        await blockJob({
          variables: {
            input: {
              jobId: job.id,
              blockedById: 1 // Using 1 as per requirement
            }
          }
        });
      } catch (error) {
        console.error('Error blocking job:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Review the job - check if at least some review content is provided
      const hasContent =
        taskReview.hazards.length > 0 ||
        taskReview.documents.length > 0 ||
        taskReview.requiredPermits.length > 0 ||
        taskReview.ppEs.some(ppe => ppe.trim() !== '') ||
        taskReview.precautionsRequired.some(p => p.trim() !== '') ||
        taskReview.modesOfAccessToBeUsed.some(m => m.trim() !== '') ||
        taskReview.fireExtinguishers.some(f => f.trim() !== '') ||
        taskReview.excavationProtectionSystems.some(e => e.trim() !== '') ||
        taskReview.depthOfExcavation.trim() !== '' ||
        taskReview.excavationEquipmentsToBeUsed.some(e => e.trim() !== '') ||
        taskReview.natureOfHotWork.some(n => n.trim() !== '') ||
        taskReview.trainingIds.length > 0;

      if (!hasContent) {
        toast.error('Please add at least some review content before submitting.');
        return;
      }

      // Prepare the input data with proper handling of optional fields
      const filteredHazards = taskReview.hazards
        .map(h => ({
          description: h.description,
          controlMeasures: h.controlMeasures.filter(cm => cm.trim() !== '')
        }))
        .filter(h => h.description.trim() !== '');

      const filteredPPEs = taskReview.ppEs.filter(ppe => ppe.trim() !== '');
      const filteredPrecautions = taskReview.precautionsRequired.filter(p => p.trim() !== '');
      const filteredTrainingIds = taskReview.trainingIds.filter(id => id > 0);

      // Handle optional fields - only include if they have content
      const filteredModesOfAccess = taskReview.modesOfAccessToBeUsed.filter(m => m.trim() !== '');
      const filteredFireExtinguishers = taskReview.fireExtinguishers.filter(f => f.trim() !== '');
      const filteredExcavationProtection = taskReview.excavationProtectionSystems.filter(e => e.trim() !== '');
      const filteredExcavationEquipment = taskReview.excavationEquipmentsToBeUsed.filter(e => e.trim() !== '');
      const filteredNatureOfHotWork = taskReview.natureOfHotWork.filter(n => n.trim() !== '');

      const reviewInput: Partial<ReviewJobInput> = {
        jobId: job.id,
        reviewedById: taskReview.reviewedById,
        hazards: filteredHazards,
        requiredPermits: taskReview.requiredPermits,
        ppEs: filteredPPEs,
        precautionsRequired: filteredPrecautions,
        trainingIds: filteredTrainingIds,
        documents: taskReview.documents
      };

      // Only add optional fields if they have content
      if (filteredModesOfAccess.length > 0) {
        reviewInput.modesOfAccessToBeUsed = filteredModesOfAccess;
      }

      if (filteredFireExtinguishers.length > 0) {
        reviewInput.fireExtinguishers = filteredFireExtinguishers;
      }

      if (filteredExcavationProtection.length > 0) {
        reviewInput.excavationProtectionSystems = filteredExcavationProtection;
      }

      if (taskReview.depthOfExcavation.trim() !== '') {
        reviewInput.depthOfExcavation = taskReview.depthOfExcavation.trim();
      }

      if (filteredExcavationEquipment.length > 0) {
        reviewInput.excavationEquipmentsToBeUsed = filteredExcavationEquipment;
      }

      if (filteredNatureOfHotWork.length > 0) {
        reviewInput.natureOfHotWork = filteredNatureOfHotWork;
      }

      setIsSubmitting(true);
      try {
        await reviewJob({
          variables: {
            input: reviewInput
          }
        });
      } catch (error) {
        console.error('Error reviewing job:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Review Task', path: `/sites/${siteId}/tasks/${id}/review` }
  ];

  if (jobLoading) {
    return (
      <FloatingCard title="Review Task" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (!job) {
    return (
      <FloatingCard title="Review Task" breadcrumbs={breadcrumbs}>
        <div className="text-center py-12">
          <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Task Not Found</h3>
          <p className="text-gray-600">The requested task could not be found.</p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Review Task" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Job Details */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">{job.title}</h2>
            {taskReview.isBlocked && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <Ban className="h-3 w-3 mr-1" />
                Blocked
              </span>
            )}
          </div>
          {/* {console.log(job) && null} */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600 mb-2"><strong>Description:</strong></p>
              <p className="text-gray-900">{job.description || 'No description provided'}</p>
            </div>
            <div className="space-y-2">
              <div>
                <span className="text-gray-600">Chief Engineer: </span>
                <span className="text-gray-900">{job.chiefEngineer?.name || '—'}</span>
              </div>
              <div>
                <span className="text-gray-600">Start Date: </span>
                <span className="text-gray-900">{job.startDate ? new Date(job.startDate).toLocaleDateString() : '—'}</span>
              </div>
              <div>
                <span className="text-gray-600">Duration: </span>
                <span className="text-gray-900">{job.timeForCompletion || '—'}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Review Form */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Details</h3>
          <p className="text-sm text-gray-600 mb-6">
            Complete the safety review by adding hazards, required permits, PPE, and other safety requirements.
            Fields will appear based on the permits you select.
          </p>

          {/* Block Status Toggle */}
          <div className="mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={taskReview.isBlocked}
                onChange={toggleBlockStatus}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Block this task</span>
            </label>
            {taskReview.isBlocked && (
              <p className="mt-1 text-sm text-red-600">
                This task will be blocked and cannot proceed until unblocked.
              </p>
            )}
          </div>

          {!taskReview.isBlocked && (
            <>
              {/* Hazards Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-md font-medium text-gray-900">Hazards & Control Measures</h4>
                  <button
                    type="button"
                    onClick={addHazard}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Hazard
                  </button>
                </div>

                {taskReview.hazards.map((hazard, hazardIndex) => (
                  <div key={hazardIndex} className="border border-gray-200 rounded-md p-4 mb-3">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Hazard Description
                        </label>
                        <textarea
                          value={hazard.description}
                          onChange={(e) => updateHazard(hazardIndex, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={2}
                          placeholder="Describe the hazard..."
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeHazard(hazardIndex)}
                        className="ml-3 text-red-500 hover:text-red-700"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Control Measures
                        </label>
                        <button
                          type="button"
                          onClick={() => addControlMeasure(hazardIndex)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          <Plus className="h-3 w-3 inline mr-1" />
                          Add Measure
                        </button>
                      </div>
                      {hazard.controlMeasures.map((measure, measureIndex) => (
                        <div key={measureIndex} className="flex items-center space-x-2 mb-2">
                          <input
                            type="text"
                            value={measure}
                            onChange={(e) => updateControlMeasure(hazardIndex, measureIndex, e.target.value)}
                            className="flex-1 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Control measure..."
                          />
                          <button
                            type="button"
                            onClick={() => removeControlMeasure(hazardIndex, measureIndex)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                {taskReview.hazards.length === 0 && (
                  <div className="text-center py-4 text-gray-500 border-2 border-dashed border-gray-300 rounded-md">
                    <p className="text-sm">No hazards added yet. Click "Add Hazard" to identify potential risks.</p>
                  </div>
                )}
              </div>

              {/* Required Permits Section */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Required Permits</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {PERMIT_TYPES.map((permit) => (
                    <label key={permit} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={taskReview.requiredPermits.includes(permit)}
                        onChange={() => togglePermit(permit)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {PERMIT_LABELS[permit]}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* PPE Section */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Personal Protective Equipment (PPE)</h4>
                <div className="space-y-3">
                  {taskReview.ppEs.map((ppe, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <textarea
                        value={ppe}
                        onChange={(e) => {
                          const newPPEs = [...taskReview.ppEs];
                          newPPEs[index] = e.target.value;
                          updateTaskReview({ ppEs: newPPEs });
                        }}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        placeholder="Enter PPE requirement (e.g., Hard hat, safety glasses, steel-toed boots)..."
                        rows={2}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPPEs = taskReview.ppEs.filter((_, i) => i !== index);
                          updateTaskReview({ ppEs: newPPEs });
                        }}
                        className="text-red-500 hover:text-red-700 mt-2"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => updateTaskReview({ ppEs: [...taskReview.ppEs, ''] })}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add PPE
                  </button>
                </div>
              </div>

              {/* Precautions Required Section */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Precautions Required</h4>
                <div className="space-y-3">
                  {taskReview.precautionsRequired.map((precaution, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <textarea
                        value={precaution}
                        onChange={(e) => {
                          const newPrecautions = [...taskReview.precautionsRequired];
                          newPrecautions[index] = e.target.value;
                          updateTaskReview({ precautionsRequired: newPrecautions });
                        }}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        placeholder="Enter safety precaution (e.g., Ensure proper ventilation, maintain safe distance from electrical equipment)..."
                        rows={2}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPrecautions = taskReview.precautionsRequired.filter((_, i) => i !== index);
                          updateTaskReview({ precautionsRequired: newPrecautions });
                        }}
                        className="text-red-500 hover:text-red-700 mt-2"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => updateTaskReview({ precautionsRequired: [...taskReview.precautionsRequired, ''] })}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Precaution Required
                  </button>
                </div>
              </div>

              {/* Training Requirements Section */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Required Trainings</h4>
                {trainingsLoading ? (
                  <p className="text-gray-500">Loading trainings...</p>
                ) : (
                  <div className="space-y-2">
                    {taskReview.trainingIds.map((trainingId, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <select
                          value={trainingId}
                          onChange={(e) => {
                            const newTrainingIds = [...taskReview.trainingIds];
                            newTrainingIds[index] = parseInt(e.target.value) || 0;
                            updateTaskReview({ trainingIds: newTrainingIds });
                          }}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={0}>Select a training...</option>
                          {trainingsData?.allTrainings?.map((training: any) => (
                            <option key={training.id} value={training.id}>
                              {training.name}
                            </option>
                          ))}
                        </select>
                        <button
                          type="button"
                          onClick={() => {
                            const newTrainingIds = taskReview.trainingIds.filter((_, i) => i !== index);
                            updateTaskReview({ trainingIds: newTrainingIds });
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => updateTaskReview({ trainingIds: [...taskReview.trainingIds, 0] })}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Training
                    </button>
                  </div>
                )}
              </div>

              {/* Modes of Access Section */}


              {/* Fire Extinguishers Section */}


              {/* Documents Section */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Supporting Documents</h4>
                <TrainingDocUploader
                  trainingId={job.id}
                  onAdd={(_trainingId: number, file: File, name: string) => handleDocumentUpload(file, name)}
                />

                {taskReview.documents.length > 0 && (
                  <div className="mt-3">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Uploaded Documents:</h5>
                    <div className="space-y-2">
                      {taskReview.documents.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="text-sm text-gray-700">{doc.name}</span>
                          <button
                            type="button"
                            onClick={() => removeDocument(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Excavation-Specific Fields */}
              {taskReview.requiredPermits.includes('EXCAVATION_PERMIT') && (
                <>
                  <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-md">
                    <h3 className="text-lg font-medium text-orange-800 mb-4">Excavation Permit Requirements</h3>

                    {/* Excavation Protection Systems Section */}
                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Excavation Protection Systems</h4>
                      <div className="space-y-2">
                        {taskReview.excavationProtectionSystems.map((system, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={system}
                              onChange={(e) => {
                                const newSystems = [...taskReview.excavationProtectionSystems];
                                newSystems[index] = e.target.value;
                                updateTaskReview({ excavationProtectionSystems: newSystems });
                              }}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Enter protection system..."
                            />
                            <button
                              type="button"
                              onClick={() => {
                                const newSystems = taskReview.excavationProtectionSystems.filter((_, i) => i !== index);
                                updateTaskReview({ excavationProtectionSystems: newSystems });
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => updateTaskReview({ excavationProtectionSystems: [...taskReview.excavationProtectionSystems, ''] })}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Protection System
                        </button>
                      </div>
                    </div>

                    {/* Depth of Excavation Section */}
                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Depth of Excavation</h4>
                      <input
                        type="text"
                        value={taskReview.depthOfExcavation}
                        onChange={(e) => updateTaskReview({ depthOfExcavation: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter excavation depth (e.g., 2.5 meters)"
                      />
                    </div>

                    {/* Excavation Equipment Section */}
                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Excavation Equipment to be Used</h4>
                      <div className="space-y-2">
                        {taskReview.excavationEquipmentsToBeUsed.map((equipment, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={equipment}
                              onChange={(e) => {
                                const newEquipment = [...taskReview.excavationEquipmentsToBeUsed];
                                newEquipment[index] = e.target.value;
                                updateTaskReview({ excavationEquipmentsToBeUsed: newEquipment });
                              }}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Enter equipment..."
                            />
                            <button
                              type="button"
                              onClick={() => {
                                const newEquipment = taskReview.excavationEquipmentsToBeUsed.filter((_, i) => i !== index);
                                updateTaskReview({ excavationEquipmentsToBeUsed: newEquipment });
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => updateTaskReview({ excavationEquipmentsToBeUsed: [...taskReview.excavationEquipmentsToBeUsed, ''] })}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Equipment
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
              {
                taskReview.requiredPermits.includes('WORK_AT_HEIGHT_PERMIT') && (
                  <>
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                      <h3 className="text-lg font-medium text-red-800 mb-4">Work at Height Permit Requirements</h3>
                      <div className="mb-6">
                        <h4 className="text-md font-medium text-gray-900 mb-3">Modes of Access to be Used</h4>
                        <div className="space-y-3">
                          {taskReview.modesOfAccessToBeUsed.map((mode, index) => (
                            <div key={index} className="flex items-start space-x-2">
                              <textarea
                                value={mode}
                                onChange={(e) => {
                                  const newModes = [...taskReview.modesOfAccessToBeUsed];
                                  newModes[index] = e.target.value;
                                  updateTaskReview({ modesOfAccessToBeUsed: newModes });
                                }}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                placeholder="Enter access mode (e.g., Scaffolding, ladder access, crane platform, mobile elevated work platform)..."
                                rows={2}
                              />
                              <button
                                type="button"
                                onClick={() => {
                                  const newModes = taskReview.modesOfAccessToBeUsed.filter((_, i) => i !== index);
                                  updateTaskReview({ modesOfAccessToBeUsed: newModes });
                                }}
                                className="text-red-500 hover:text-red-700 mt-2"
                              >
                                <Minus className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            type="button"
                            onClick={() => updateTaskReview({ modesOfAccessToBeUsed: [...taskReview.modesOfAccessToBeUsed, ''] })}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Modes of Access to be Used
                          </button>
                        </div>
                      </div>
                    </div>
                  </>
                )
              }
              {/* Hot Work-Specific Fields */}
              {taskReview.requiredPermits.includes('HOT_WORK_PERMIT') && (
                <>



                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                    <h3 className="text-lg font-medium text-red-800 mb-4">Hot Work Permit Requirements</h3>

                    {/* Nature of Hot Work Section */}
                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Nature of Hot Work</h4>
                      <div className="space-y-2">
                        {taskReview.natureOfHotWork.map((work, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={work}
                              onChange={(e) => {
                                const newWork = [...taskReview.natureOfHotWork];
                                newWork[index] = e.target.value;
                                updateTaskReview({ natureOfHotWork: newWork });
                              }}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Enter nature of hot work..."
                            />
                            <button
                              type="button"
                              onClick={() => {
                                const newWork = taskReview.natureOfHotWork.filter((_, i) => i !== index);
                                updateTaskReview({ natureOfHotWork: newWork });
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => updateTaskReview({ natureOfHotWork: [...taskReview.natureOfHotWork, ''] })}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Hot Work Type
                        </button>
                      </div>
                    </div>
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Fire Extinguishers</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {FIRE_EXTINGUISHER_TYPES.map((type) => (
                          <label key={type} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={taskReview.fireExtinguishers.includes(type)}
                              onChange={(e) => {
                                const newExtinguishers = e.target.checked
                                  ? [...taskReview.fireExtinguishers, type]
                                  : taskReview.fireExtinguishers.filter(ext => ext !== type);
                                updateTaskReview({ fireExtinguishers: newExtinguishers });
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {type}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={() => navigate(`/sites/${siteId}/tasks`)}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              <X className="h-4 w-4 inline mr-2" />
              Cancel
            </button>
            <button
              onClick={handleSaveChanges}
              disabled={isSubmitting}
              className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400 rounded-md transition-colors"
            >
              <Save className="h-4 w-4 inline mr-2" />
              {isSubmitting ? 'Saving...' : taskReview.isBlocked ? 'Block Task' : 'Review Task'}
            </button>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ReviewTaskPage;
