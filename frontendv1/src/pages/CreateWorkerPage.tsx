import React, { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
// no lucide icons needed here; PhotoCapture handles visuals
import FloatingCard from '../components/layout/FloatingCard';
import { useSiteContext } from '../hooks/useSiteContext';
import CreateWorkerForm from '../components/workers/CreateWorkerForm';
import { PhotoCapture } from '../components/ui/photo-capture';
import UniversalFilterModal, { FilterValues } from '../components/common/UniversalFilterModal';
import {
  getAvailableWorkers,
  CompanyWorker} from '../data/workers';
// import { mockTrades } from '../data/mockData';

type TabType = 'personal' | 'documents' | 'training' | 'medical' | 'tools';

const CreateWorkerPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  const { siteName } = useSiteContext();

  // Wizard tab state
  const [activeTab, setActiveTab] = useState<TabType>('personal');
  const tabs: { key: TabType; label: string }[] = [
    { key: 'personal', label: 'Personal Details' },
    { key: 'documents', label: 'Documents' },
    { key: 'training', label: 'Training' },
    { key: 'medical', label: 'Medical' },
    { key: 'tools', label: 'Tools' },
  ];

  // Left profile preview info coming from the child form
  const [profileName, setProfileName] = useState<string>('');
  const [profilePhoto, setProfilePhoto] = useState<string | undefined>(undefined);
  const [profilePhotoFile, setProfilePhotoFile] = useState<File | null>(null);
  const [, setRegisteredWorker] = useState<any | null>(null);
  const [requiredCerts, setRequiredCerts] = useState<string[]>([]);

  // Save trigger for child form
  const [submitSignal, setSubmitSignal] = useState<number>(0);

  // Legacy import tab state (kept for future enhancement but not rendered in the new wizard)
  const [] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [] = useState<number[]>([]);
  const [] = useState(false);
  const [] = useState<CompanyWorker | null>(null);
  const [] = useState<{success: number[], failed: number[]}>({success: [], failed: []});

  // Get available workers from company database
  const availableWorkers = useMemo(() => {
    return getAvailableWorkers();
  }, []);

  // Dynamic filter configuration based on available workers
  const filterConfig = useMemo(() => {
    const uniqueTrades = new Set<string>();
    const uniqueSkills = new Set<string>();

    availableWorkers.forEach(worker => {
      worker.trades.forEach(trade => {
        uniqueTrades.add(trade.name);
      });
      // Get skills from worker.skills array directly
      if (worker.skills) {
        worker.skills.forEach(skill => uniqueSkills.add(skill.name));
      }
    });

    const tradeOptions = Array.from(uniqueTrades).map(trade => ({
      value: trade,
      label: trade,
      count: availableWorkers.filter(w => w.trades.some(t => t.name === trade)).length
    }));

    const skillOptions = Array.from(uniqueSkills).map(skill => ({
      value: skill,
      label: skill,
      count: availableWorkers.filter(w => w.skills && w.skills.some(s => s.name === skill)).length
    }));

    return [
      {
        id: 'trade',
        label: 'Trade',
        type: 'multiselect' as const,
        options: tradeOptions
      },
      {
        id: 'skill',
        label: 'Skills',
        type: 'multiselect' as const,
        options: skillOptions
      },
      {
        id: 'complianceStatus',
        label: 'Compliance Status',
        type: 'dropdown' as const,
        placeholder: 'Select compliance status',
        options: [
          { value: 'compliant', label: 'Compliant', count: availableWorkers.filter(w => w.complianceStatus === 'compliant').length },
          { value: 'pending_training', label: 'Pending Training', count: availableWorkers.filter(w => w.complianceStatus === 'pending_training').length },
          { value: 'non_compliant', label: 'Non-Compliant', count: availableWorkers.filter(w => w.complianceStatus === 'non_compliant').length },
          { value: 'expired', label: 'Expired', count: availableWorkers.filter(w => w.complianceStatus === 'expired').length },
        ]
      },
      {
        id: 'onlyCompliant',
        label: 'Show Only Compliant Workers',
        type: 'checkbox' as const,
        options: [
          { value: 'true', label: 'Show only workers with compliant status', count: availableWorkers.filter(w => w.complianceStatus === 'compliant').length }
        ]
      }
    ];
  }, [availableWorkers]);

  // Filter workers based on search and active filters

  // Filter handlers
  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Count active filters for display


  // Cancel action handled via TopBar right actions

  // Helper functions for compliance status



  // Import functionality









  const breadcrumbs = siteId
    ? [
        { name: 'Dashboard', path: '/' },
        { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
        { name: 'Workers', path: `/sites/${siteId}/workers` },
        { name: 'Add Worker', path: `/sites/${siteId}/workers/create` },
      ]
    : [
        { name: 'Dashboard', path: '/' },
        { name: 'Workers', path: '/workers' },
        { name: 'Add Worker', path: '/workers/create' },
      ];

  return (
    <FloatingCard
      title={siteId ? `Add worker to ${siteName ?? siteId}` : 'Create Worker'}
      breadcrumbs={breadcrumbs}
      topBarMinimal={true}
      topBarRightActions={(
        <div className="flex items-center gap-2">
          <button
            onClick={() => {
              if (siteId) {
                navigate(`/sites/${siteId}/workers`);
              } else {
                navigate('/workers');
              }
            }}
            className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              if (activeTab === 'personal') {
                setSubmitSignal(s => s + 1);
              } else {
                // For future tabs, this can trigger their own save handlers
                toast.info('Nothing to save on this step yet.');
              }
            }}
            className="inline-flex items-center px-5 h-9 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
          >
            Save
          </button>
        </div>
      )}
    >
      <div className="flex gap-6">
        {/* Left Panel: Profile + Progress */}
        <aside className="hidden lg:block w-80 flex-shrink-0 sticky top-0 self-start">
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 h-[calc(100vh-180px)] flex flex-col justify-between">
            {/* Profile & Photo Capture */}
            <div className="flex flex-col items-center text-center gap-4">
              <PhotoCapture
                currentPhoto={profilePhotoFile || profilePhoto || null}
                onPhotoChange={(file) => {
                  setProfilePhotoFile(file);
                  const url = URL.createObjectURL(file);
                  setProfilePhoto(url);
                }}
                className="border-none shadow-none"
                photoSize="lg"
              />
              <div>
                <div className="text-base font-semibold text-gray-900">{profileName || 'New Worker'}</div>
                <div className="text-xs text-gray-500">Profile Preview</div>
              </div>
            </div>

            {/* Progress Steps */}
            <div className="mt-8 flex-1 overflow-auto pr-1">
              <ol className="ml-0 space-y-3">
                {tabs.map((t, idx) => {
                  const isActive = activeTab === t.key;
                  const activeIndex = tabs.findIndex(x => x.key === activeTab);
                  const isComplete = idx < activeIndex;
                  return (
                    <li key={t.key} className="ml-0">
                      <div
                        role="button"
                        tabIndex={0}
                        onClick={() => setActiveTab(t.key)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') setActiveTab(t.key);
                        }}
                        className="w-full text-left flex items-center gap-3 cursor-pointer select-none"
                        aria-current={isActive ? 'step' : undefined}
                      >
                        <span
                          className={`inline-flex items-center justify-center w-6 h-6 rounded-full border text-xs font-semibold ${
                            isActive
                              ? 'bg-green-600 text-white border-green-600'
                              : isComplete
                              ? 'bg-green-100 text-green-700 border-green-200'
                              : 'bg-white text-gray-600 border-gray-300'
                          }`}
                        >
                          {isComplete ? '✓' : idx + 1}
                        </span>
                        <span className={`text-sm font-medium ${isActive ? 'text-gray-900' : 'text-gray-700'}`}>{t.label}</span>
                      </div>
                    </li>
                  );
                })}
              </ol>
            </div>

            {/* Bottom Helper */}
            <div className="pt-4 border-t border-gray-100 text-xs text-gray-500">
              Changes are saved when you click Save. You can navigate steps anytime.
            </div>
          </div>
        </aside>

        {/* Right Panel: Content */}
        <section className="flex-1 min-w-0">
          {/* Unified Card: Tabs + Content */}
          <div className="bg-white rounded-xl border border-gray-200 w-full">
            <div className="px-3 border-b border-gray-200 rounded-t-xl">
              <nav className="flex gap-2">
                {tabs.map((t) => (
                  <button
                    key={t.key}
                    onClick={() => setActiveTab(t.key)}
                    className={`h-12 px-4 -mb-px border-b-2 text-sm font-medium ${
                      activeTab === t.key
                        ? 'border-green-600 text-gray-900'
                        : 'border-transparent text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    {t.label}
                  </button>
                ))}
              </nav>
            </div>
            {/* Content Area */}
            <div className="p-6 w-full">
              {activeTab === 'personal' && (
              <CreateWorkerForm
                onSuccess={(worker) => {
                  // Registration done; advance to Training step
                  setRegisteredWorker(worker);
                  const certs = worker?.trade?.requiredCertifications ?? [];
                  setRequiredCerts(certs);
                  setActiveTab('training');
                  toast.success(`Registered ${worker.name}. Next: required trainings`);
                }}
                onCancel={() => {
                  if (siteId) {
                    navigate(`/sites/${siteId}/workers`);
                  } else {
                    navigate('/workers');
                  }
                }}
                  submitSignal={submitSignal}
                  onBasicInfoChange={({ name, photoUrl }) => {
                    setProfileName(name);
                    setProfilePhoto(photoUrl);
                  }}
                  externalPhoto={profilePhotoFile}
                  onPrev={() => {
                    const idx = tabs.findIndex(t => t.key === activeTab);
                    if (idx > 0) setActiveTab(tabs[idx - 1].key);
                  }}
                  onNext={() => {
                    const idx = tabs.findIndex(t => t.key === activeTab);
                    if (idx < tabs.length - 1) {
                      setActiveTab(tabs[idx + 1].key);
                    } else if (activeTab === 'personal') {
                      setSubmitSignal(s => s + 1);
                    }
                  }}
                />
              )}

              {activeTab !== 'personal' && (
              <div className="space-y-4">
                {activeTab === 'training' && (
                  <div className="text-sm text-gray-700">
                    <div className="text-base font-semibold text-gray-900 mb-2">Required Trainings</div>
                    {requiredCerts && requiredCerts.length > 0 ? (
                      <ul className="list-disc pl-5 space-y-1">
                        {requiredCerts.map((c) => (
                          <li key={c}>{c}</li>
                        ))}
                      </ul>
                    ) : (
                      <div>No required trainings loaded yet. Select a trade on the Register step.</div>
                    )}
                  </div>
                )}
                {activeTab !== 'training' && (
                  <div className="text-sm text-gray-600">
                    This section is coming soon. You will be able to manage {activeTab} here.
                  </div>
                )}
              </div>
            )}
            </div>
          </div>

        {/* Bottom Navigation removed; handled inside CreateWorkerForm */}
      </section>
    </div>

    {/* Keep the modal for future import features, but not used in current layout */}
    <UniversalFilterModal
      isOpen={isFilterOpen}
      onClose={() => setIsFilterOpen(false)}
      title="Filter Available Workers"
      filters={filterConfig}
      initialValues={activeFilters}
      onApplyFilters={handleApplyFilters}
      onClearFilters={handleClearFilters}
      size="xl"
    />
  </FloatingCard>
);
};

export default CreateWorkerPage;
