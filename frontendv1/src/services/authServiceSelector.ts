import { mockAuthService } from './mockAuthService';
import { graphqlAuthService } from './graphqlAuthService';
import {
  User,
  LoginCredentials,
  RegisterData,
  LoginPayload,
  UserSession,
  UpdateUserRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  CreateUserRequest,
  PermissionLevel
} from '../types/auth';

// Check if we should use mock authentication
const useMockAuth = import.meta.env.VITE_USE_MOCK_AUTH === 'true';

// Interface for auth service to ensure both services implement the same methods
interface AuthService {
  login(credentials: LoginCredentials): Promise<LoginPayload>;
  register(data: RegisterData): Promise<LoginPayload>;
  getCurrentUser(): Promise<User | null>;
  refreshToken(): Promise<{ success: boolean; accessToken?: string; expiresAt?: string; user?: User }>;
  logout(): Promise<void>;
  logoutAllSessions(): Promise<void>;
  updateProfile(data: UpdateUserRequest): Promise<User>;
  changePassword(data: ChangePasswordRequest): Promise<void>;
  requestPasswordReset(email: string): Promise<void>;
  resetPassword(data: ResetPasswordRequest): Promise<void>;
  getActiveSessions(): Promise<UserSession[]>;
  endSession(sessionId: string): Promise<void>;
  createUser(data: CreateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }>;
  updateUser(userId: number, data: UpdateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }>;
  deleteUser(userId: number, deletedBy: string): Promise<{ success: boolean; errorMessage?: string }>;
  hasPermission(user: User | null, resource: string, action: string, level?: PermissionLevel): boolean;
}

// Create a wrapper that provides a consistent interface
class AuthServiceWrapper implements AuthService {
  private service: AuthService;

  constructor() {
    this.service = useMockAuth ? mockAuthService : graphqlAuthService;
    
    // Log which service is being used
    console.log(`🔐 Auth Service: Using ${useMockAuth ? 'MOCK' : 'GRAPHQL'} authentication`);
    
    if (useMockAuth) {
      console.log('📝 Mock Auth Credentials:');
      console.log('  Email: <EMAIL>');
      console.log('  Password: Admin@123!');
      console.log('  Tenant ID: 1');
      console.log('  Alternative: any email with password "password"');
    }
  }

  async login(credentials: LoginCredentials): Promise<LoginPayload> {
    return this.service.login(credentials);
  }

  async register(data: RegisterData): Promise<LoginPayload> {
    return this.service.register(data);
  }

  async getCurrentUser(): Promise<User | null> {
    return this.service.getCurrentUser();
  }

  async refreshToken(): Promise<{ success: boolean; accessToken?: string; expiresAt?: string; user?: User }> {
    return this.service.refreshToken();
  }

  async logout(): Promise<void> {
    return this.service.logout();
  }

  async logoutAllSessions(): Promise<void> {
    return this.service.logoutAllSessions();
  }

  async updateProfile(data: UpdateUserRequest): Promise<User> {
    return this.service.updateProfile(data);
  }

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    return this.service.changePassword(data);
  }

  async requestPasswordReset(email: string): Promise<void> {
    return this.service.requestPasswordReset(email);
  }

  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    return this.service.resetPassword(data);
  }

  async getActiveSessions(): Promise<UserSession[]> {
    return this.service.getActiveSessions();
  }

  async endSession(sessionId: string): Promise<void> {
    return this.service.endSession(sessionId);
  }

  async createUser(data: CreateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }> {
    return this.service.createUser(data);
  }

  async updateUser(userId: number, data: UpdateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }> {
    return this.service.updateUser(userId, data);
  }

  async deleteUser(userId: number, deletedBy: string): Promise<{ success: boolean; errorMessage?: string }> {
    return this.service.deleteUser(userId, deletedBy);
  }

  hasPermission(user: User | null, resource: string, action: string, level: PermissionLevel = PermissionLevel.Site): boolean {
    return this.service.hasPermission(user, resource, action, level);
  }

  // Utility method to check which service is being used
  get isUsingMock(): boolean {
    return useMockAuth;
  }

  // Method to get service type for debugging
  get serviceType(): string {
    return useMockAuth ? 'mock' : 'graphql';
  }
}

// Export the singleton instance
export const authService = new AuthServiceWrapper();

// Export the type for use in other files
export type { AuthService };
