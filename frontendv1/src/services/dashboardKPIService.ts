import { SiteInfo } from '../types';

export interface KPIData {
  value: string | number;
  contextualMessage: string;
  priority: 'high' | 'medium' | 'low';
  change?: number;
}

export interface CompanyKPIStats {
  workers: KPIData;
  sites: KPIData;
  incidents: KPIData;
  equipment: KPIData;
}

// Mock data service - replace with actual GraphQL queries
export class DashboardKPIService {
  
  // Mock data for demonstration - replace with actual API calls
  private static mockWorkerStats = {
    totalWorkers: 245,
    trainingsExpiring: 20,
    trainingsExpired: 5,
    compliantWorkers: 220,
    workersOnSites: 180,
    availableWorkers: 65
  };

  private static mockSiteStats = {
    totalSites: 8,
    activeSites: 6,
    draftSites: 1,
    pausedSites: 1,
    closedSites: 0
  };

  private static mockIncidentStats = {
    totalIncidents: 12,
    highPriorityIncidents: 3,
    openIncidents: 5,
    resolvedThisMonth: 7,
    safeManHours: 15840
  };

  private static mockEquipmentStats = {
    totalEquipment: 156,
    needingMaintenance: 5,
    inUse: 89,
    available: 62,
    outOfService: 0
  };

  /**
   * Get intelligent contextual message for workers KPI
   */
  private static getWorkersContextualMessage(): { message: string; priority: 'high' | 'medium' | 'low' } {
    const { trainingsExpired, trainingsExpiring } = this.mockWorkerStats;
    
    if (trainingsExpired > 0) {
      return {
        message: `${trainingsExpired} training${trainingsExpired > 1 ? 's' : ''} expired`,
        priority: 'high'
      };
    }
    
    if (trainingsExpiring > 0) {
      return {
        message: `${trainingsExpiring} training${trainingsExpiring > 1 ? 's' : ''} expiring soon`,
        priority: 'medium'
      };
    }
    
    return {
      message: 'All trainings up to date',
      priority: 'low'
    };
  }

  /**
   * Get intelligent contextual message for sites KPI
   */
  private static getSitesContextualMessage(): { message: string; priority: 'high' | 'medium' | 'low' } {
    const { activeSites, pausedSites, draftSites } = this.mockSiteStats;
    
    if (pausedSites > 0) {
      return {
        message: `${pausedSites} site${pausedSites > 1 ? 's' : ''} paused`,
        priority: 'medium'
      };
    }
    
    if (draftSites > 0) {
      return {
        message: `${draftSites} site${draftSites > 1 ? 's' : ''} in draft`,
        priority: 'low'
      };
    }
    
    return {
      message: `${activeSites} active`,
      priority: 'low'
    };
  }

  /**
   * Get intelligent contextual message for incidents KPI
   */
  private static getIncidentsContextualMessage(): { message: string; priority: 'high' | 'medium' | 'low' } {
    const { highPriorityIncidents, openIncidents, safeManHours } = this.mockIncidentStats;
    
    if (highPriorityIncidents > 0) {
      return {
        message: `${highPriorityIncidents} high-priority open`,
        priority: 'high'
      };
    }
    
    if (openIncidents > 0) {
      return {
        message: `${openIncidents} open incident${openIncidents > 1 ? 's' : ''}`,
        priority: 'medium'
      };
    }
    
    return {
      message: `${safeManHours.toLocaleString()} safe man hours`,
      priority: 'low'
    };
  }

  /**
   * Get intelligent contextual message for equipment KPI
   */
  private static getEquipmentContextualMessage(): { message: string; priority: 'high' | 'medium' | 'low' } {
    const { needingMaintenance, inUse, outOfService } = this.mockEquipmentStats;
    
    if (outOfService > 0) {
      return {
        message: `${outOfService} out of service`,
        priority: 'high'
      };
    }
    
    if (needingMaintenance > 0) {
      return {
        message: `${needingMaintenance} needing maintenance`,
        priority: 'medium'
      };
    }
    
    return {
      message: `${inUse} in use`,
      priority: 'low'
    };
  }

  /**
   * Get all company-level KPI data with intelligent contextual messaging
   */
  static getCompanyKPIStats(): CompanyKPIStats {
    const workersContext = this.getWorkersContextualMessage();
    const sitesContext = this.getSitesContextualMessage();
    const incidentsContext = this.getIncidentsContextualMessage();
    const equipmentContext = this.getEquipmentContextualMessage();

    return {
      workers: {
        value: this.mockWorkerStats.totalWorkers,
        contextualMessage: workersContext.message,
        priority: workersContext.priority,
        change: 5 // Mock change percentage
      },
      sites: {
        value: this.mockSiteStats.totalSites,
        contextualMessage: sitesContext.message,
        priority: sitesContext.priority,
        change: 2
      },
      incidents: {
        value: this.mockIncidentStats.totalIncidents,
        contextualMessage: incidentsContext.message,
        priority: incidentsContext.priority,
        change: -15 // Negative change is good for incidents
      },
      equipment: {
        value: this.mockEquipmentStats.totalEquipment,
        contextualMessage: equipmentContext.message,
        priority: equipmentContext.priority,
        change: 8
      }
    };
  }

  /**
   * Future method to integrate with GraphQL
   * Replace mock data with actual API calls
   */
  static async fetchCompanyKPIStatsFromAPI(tenantId: string): Promise<CompanyKPIStats> {
    // TODO: Implement GraphQL queries
    // - GET_COMPANY_WORKER_STATS
    // - GET_SITES with status aggregation
    // - Incident queries with severity filtering
    // - Equipment queries with status filtering
    
    // For now, return mock data
    return this.getCompanyKPIStats();
  }
}
