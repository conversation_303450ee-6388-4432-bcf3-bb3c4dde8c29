import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { 
  GET_ALL_NOTIFICATION_PREFERENCES 
} from '../graphql/queries';
import { 
  UPDATE_NOTIFICATION_PREFERENCES,
  SEND_TEST_NOTIFICATION 
} from '../graphql/mutations';
import { 
  NotificationPreference, 
  NotificationPreferencesHookResult,
  NotificationPriority 
} from '../types/notifications';

export const useNotificationPreferences = (): NotificationPreferencesHookResult => {
  const [preferences, setPreferences] = useState<NotificationPreference[]>([]);

  // Query for all notification preferences
  const { 
    data: preferencesData, 
    loading, 
    error,
    refetch 
  } = useQuery(GET_ALL_NOTIFICATION_PREFERENCES, {
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all'
  });

  // Mutations
  const [updatePreferencesMutation] = useMutation(UPDATE_NOTIFICATION_PREFERENCES);
  const [sendTestNotificationMutation] = useMutation(SEND_TEST_NOTIFICATION);

  // Update local state when data changes
  useEffect(() => {
    if (preferencesData?.allNotificationPreferences) {
      setPreferences(preferencesData.allNotificationPreferences);
    }
  }, [preferencesData]);

  // Update notification preferences
  const updatePreferences = useCallback(async (
    notificationType: string,
    updates: Partial<NotificationPreference>
  ) => {
    try {
      const existingPreference = preferences.find(p => p.notificationType === notificationType);
      
      const variables = {
        notificationType,
        inAppEnabled: updates.inAppEnabled ?? existingPreference?.inAppEnabled ?? true,
        emailEnabled: updates.emailEnabled ?? existingPreference?.emailEnabled ?? true,
        smsEnabled: updates.smsEnabled ?? existingPreference?.smsEnabled ?? false,
        minimumPriority: updates.minimumPriority ?? existingPreference?.minimumPriority ?? NotificationPriority.LOW,
        doNotDisturbEnabled: updates.doNotDisturbEnabled ?? existingPreference?.doNotDisturbEnabled ?? false,
        doNotDisturbStart: updates.doNotDisturbStart ?? existingPreference?.doNotDisturbStart,
        doNotDisturbEnd: updates.doNotDisturbEnd ?? existingPreference?.doNotDisturbEnd
      };

      await updatePreferencesMutation({
        variables,
        optimisticResponse: {
          updateNotificationPreferences: true
        },
        update: () => {
          // Update local state optimistically
          setPreferences(prev => {
            const index = prev.findIndex(p => p.notificationType === notificationType);
            if (index >= 0) {
              const updated = [...prev];
              updated[index] = { ...updated[index], ...updates };
              return updated;
            } else {
              // Create new preference if it doesn't exist
              const newPreference: NotificationPreference = {
                id: Date.now(), // Temporary ID
                notificationType,
                inAppEnabled: variables.inAppEnabled,
                emailEnabled: variables.emailEnabled,
                smsEnabled: variables.smsEnabled,
                minimumPriority: variables.minimumPriority,
                doNotDisturbEnabled: variables.doNotDisturbEnabled,
                doNotDisturbStart: variables.doNotDisturbStart,
                doNotDisturbEnd: variables.doNotDisturbEnd,
                userId: 0, // Will be set by backend
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              return [...prev, newPreference];
            }
          });
        }
      });

      // Refetch to get the actual data from server
      await refetch();
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }, [preferences, updatePreferencesMutation, refetch]);

  // Send test notification
  const sendTestNotification = useCallback(async (
    title = "Test Notification",
    message = "This is a test notification to verify your notification settings.",
    priority = NotificationPriority.MEDIUM
  ) => {
    try {
      await sendTestNotificationMutation({
        variables: { title, message, priority }
      });
    } catch (error) {
      console.error('Error sending test notification:', error);
      throw error;
    }
  }, [sendTestNotificationMutation]);

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    sendTestNotification
  };
};

// Helper function to get preference for a specific notification type
export const useNotificationPreference = (notificationType: string) => {
  const { preferences, loading, error, updatePreferences } = useNotificationPreferences();
  
  const preference = preferences.find(p => p.notificationType === notificationType);
  
  const updatePreference = useCallback((updates: Partial<NotificationPreference>) => {
    return updatePreferences(notificationType, updates);
  }, [notificationType, updatePreferences]);

  return {
    preference,
    loading,
    error,
    updatePreference
  };
};

// Default notification types that should have preferences
export const DEFAULT_NOTIFICATION_TYPES = [
  'training_expiring',
  'training_expired', 
  'training_assigned',
  'training_completed',
  'worker_added',
  'worker_updated',
  'system_alert',
  'permit_expiring',
  'permit_expired',
  'attendance_alert',
  'safety_incident'
];

// Helper to ensure all default preferences exist
export const useEnsureDefaultPreferences = () => {
  const { preferences, updatePreferences } = useNotificationPreferences();
  
  useEffect(() => {
    DEFAULT_NOTIFICATION_TYPES.forEach(type => {
      const exists = preferences.find(p => p.notificationType === type);
      if (!exists) {
        // Create default preference
        updatePreferences(type, {
          inAppEnabled: true,
          emailEnabled: true,
          smsEnabled: false,
          minimumPriority: NotificationPriority.LOW,
          doNotDisturbEnabled: false
        }).catch(console.error);
      }
    });
  }, [preferences, updatePreferences]);
};

export default useNotificationPreferences;
