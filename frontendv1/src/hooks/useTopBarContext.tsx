import React, { createContext, useContext, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { extractSiteId } from '../utils/routeUtils';
import { useAllSites } from './useSiteContext';

// TopBar configuration interface
export interface TopBarConfig {
  // Display configuration
  showSearch: boolean;
  showWeather: boolean;
  showNotifications: boolean;
  showSiteInfo: boolean;
  showUserMenu: boolean;
  
  // Layout configuration
  minimal: boolean;
  showBack: boolean;
  
  // Context information
  pageType: 'dashboard' | 'site-dashboard' | 'site-page' | 'creation-workflow' | 'company-level';
  siteId?: string;
  siteName?: string;
  
  // Title configuration
  title: string;
  showTitle: boolean;
}

// Context interface
interface TopBarContextType {
  config: TopBarConfig;
  updateTitle: (title: string) => void;
  setMinimal: (minimal: boolean) => void;
  setShowBack: (showBack: boolean) => void;
}

// Create context
const TopBarContext = createContext<TopBarContextType | undefined>(undefined);

// Route patterns for different page types
const ROUTE_PATTERNS = {
  // Creation/Edit workflows - these should be minimal
  CREATION_WORKFLOWS: [
    /^\/sites\/new$/,
    /^\/sites\/[^\/]+\/workers\/new$/,
    /^\/sites\/[^\/]+\/workers\/edit\/[^\/]+$/,
    /^\/sites\/[^\/]+\/permits\/new$/,
    /^\/sites\/[^\/]+\/permits\/edit\/[^\/]+$/,
    /^\/sites\/[^\/]+\/incidents\/new$/,
    /^\/sites\/[^\/]+\/incidents\/edit\/[^\/]+$/,
    /^\/sites\/[^\/]+\/equipment\/new$/,
    /^\/sites\/[^\/]+\/equipment\/edit\/[^\/]+$/,
    // Permit form routes (site-specific)
    /^\/sites\/[^\/]+\/hot-work\/form(\/[^\/]+)?$/,
    /^\/sites\/[^\/]+\/confined-space\/form(\/[^\/]+)?$/,
    /^\/sites\/[^\/]+\/excavation\/form(\/[^\/]+)?$/,
    /^\/sites\/[^\/]+\/work-at-height\/form(\/[^\/]+)?$/,
    /^\/sites\/[^\/]+\/ptw-form(\/[^\/]+)?$/,
    // Demo form routes
    /^\/demo\/ptw-form$/,
    /^\/demo\/hot-work-form$/,
    /^\/demo\/confined-space-form$/,
    /^\/demo\/work-at-height-form$/,
    /^\/demo\/excavation-form$/,
    // Company-level permit forms
    /^\/hot-work\/form(\/[^\/]+)?$/,
    /^\/confined-space\/form(\/[^\/]+)?$/,
    /^\/excavation\/form(\/[^\/]+)?$/,
    /^\/work-at-height\/form(\/[^\/]+)?$/,
    /^\/ptw\/form(\/[^\/]+)?$/,
    /^\/ptw-form$/,
  ],
  
  // Site dashboard
  SITE_DASHBOARD: [
    /^\/sites\/[^\/]+\/dashboard$/,
    /^\/sites\/[^\/]+$/,
  ],
  
  // Secondary/Detail pages - these should show back button but not minimal mode
  SECONDARY_PAGES: [
    // Task detail and request pages
    /^\/sites\/[^\/]+\/tasks\/[^\/]+$/,
    /^\/sites\/[^\/]+\/tasks\/request\/[^\/]+$/,
    // Worker profile pages
    /^\/sites\/[^\/]+\/workers\/[^\/]+$/,
    // Permit detail pages
    /^\/sites\/[^\/]+\/permits\/[^\/]+\/[^\/]+$/,
    // Engineer detail pages
    /^\/sites\/[^\/]+\/engineer\/permits\/[^\/]+$/,
    // Inspection form pages
    /^\/sites\/[^\/]+\/inspections\/form\/[^\/]+$/,
    // Toolbox specific pages
    /^\/sites\/[^\/]+\/toolbox\/fill$/,
    /^\/sites\/[^\/]+\/toolbox\/attendance$/,
    /^\/sites\/[^\/]+\/toolbox\/summarize$/,
    // Training specific pages
    /^\/sites\/[^\/]+\/training\/new$/,
    // Task workflow pages
    /^\/sites\/[^\/]+\/tasks\/review$/,
    /^\/sites\/[^\/]+\/tasks\/approve$/,
    /^\/sites\/[^\/]+\/tasks\/close$/,
    /^\/sites\/[^\/]+\/tasks\/all$/,
    // Permit workflow pages
    /^\/sites\/[^\/]+\/permits\/approve$/,
  ],

  // Site pages (non-dashboard)
  SITE_PAGES: [
    /^\/sites\/[^\/]+\/workers$/,
    /^\/sites\/[^\/]+\/permits$/,
    /^\/sites\/[^\/]+\/incidents$/,
    /^\/sites\/[^\/]+\/equipment$/,
    /^\/sites\/[^\/]+\/training$/,
    /^\/sites\/[^\/]+\/safety$/,
    /^\/sites\/[^\/]+\/data$/,
    /^\/sites\/[^\/]+\/info$/,
    /^\/sites\/[^\/]+\/tasks$/,
    /^\/sites\/[^\/]+\/inspections$/,
    /^\/sites\/[^\/]+\/toolbox$/,
    /^\/sites\/[^\/]+\/engineer$/,
    /^\/sites\/[^\/]+\/engineer\/tasks$/,
    /^\/sites\/[^\/]+\/engineer\/permits$/,
    /^\/sites\/[^\/]+\/engineer\/weather$/,
    /^\/sites\/[^\/]+\/engineer\/notifications$/,
    /^\/sites\/[^\/]+\/engineer\/account$/,
    /^\/sites\/[^\/]+\/engineer\/overtime$/,
  ],
  
  // Company dashboard
  COMPANY_DASHBOARD: [
    /^\/$/,
  ],
  
  // Company level pages
  COMPANY_PAGES: [
    /^\/settings$/,
    /^\/account$/,
    /^\/data$/,
    /^\/reports$/,
  ],
};

// Determine page type from pathname
function getPageType(pathname: string): TopBarConfig['pageType'] {
  // Check creation workflows first (highest priority)
  if (ROUTE_PATTERNS.CREATION_WORKFLOWS.some(pattern => pattern.test(pathname))) {
    return 'creation-workflow';
  }

  // Check secondary pages (detail pages that need back button)
  if (ROUTE_PATTERNS.SECONDARY_PAGES.some(pattern => pattern.test(pathname))) {
    return 'site-page'; // Use site-page type but will get back button
  }

  // Check site dashboard
  if (ROUTE_PATTERNS.SITE_DASHBOARD.some(pattern => pattern.test(pathname))) {
    return 'site-dashboard';
  }

  // Check site pages
  if (ROUTE_PATTERNS.SITE_PAGES.some(pattern => pattern.test(pathname))) {
    return 'site-page';
  }

  // Check company dashboard
  if (ROUTE_PATTERNS.COMPANY_DASHBOARD.some(pattern => pattern.test(pathname))) {
    return 'dashboard';
  }

  // Default to company level
  return 'company-level';
}

// Generate default title based on route
function getDefaultTitle(pathname: string, pageType: TopBarConfig['pageType'], siteName?: string): string {
  // Creation workflows
  if (pageType === 'creation-workflow') {
    if (pathname.includes('/sites/new')) return 'Create New Site';
    if (pathname.includes('/workers/new')) return 'Add New Worker';
    if (pathname.includes('/workers/edit')) return 'Edit Worker';
    if (pathname.includes('/permits/new')) return 'Create New Permit';
    if (pathname.includes('/permits/edit')) return 'Edit Permit';
    if (pathname.includes('/incidents/new')) return 'Report New Incident';
    if (pathname.includes('/incidents/edit')) return 'Edit Incident';
    if (pathname.includes('/equipment/new')) return 'Add New Equipment';
    if (pathname.includes('/equipment/edit')) return 'Edit Equipment';
    if (pathname.includes('/demo/ptw-form')) return 'Permit to Work Form';
    if (pathname.includes('/demo/hot-work-form')) return 'Hot Work Permit Form';
    if (pathname.includes('/demo/confined-space-form')) return 'Confined Space Entry System Demo';
    if (pathname.includes('/demo/work-at-height-form')) return 'Work at Height Permit Form';
    return 'Create/Edit';
  }
  
  // Site pages
  if (pageType === 'site-dashboard') {
    return siteName ? `${siteName} Dashboard` : 'Site Dashboard';
  }

  if (pageType === 'site-page') {
    // Secondary/Detail pages (more specific patterns first)
    if (pathname.match(/\/tasks\/request\/[^\/]+$/)) return 'Task Request';
    if (pathname.match(/\/tasks\/[^\/]+$/)) return 'Task Details';
    if (pathname.match(/\/workers\/[^\/]+$/)) return 'Worker Profile';
    if (pathname.match(/\/permits\/[^\/]+\/[^\/]+$/)) return 'Permit Details';
    if (pathname.match(/\/engineer\/permits\/[^\/]+$/)) return 'Permit Details';
    if (pathname.match(/\/inspections\/form\/[^\/]+$/)) return 'Inspection Form';
    if (pathname.includes('/toolbox/fill')) return 'Fill Toolbox Talk';
    if (pathname.includes('/toolbox/attendance')) return 'Toolbox Attendance';
    if (pathname.includes('/toolbox/summarize')) return 'Toolbox Summary';
    if (pathname.includes('/training/new')) return 'New Training';
    if (pathname.includes('/tasks/review')) return 'Review Tasks';
    if (pathname.includes('/tasks/approve')) return 'Approve Tasks';
    if (pathname.includes('/tasks/close')) return 'Close Tasks';
    if (pathname.includes('/tasks/all')) return 'All Tasks';
    if (pathname.includes('/permits/approve')) return 'Approve Permits';

    // Main section pages
    if (pathname.includes('/workers')) return 'Workers';
    if (pathname.includes('/permits')) return 'Permits';
    if (pathname.includes('/incidents')) return 'Incidents';
    if (pathname.includes('/equipment')) return 'Equipment';
    if (pathname.includes('/training')) return 'Training';
    if (pathname.includes('/safety')) return 'Safety';
    if (pathname.includes('/data')) return 'Data';
    if (pathname.includes('/info')) return 'Site Information';
    if (pathname.includes('/tasks')) return 'Tasks';
    if (pathname.includes('/inspections')) return 'Inspections';
    if (pathname.includes('/toolbox')) return 'Toolbox Talks';
    if (pathname.includes('/engineer')) return 'Engineer Dashboard';
    return 'Site Page';
  }
  
  // Company level
  if (pageType === 'dashboard') return 'Dashboard';
  if (pathname.includes('/settings')) return 'Settings';
  if (pathname.includes('/account')) return 'Account';
  if (pathname.includes('/data')) return 'Data Management';
  if (pathname.includes('/reports')) return 'Reports';
  
  return 'Page';
}

// Generate TopBar configuration based on route
function generateTopBarConfig(pathname: string, allSites: any[]): TopBarConfig {
  const pageType = getPageType(pathname);
  const siteId = extractSiteId(pathname);
  const site = siteId ? allSites.find(s => s.id === siteId) : undefined;
  const siteName = site?.name;
  const isSecondaryPage = ROUTE_PATTERNS.SECONDARY_PAGES.some(pattern => pattern.test(pathname));

  const baseConfig: TopBarConfig = {
    pageType,
    siteId: siteId || undefined,
    siteName,
    title: getDefaultTitle(pathname, pageType, siteName),
    showTitle: true,
    showSearch: true,
    showWeather: true,
    showNotifications: true,
    showSiteInfo: true,
    showUserMenu: true,
    minimal: false,
    showBack: false,
  };

  // Configure based on page type
  switch (pageType) {
    case 'creation-workflow':
      return {
        ...baseConfig,
        minimal: true,
        showBack: true,
        showSearch: false,
        showWeather: false,
        showNotifications: false,
        showSiteInfo: false,
        // Keep user menu for essential access
      };

    case 'site-dashboard':
      return {
        ...baseConfig,
        showSiteInfo: !!siteId, // Only show if we have a site
        showBack: false,
      };

    case 'site-page':
      {
        const configForSitePage = {
          ...baseConfig,
          showSiteInfo: !!siteId, // Only show if we have a site
          showBack: isSecondaryPage, // Show back button for secondary/detail pages
        };

        // Specialize permit detail pages to hide site-level controls
        const isPermitDetail = /\/sites\/[^\/]+\/permits\/[^\/]+\/[^\/]+$/.test(pathname);
        if (isPermitDetail) {
          return {
            ...configForSitePage,
            showSearch: false,
            showWeather: false,
            showNotifications: false,
            showSiteInfo: false,
            showUserMenu: false,
          };
        }

        return configForSitePage;
      }

    case 'dashboard':
    case 'company-level':
      return {
        ...baseConfig,
        showSiteInfo: false, // No site info at company level
        showBack: false,
      };

    default:
      return baseConfig;
  }
}

// Provider component
interface TopBarProviderProps {
  children: React.ReactNode;
}

export const TopBarProvider: React.FC<TopBarProviderProps> = ({ children }) => {
  const location = useLocation();
  const allSites = useAllSites();
  
  // Generate configuration based on current route
  const baseConfig = useMemo(() => {
    return generateTopBarConfig(location.pathname, allSites);
  }, [location.pathname, allSites]);
  
  // State for dynamic overrides
  const [configOverrides, setConfigOverrides] = React.useState<Partial<TopBarConfig>>({});
  
  // Merge base config with overrides
  const config = useMemo(() => {
    return { ...baseConfig, ...configOverrides };
  }, [baseConfig, configOverrides]);
  
  // Context methods
  const updateTitle = React.useCallback((title: string) => {
    setConfigOverrides(prev => ({ ...prev, title }));
  }, []);
  
  const setMinimal = React.useCallback((minimal: boolean) => {
    setConfigOverrides(prev => ({
      ...prev,
      minimal,
      // When minimal mode is enabled, hide distracting elements
      showSearch: minimal ? false : prev.showSearch ?? true,
      showWeather: minimal ? false : prev.showWeather ?? true,
      showNotifications: minimal ? false : prev.showNotifications ?? true,
      showSiteInfo: minimal ? false : prev.showSiteInfo ?? true,
    }));
  }, []);
  
  const setShowBack = React.useCallback((showBack: boolean) => {
    setConfigOverrides(prev => ({ ...prev, showBack }));
  }, []);
  
  // Reset overrides when route changes
  React.useEffect(() => {
    setConfigOverrides({});
  }, [location.pathname]);
  
  const contextValue = useMemo(() => ({
    config,
    updateTitle,
    setMinimal,
    setShowBack,
  }), [config, updateTitle, setMinimal, setShowBack]);
  
  return (
    <TopBarContext.Provider value={contextValue}>
      {children}
    </TopBarContext.Provider>
  );
};

// Hook to use TopBar context
export const useTopBar = (): TopBarContextType => {
  const context = useContext(TopBarContext);
  if (!context) {
    throw new Error('useTopBar must be used within a TopBarProvider');
  }
  return context;
};

// Hook for pages to override TopBar configuration
export const useTopBarConfig = () => {
  const { updateTitle, setMinimal, setShowBack } = useTopBar();
  
  return {
    setTitle: updateTitle,
    setMinimal,
    setShowBack,
  };
};
