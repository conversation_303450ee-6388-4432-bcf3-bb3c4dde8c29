import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import {
  GET_MY_NOTIFICATIONS,
  GET_UNREAD_NOTIFICATION_COUNT
} from '../graphql/queries';
import {
  MARK_NOTIFICATION_AS_READ,
  MARK_ALL_NOTIFICATIONS_AS_READ
} from '../graphql/mutations';
import {
  NotificationHookResult,
} from '../types/notifications';
import { notificationErrorHandler, RetryHandler } from '../utils/notificationErrorHandler';

interface UseNotificationsOptions {
  skip?: number;
  take?: number;
  unreadOnly?: boolean;
  enableRealTime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export const useNotifications = (options: UseNotificationsOptions = {}): NotificationHookResult => {
  const {
    skip = 0,
    take = 50,
    unreadOnly = false,
    // enableRealTime = true,
    autoRefresh = true,
    refreshInterval = 30000 // 30 seconds
  } = options;

  const [_notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Query for notifications
  const {
    data: notificationsData,
    loading: notificationsLoading,
    error: notificationsError,
    refetch: refetchNotifications
  } = useQuery(GET_MY_NOTIFICATIONS, {
    variables: { skip, take, unreadOnly },
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    onError: (error) => {
      notificationErrorHandler.handleError(error, 'notifications-query');
    }
  });

  // Query for unread count
  const {
    data: unreadCountData,
    refetch: refetchUnreadCount
  } = useQuery(GET_UNREAD_NOTIFICATION_COUNT, {
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    onError: (error) => {
      notificationErrorHandler.handleError(error, 'unread-count-query');
    }
  });

  // Mutations
  const [markAsReadMutation] = useMutation(MARK_NOTIFICATION_AS_READ);
  const [markAllAsReadMutation] = useMutation(MARK_ALL_NOTIFICATIONS_AS_READ);

  // Real-time subscription
  // const { data: subscriptionData } = useSubscription(ON_NOTIFICATION, {
  //   skip: !enableRealTime || !connectionMonitor.isConnected(),
  //   onSubscriptionData: ({ subscriptionData }) => {
  //     if (subscriptionData.data?.onNotification) {
  //       const newNotification = subscriptionData.data.onNotification;

  //       // Add new notification to the list
  //       setNotifications(prev => {
  //         const exists = prev.find(n => n.id === newNotification.id);
  //         if (exists) return prev;

  //         return [newNotification, ...prev].slice(0, take);
  //       });

  //       // Update unread count
  //       if (!newNotification.readAt) {
  //         setUnreadCount(prev => prev + 1);
  //       }

  //       // Play notification sound if enabled and high priority
  //       if (newNotification.priority === NotificationPriority.HIGH ||
  //         newNotification.priority === NotificationPriority.CRITICAL) {
  //         playNotificationSound(newNotification.priority);
  //       }
  //     }
  //   },
  //   onError: (error) => {
  //     notificationErrorHandler.handleError(error, 'notification-subscription');
  //   }
  // });

  // Update local state when data changes
  useEffect(() => {
    if (notificationsData?.myNotifications) {
      setNotifications(notificationsData.myNotifications);
    }
  }, [notificationsData]);

  useEffect(() => {
    if (unreadCountData?.unreadNotificationCount !== undefined) {
      setUnreadCount(unreadCountData.unreadNotificationCount);
    }
  }, [unreadCountData]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refetchNotifications();
      refetchUnreadCount();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refetchNotifications, refetchUnreadCount]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: number) => {
    try {
      await RetryHandler.withRetry(async () => {
        await markAsReadMutation({
          variables: { notificationId },
          optimisticResponse: {
            markNotificationAsRead: true
          },
          update: (_cache) => {
            // Update the notification in cache
            // setNotifications(prev =>
            //   prev.map(n =>
            //     n.id === notificationId
            //       ? { ...n, readAt: new Date().toISOString(), status: 'READ' as any }
            //       : n
            //   )
            // );

            // Update unread count
            setUnreadCount(prev => Math.max(0, prev - 1));
          }
        });
      });
    } catch (error) {
      notificationErrorHandler.handleError(error, 'mark-as-read');
      throw error;
    }
  }, [markAsReadMutation]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await RetryHandler.withRetry(async () => {
        await markAllAsReadMutation({
          optimisticResponse: {
            markAllNotificationsAsRead: true
          },
          update: () => {
            // Update all notifications in local state
            setNotifications(prev =>
              prev.map(n => ({
                ...n,
                // readAt: n.readAt || new Date().toISOString(),
                status: 'read' as any
              }))
            );

            // Reset unread count
            setUnreadCount(0);
          }
        });
      });
    } catch (error) {
      notificationErrorHandler.handleError(error, 'mark-all-as-read');
      throw error;
    }
  }, [markAllAsReadMutation]);

  // Refetch all data
  const refetch = useCallback(async () => {
    await Promise.all([
      refetchNotifications(),
      refetchUnreadCount()
    ]);
  }, [refetchNotifications, refetchUnreadCount]);

  return {
  // notifications,
  loading: notificationsLoading,
  error: notificationsError,
  unreadCount,
  refetch,
  markAsRead,
  markAllAsRead,
  notifications: [],
};
};

// Utility function to play notification sounds
// const playNotificationSound = (priority: NotificationPriority) => {
//   try {
//     const audio = new Audio();

//     switch (priority) {
//       case NotificationPriority.CRITICAL:
//         audio.src = '/sounds/critical-notification.mp3';
//         break;
//       case NotificationPriority.HIGH:
//         audio.src = '/sounds/high-notification.mp3';
//         break;
//       case NotificationPriority.MEDIUM:
//         audio.src = '/sounds/medium-notification.mp3';
//         break;
//       default:
//         audio.src = '/sounds/low-notification.mp3';
//     }

//     audio.volume = 0.5;
//     audio.play().catch(console.warn); // Ignore autoplay restrictions
//   } catch (error) {
//     console.warn('Could not play notification sound:', error);
//   }
// };

export default useNotifications;
