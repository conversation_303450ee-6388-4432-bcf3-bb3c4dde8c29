import React, { useState, useEffect } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  Calendar,
  Users,
  Clock,
  MapPin,
  X,
  User,
  ChevronDown
} from 'lucide-react';
import { TrainingSession } from '../../types/training';
import TrainingScheduleModal from './TrainingScheduleModal';
import TrainingEnrollmentModal from './TrainingEnrollmentModal';
import { useNavigate } from "react-router-dom";

interface TrainingCalendarProps {
	siteId: string;
}

// Mock data - replace with actual API calls
const instructors = [
  { id: 'inst1', name: '<PERSON>' },
  { id: 'inst2', name: 'Dr. <PERSON>' },
  { id: 'inst3', name: '<PERSON>' }
];

const mockTrainingSessions: TrainingSession[] = [
  {
    id: '1',
    title: 'Safety Orientation',
    description: 'Basic safety training for new workers',
    trainingProgramId: 'tp1',
    date: new Date('2025-01-20'),
    startTime: '09:00',
    endTime: '12:00',
    location: 'Training Room A',
    instructor: '<PERSON>',
    maxAttendees: 20,
    attendees: 15,
    status: 'scheduled',
    registeredWorkers: []
  },
  {
    id: '2',
    title: 'Equipment Operation',
    description: 'Heavy machinery operation training',
    trainingProgramId: 'tp2',
    date: new Date('2025-01-22'),
    startTime: '14:00',
    endTime: '17:00',
    location: 'Site Workshop',
    instructor: 'Mike Wilson',
    maxAttendees: 15,
    attendees: 7,
    status: 'scheduled',
    registeredWorkers: []
  },
  {
    id: '3',
    title: 'First Aid Certification',
    description: 'Basic first aid and CPR training',
    trainingProgramId: 'tp3',
    date: new Date('2025-01-25'),
    startTime: '10:00',
    endTime: '16:00',
    location: 'Training Room B',
    instructor: 'Dr. Sarah Johnson',
    maxAttendees: 25,
    attendees: 13,
    status: 'scheduled',
    registeredWorkers: []
  },
  {
    id: '4',
    title: 'Working at Heights',
    description: 'Safety training for elevated work',
    trainingProgramId: 'tp4',
    date: new Date('2025-01-27'),
    startTime: '08:00',
    endTime: '17:00',
    location: 'Training Center',
    instructor: 'John Smith',
    maxAttendees: 20,
    attendees: 18,
    status: 'scheduled',
    registeredWorkers: []
  },
  {
    id: '5',
    title: 'Electrical Safety',
    description: 'Electrical hazard awareness',
    trainingProgramId: 'tp5',
    date: new Date('2025-01-30'),
    startTime: '09:00',
    endTime: '15:00',
    location: 'Electrical Workshop',
    instructor: 'Alex Thompson',
    maxAttendees: 12,
    attendees: 10,
    status: 'scheduled',
    registeredWorkers: []
  }
];

const TrainingCalendar: React.FC<TrainingCalendarProps> = ({ siteId }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'month' | 'week' | 'day' | 'year'>('week');
  const [trainingSessions, setTrainingSessions] = useState<TrainingSession[]>(mockTrainingSessions);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showEnrollmentModal, setShowEnrollmentModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<TrainingSession | null>(null);
  const [showSessionDetails, setShowSessionDetails] = useState(false);
  const [miniCalendarDate, setMiniCalendarDate] = useState(new Date());
  const navigate = useNavigate();

	useEffect(() => {
		// TODO: Fetch actual data from API
		// fetchTrainingSessions(siteId);
	}, [siteId]);

  const formatCalendarHeader = () => {
    if (view === 'month') {
      return currentDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    } else if (view === 'week') {
      const weekDates = getWeekDates(currentDate);
      const startDate = weekDates[0];
      const endDate = weekDates[6];

      if (startDate.getMonth() === endDate.getMonth()) {
        return `${startDate.toLocaleDateString('en-US', { month: 'long' })} ${startDate.getDate()} - ${endDate.getDate()}, ${startDate.getFullYear()}`;
      } else {
        return `${startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}, ${endDate.getFullYear()}`;
      }
    } else if (view === 'day') {
      return currentDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    } else {
      return currentDate.getFullYear().toString();
    }
  };

  const navigateCalendar = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (view === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else if (view === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (view === 'year') {
      newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));
    }
    setCurrentDate(newDate);
  };

  const getWeekDates = (date: Date) => {
    const week = [];
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day; // First day is Sunday
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const weekDate = new Date(startOfWeek);
      weekDate.setDate(startOfWeek.getDate() + i);
      week.push(weekDate);
    }
    return week;
  };

  const openEnrollmentModal = () => {
    // For site-level, only enrollment in company-scheduled programs is allowed
    setShowEnrollmentModal(true);
  };

  const handleScheduleTraining = (trainingData: any) => {
    const newSession: TrainingSession = {
      id: trainingData.id,
      title: trainingData.title,
      description: trainingData.description,
      trainingProgramId: trainingData.trainingProgramId,
      date: new Date(trainingData.date),
      startTime: trainingData.startTime,
      endTime: trainingData.endTime,
      location: trainingData.location,
      instructor: instructors.find(i => i.id === trainingData.instructorId)?.name || 'Unknown',
      maxAttendees: trainingData.maxParticipants,
      attendees: 0,
      status: 'scheduled',
      registeredWorkers: []
    };

    setTrainingSessions(prev => [...prev, newSession]);
    console.log('Training scheduled:', newSession);
  };

  const handleEventClick = (sessionId: string) => {
    const session = trainingSessions.find(s => s.id === sessionId);
    if (session) {
      setSelectedSession(session);
      setShowSessionDetails(true);
    }
  };

  const handleTimeSlotClick = (date: Date, hour: number) => {
    // Site-level users cannot create training sessions
    console.log('Site-level users can only enroll in company-scheduled programs');
    return;
  };

  const handleMiniCalendarDateClick = (date: Date) => {
    setCurrentDate(date);
    setMiniCalendarDate(date);
  };

  const navigateMiniCalendar = (direction: 'prev' | 'next') => {
    const newDate = new Date(miniCalendarDate);
    newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    setMiniCalendarDate(newDate);
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const getSessionsForDate = (date: Date) => {
    return trainingSessions.filter(session => {
      const sessionDate = new Date(session.date);
      return sessionDate.toDateString() === date.toDateString();
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress': return 'bg-green-100 text-green-800 border-green-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTimeSlots = () => {
    const slots = [];
    for (let hour = 6; hour <= 20; hour++) {
      slots.push({
        time: `${hour.toString().padStart(2, '0')}:00`,
        hour: hour
      });
    }
    return slots;
  };

  const calculateSessionHeight = (session: TrainingSession) => {
    const startHour = parseInt(session.startTime.split(':')[0]);
    const startMinute = parseInt(session.startTime.split(':')[1]);
    const endHour = parseInt(session.endTime.split(':')[0]);
    const endMinute = parseInt(session.endTime.split(':')[1]);

    const durationInMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);
    const heightPerHour = 60;
    return (durationInMinutes / 60) * heightPerHour;
  };

  const calculateSessionTop = (session: TrainingSession) => {
    const startHour = parseInt(session.startTime.split(':')[0]);
    const startMinute = parseInt(session.startTime.split(':')[1]);
    const baseHour = 6;

    const hourOffset = startHour - baseHour;
    const minuteOffset = startMinute / 60;
    const heightPerHour = 60;

    return (hourOffset + minuteOffset) * heightPerHour;
  };

  const renderYearView = () => {
    const months = [];
    const today = new Date();
    const currentYear = currentDate.getFullYear();

    for (let month = 0; month < 12; month++) {
      const monthDate = new Date(currentYear, month, 1);
      const monthName = monthDate.toLocaleDateString('en-US', { month: 'short' });
      const daysInMonth = getDaysInMonth(monthDate);
      const firstDay = getFirstDayOfMonth(monthDate);
      const isCurrentMonth = month === today.getMonth() && currentYear === today.getFullYear();
      const isSelectedMonth = month === currentDate.getMonth() && currentYear === currentDate.getFullYear();

      const monthDays = [];

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDay; i++) {
        monthDays.push(<div key={`empty-${month}-${i}`} className="h-4 text-gray-300"></div>);
      }

      // Add cells for each day of the month
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(currentYear, month, day);
        const daySessions = getSessionsForDate(date);
        const isToday = date.toDateString() === today.toDateString();
        const hasEvents = daySessions.length > 0;

        monthDays.push(
          <div
            key={`${month}-${day}`}
            className={`h-4 text-xs flex items-center justify-center cursor-pointer relative ${
              isToday 
                ? 'bg-green-600 text-white rounded-full w-4 h-4' 
                : hasEvents 
                  ? 'text-green-600 font-medium' 
                  : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => {
              setCurrentDate(date);
              setView('month');
            }}
            title={hasEvents ? `${daySessions.length} training session(s)` : ''}
          >
            {day}
            {hasEvents && !isToday && (
              <div className="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-500 rounded-full"></div>
            )}
          </div>
        );
      }

      months.push(
        <div key={month} className={`p-3 border border-gray-200 rounded-lg ${isSelectedMonth ? 'bg-green-50 border-green-300' : 'bg-white'}`}>
          <div className={`text-sm font-medium mb-2 text-center ${isSelectedMonth ? 'text-green-700' : 'text-gray-900'}`}>
            {monthName}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map(day => (
              <div key={day} className="h-4 flex items-center justify-center text-xs font-medium text-gray-500">
                {day}
              </div>
            ))}
            {monthDays}
          </div>
        </div>
      );
    }

    return (
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          {months}
        </div>
      </div>
    );
  };

  const renderCalendarGrid = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-32 border border-gray-200 bg-gray-50"></div>);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      const daySessions = getSessionsForDate(date);
      const isToday = date.toDateString() === new Date().toDateString();

      days.push(
        <div key={day} className={`h-32 border border-gray-200 p-1 ${isToday ? 'bg-green-50' : 'bg-white'}`}>
          <div className={`text-sm font-medium mb-1 ${isToday ? 'text-green-600' : 'text-gray-900'}`}>
            {day}
          </div>
          <div className="space-y-1 overflow-y-auto max-h-24">
            {daySessions.slice(0, 3).map(session => (
              <div
                key={session.id}
                onClick={() => handleEventClick(session.id)}
                className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${getStatusColor(session.status)}`}
              >
                <div className="font-medium truncate">{session.title}</div>
                <div className="truncate">{session.startTime}</div>
              </div>
            ))}
            {daySessions.length > 3 && (
              <div className="text-xs text-gray-500 p-1">
                +{daySessions.length - 3} more
              </div>
            )}
          </div>
        </div>
      );
    }

    return days;
  };

  const renderMiniCalendar = () => {
    const daysInMonth = getDaysInMonth(miniCalendarDate);
    const firstDay = getFirstDayOfMonth(miniCalendarDate);
    const today = new Date();
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 text-gray-400"></div>);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(miniCalendarDate.getFullYear(), miniCalendarDate.getMonth(), day);
      const isToday = date.toDateString() === today.toDateString();
      const isSelected = date.toDateString() === currentDate.toDateString();
      const hasEvents = getSessionsForDate(date).length > 0;

      days.push(
        <button
          key={day}
          onClick={() => handleMiniCalendarDateClick(date)}
          className={`h-8 w-8 rounded-full text-sm font-medium transition-colors relative ${
            isToday 
              ? 'bg-green-600 text-white' 
              : isSelected 
                ? 'bg-green-100 text-green-800' 
                : 'hover:bg-gray-100 text-gray-900'
          }`}
        >
          {day}
          {hasEvents && (
            <div className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full ${
              isToday ? 'bg-white' : 'bg-green-500'
            }`}></div>
          )}
        </button>
      );
    }

    return days;
  };

  const renderDayView = () => {
    const timeSlots = getTimeSlots();
    const today = new Date();
    const isToday = currentDate.toDateString() === today.toDateString();

    return (
      <div className="flex flex-col h-full">
        {/* Day Header */}
        <div className="grid grid-cols-2 border-b border-gray-200">
          <div className="p-3 text-sm font-medium text-gray-500">Time</div>
          <div className={`p-3 text-center border-l border-gray-200 ${isToday ? 'bg-green-50' : ''}`}>
            <div className={`text-sm font-medium ${isToday ? 'text-green-600' : 'text-gray-900'}`}>
              {currentDate.toLocaleDateString('en-US', { weekday: 'long' })}
            </div>
            <div className={`text-lg font-bold ${isToday ? 'text-green-600' : 'text-gray-700'}`}>
              {currentDate.getDate()}
            </div>
          </div>
        </div>

        {/* All-day events section */}
        <div className="border-b border-gray-200">
          <div className="grid grid-cols-2 min-h-[60px]">
            <div className="p-2 text-xs text-gray-500 border-r border-gray-200 flex items-center">
              All Day
            </div>
            <div className="border-l border-gray-200 p-1 space-y-1">
              {trainingSessions
                .filter(session => {
                  const sessionDate = new Date(session.date);
                  const sessionDuration = calculateSessionHeight(session);
                  return sessionDate.toDateString() === currentDate.toDateString() && sessionDuration >= 480;
                })
                .map(session => (
                  <div
                    key={session.id}
                    className={`text-xs p-1 rounded cursor-pointer ${getStatusColor(session.status)} hover:opacity-80`}
                    onClick={() => handleEventClick(session.id)}
                  >
                    <div className="font-medium truncate">{session.title}</div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Day Grid */}
        <div className="flex-1 overflow-y-auto">
          <div className="relative">
            {/* Time slots */}
            {timeSlots.map((slot, slotIndex) => (
              <div key={slotIndex} className="grid grid-cols-2 border-b border-gray-100" style={{ height: '60px' }}>
                {/* Time label */}
                <div className="p-2 text-xs text-gray-500 border-r border-gray-200">
                  {slot.time}
                </div>

                {/* Day column */}
                <div
                  className={`border-l border-gray-200 relative cursor-pointer ${
                    isToday && slot.hour === new Date().getHours() ? 'bg-green-50' : isToday ? 'bg-green-25' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleTimeSlotClick(currentDate, slot.hour)}
                  title={`Schedule training for ${currentDate.toLocaleDateString()} at ${slot.time}`}
                >
                  {/* Current time indicator */}
                  {isToday && slot.hour === new Date().getHours() && (
                    <div className="absolute left-0 right-0 h-0.5 bg-green-500 z-10" style={{ top: `${(new Date().getMinutes() / 60) * 60}px` }}></div>
                  )}
                </div>
              </div>
            ))}

            {/* Training sessions overlay */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="grid grid-cols-2 h-full">
                <div></div>
                <div className="relative border-l border-gray-200">
                  {trainingSessions
                    .filter(session => {
                      const sessionDate = new Date(session.date);
                      return sessionDate.toDateString() === currentDate.toDateString();
                    })
                    .map(session => {
                      const height = calculateSessionHeight(session);
                      const top = calculateSessionTop(session);

                      return (
                        <div
                          key={session.id}
                          className={`absolute left-1 right-1 rounded-md p-1 text-xs cursor-pointer pointer-events-auto shadow-sm border ${getStatusColor(session.status)} hover:opacity-80 transition-opacity`}
                          style={{
                            top: `${top}px`,
                            height: `${height}px`,
                            minHeight: '20px'
                          }}
                          onClick={() => handleEventClick(session.id)}
                        >
                          <div className="font-medium truncate">{session.title}</div>
                          <div className="truncate opacity-75">
                            {session.startTime} - {session.endTime}
                          </div>
                          <div className="truncate opacity-75">
                            {session.location}
                          </div>
                          <div className="truncate opacity-75">
                            {session.attendees}/{session.maxAttendees} attendees
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const weekDates = getWeekDates(currentDate);
    const timeSlots = getTimeSlots();
    const today = new Date();

    return (
      <div className="flex flex-col h-full">
        {/* Week Header */}
        <div className="grid grid-cols-8 border-b border-gray-200">
          <div className="p-3 text-sm font-medium text-gray-500">Time</div>
          {weekDates.map((date, index) => {
            const isToday = date.toDateString() === today.toDateString();
            const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
            const dayNumber = date.getDate();

            return (
              <div key={index} className={`p-3 text-center border-l border-gray-200 ${isToday ? 'bg-green-50' : ''}`}>
                <div className={`text-sm font-medium ${isToday ? 'text-green-600' : 'text-gray-900'}`}>
                  {dayName}
                </div>
                <div className={`text-lg font-bold ${isToday ? 'text-green-600' : 'text-gray-700'}`}>
                  {dayNumber}
                </div>
              </div>
            );
          })}
        </div>

        {/* All-day events section */}
        <div className="border-b border-gray-200">
          <div className="grid grid-cols-8 min-h-[60px]">
            <div className="p-2 text-xs text-gray-500 border-r border-gray-200 flex items-center">
              All Day
            </div>
            {weekDates.map((date, dateIndex) => {
              const allDayEvents = trainingSessions.filter(session => {
                const sessionDate = new Date(session.date);
                const sessionDuration = calculateSessionHeight(session);
                return sessionDate.toDateString() === date.toDateString() && sessionDuration >= 480;
              });

              return (
                <div key={dateIndex} className="border-l border-gray-200 p-1 space-y-1">
                  {allDayEvents.map(session => (
                    <div
                      key={session.id}
                      className={`text-xs p-1 rounded cursor-pointer ${getStatusColor(session.status)} hover:opacity-80`}
                      onClick={() => handleEventClick(session.id)}
                    >
                      <div className="font-medium truncate">{session.title}</div>
                    </div>
                  ))}
                </div>
              );
            })}
          </div>
        </div>

        {/* Week Grid */}
        <div className="flex-1 overflow-y-auto">
          <div className="relative">
            {/* Time slots */}
            {timeSlots.map((slot, slotIndex) => (
              <div key={slotIndex} className="grid grid-cols-8 border-b border-gray-100" style={{ height: '60px' }}>
                {/* Time label */}
                <div className="p-2 text-xs text-gray-500 border-r border-gray-200">
                  {slot.time}
                </div>

                {/* Day columns */}
                {weekDates.map((date, dateIndex) => {
                  const isToday = date.toDateString() === today.toDateString();
                  const currentHour = new Date().getHours();
                  const isCurrentHour = isToday && slot.hour === currentHour;

                  return (
                    <div
                      key={dateIndex}
                      className={`border-l border-gray-200 relative cursor-pointer ${
                        isCurrentHour ? 'bg-green-50' : isToday ? 'bg-green-25' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleTimeSlotClick(date, slot.hour)}
                      title={`Schedule training for ${date.toLocaleDateString()} at ${slot.time}`}
                    >
                      {/* Current time indicator */}
                      {isCurrentHour && (
                        <div className="absolute left-0 right-0 h-0.5 bg-green-500 z-10" style={{ top: `${(new Date().getMinutes() / 60) * 60}px` }}></div>
                      )}
                    </div>
                  );
                })}
              </div>
            ))}

            {/* Training sessions overlay */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="grid grid-cols-8 h-full">
                <div></div>
                {weekDates.map((date, dateIndex) => (
                  <div key={dateIndex} className="relative border-l border-gray-200">
                    {trainingSessions
                      .filter(session => {
                        const sessionDate = new Date(session.date);
                        return sessionDate.toDateString() === date.toDateString();
                      })
                      .map(session => {
                        const height = calculateSessionHeight(session);
                        const top = calculateSessionTop(session);

                        return (
                          <div
                            key={session.id}
                            className={`absolute left-1 right-1 rounded-md p-1 text-xs cursor-pointer pointer-events-auto shadow-sm border ${getStatusColor(session.status)} hover:opacity-80 transition-opacity`}
                            style={{
                              top: `${top}px`,
                              height: `${height}px`,
                              minHeight: '20px'
                            }}
                            onClick={() => handleEventClick(session.id)}
                          >
                            <div className="font-medium truncate">{session.title}</div>
                            <div className="truncate opacity-75">
                              {session.startTime} - {session.endTime}
                            </div>
                            <div className="truncate opacity-75">
                              {session.location}
                            </div>
                            <div className="truncate opacity-75">
                              {session.attendees}/{session.maxAttendees} attendees
                            </div>
                          </div>
                        );
                      })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const getScheduledTrainings = () => {
    const today = new Date();
    return trainingSessions
      .filter((session) => session.date >= today)
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .slice(0, 10);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (time: string) => {
    return time;
  };

	return (
		<div className="flex h-screen">
			{/* Left Column - 1/3 width */}
			<div className="w-1/3 flex flex-col">
				{/* Schedule Training Button */}
				<div className="p-4">
					<button
						className="w-full px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
								onClick={openEnrollmentModal}
					>
								<Plus className="h-4 w-4 mr-2" />
								Enroll in Training
					</button>
				</div>

				{/* Scrollable Left Sidebar */}
				<div className="flex-1 overflow-y-auto hover:overflow-y-scroll scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
					{/* Mini Calendar */}
					<div className="p-4">
						<div className="flex items-center justify-between mb-3">
							<button
								className="p-1 rounded-full hover:bg-gray-100 transition-colors"
								onClick={() => navigateMiniCalendar('prev')}
							>
								<ChevronLeft className="h-4 w-4 text-gray-600" />
							</button>
							<h3 className="text-sm font-medium text-gray-900">
								{miniCalendarDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
							</h3>
							<button
								className="p-1 rounded-full hover:bg-gray-100 transition-colors"
								onClick={() => navigateMiniCalendar('next')}
							>
								<ChevronRight className="h-4 w-4 text-gray-600" />
							</button>
						</div>

						{/* Day labels */}
						<div className="grid grid-cols-7 gap-1 mb-2">
							{['S', 'M', 'T', 'W', 'T', 'F', 'S'].map(day => (
								<div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-gray-500">
									{day}
								</div>
							))}
						</div>

						{/* Calendar grid */}
						<div className="grid grid-cols-7 gap-1">
							{renderMiniCalendar()}
						</div>
					</div>

					{/* Scheduled Trainings List */}
					<div className="p-4">
						<h3 className="text-sm font-medium text-gray-900 mb-3">Scheduled Trainings</h3>
						<div className="space-y-2">
							{getScheduledTrainings().map(session => (
								<div
									key={session.id}
									className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer"
									onClick={() => handleEventClick(session.id)}
								>
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<h4 className="text-sm font-medium text-gray-900 truncate">{session.title}</h4>
											<div className="flex items-center text-xs text-gray-500 mt-1">
												<Calendar className="h-3 w-3 mr-1" />
												{formatDate(session.date)}
											</div>
											<div className="flex items-center text-xs text-gray-500 mt-1">
												<Clock className="h-3 w-3 mr-1" />
												{session.startTime} - {session.endTime}
											</div>
											<div className="flex items-center text-xs text-gray-500 mt-1">
												<MapPin className="h-3 w-3 mr-1" />
												{session.location}
											</div>
										</div>
										<span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(session.status)}`}>
											{session.status.charAt(0).toUpperCase() + session.status.slice(1)}
										</span>
									</div>
								</div>
							))}
							
							{getScheduledTrainings().length === 0 && (
								<div className="text-center py-8">
									<Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
									<p className="text-sm text-gray-500">No upcoming training sessions</p>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Right Column - 2/3 width */}
			<div className="w-2/3 flex flex-col">
				{/* Main Calendar Container with Border and Rounded Corners */}
				<div className="flex-1 border border-gray-200 rounded-[20px] overflow-hidden bg-white">
					{/* Calendar Navigation Bar */}
					<div className="flex items-center justify-between p-4 border-b border-gray-200">
						<div className="flex items-center space-x-4">
							<button
								className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
								onClick={() => setCurrentDate(new Date())}
							>
								Today
							</button>
							<div className="flex items-center space-x-2">
								<button
									className="p-1 rounded-full hover:bg-gray-100 transition-colors"
									onClick={() => navigateCalendar('prev')}
								>
									<ChevronLeft className="h-5 w-5 text-gray-600" />
								</button>
								<button
									className="p-1 rounded-full hover:bg-gray-100 transition-colors"
									onClick={() => navigateCalendar('next')}
								>
									<ChevronRight className="h-5 w-5 text-gray-600" />
								</button>
							</div>
							<span className="text-lg font-medium text-gray-900">
								{formatCalendarHeader()}
							</span>
						</div>
						<div className="flex items-center space-x-2">
							<div className="relative">
								<select
									value={view}
									onChange={(e) => setView(e.target.value as 'month' | 'week' | 'day' | 'year')}
									className="appearance-none px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors pr-8"
								>
									<option value="day">Day</option>
									<option value="week">Week</option>
									<option value="month">Month</option>
									<option value="year">Year</option>
								</select>
								<ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
							</div>
						</div>
					</div>

					{/* Main Calendar Area */}
					<div className="flex-1 overflow-y-auto hover:overflow-y-scroll scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
						{view === 'day' && renderDayView()}
						{view === 'week' && renderWeekView()}
						{view === 'month' && (
							<div className="p-4">
								{/* Calendar Header */}
								<div className="grid grid-cols-7 gap-px mb-2">
									{['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
										<div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
											{day}
										</div>
									))}
								</div>

								{/* Calendar Grid */}
								<div className="grid grid-cols-7 gap-px border border-gray-200 rounded-lg overflow-hidden">
									{renderCalendarGrid()}
								</div>
							</div>
						)}
						{view === 'year' && renderYearView()}
					</div>
				</div>
			</div>

			{/* Training Schedule Modal - Only for company level */}
			<TrainingScheduleModal
				isOpen={showScheduleModal}
				onClose={() => setShowScheduleModal(false)}
				onSchedule={handleScheduleTraining}
				siteId={siteId}
			/>

			{/* Training Enrollment Modal - For site level */}
			<TrainingEnrollmentModal
				isOpen={showEnrollmentModal}
				onClose={() => setShowEnrollmentModal(false)}
				siteId={siteId}
			/>

			{/* Session Details Modal */}
			{showSessionDetails && selectedSession && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
					<div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
						<div className="flex justify-between items-center p-6 border-b border-gray-200">
							<h2 className="text-xl font-semibold text-gray-900">{selectedSession.title}</h2>
							<button
								onClick={() => setShowSessionDetails(false)}
								className="text-gray-400 hover:text-gray-600"
							>
								<X className="h-6 w-6" />
							</button>
						</div>

						<div className="p-6 space-y-4">
							<div>
								<h3 className="text-sm font-medium text-gray-700 mb-1">Description</h3>
								<p className="text-gray-900">{selectedSession.description}</p>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<h3 className="text-sm font-medium text-gray-700 mb-1">Date & Time</h3>
									<div className="flex items-center text-gray-900">
										<Calendar className="h-4 w-4 mr-2" />
										{formatDate(selectedSession.date)}
									</div>
									<div className="flex items-center text-gray-900 mt-1">
										<Clock className="h-4 w-4 mr-2" />
										{selectedSession.startTime} - {selectedSession.endTime}
									</div>
								</div>

								<div>
									<h3 className="text-sm font-medium text-gray-700 mb-1">Location & Instructor</h3>
									<div className="flex items-center text-gray-900">
										<MapPin className="h-4 w-4 mr-2" />
										{selectedSession.location}
									</div>
									<div className="flex items-center text-gray-900 mt-1">
										<User className="h-4 w-4 mr-2" />
										{selectedSession.instructor}
									</div>
								</div>
							</div>

							<div>
								<h3 className="text-sm font-medium text-gray-700 mb-1">Attendance</h3>
								<div className="flex items-center text-gray-900">
									<Users className="h-4 w-4 mr-2" />
									{selectedSession.attendees} of {selectedSession.maxAttendees} attendees
								</div>
								<div className="w-full bg-gray-200 rounded-full h-2 mt-2">
									<div
										className="bg-green-500 h-2 rounded-full"
										style={{ width: `${(selectedSession.attendees / selectedSession.maxAttendees) * 100}%` }}
									/>
								</div>
							</div>

							<div>
								<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedSession.status)}`}>
									{selectedSession.status.charAt(0).toUpperCase() + selectedSession.status.slice(1)}
								</span>
							</div>
						</div>

						<div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
							<button
								onClick={() => setShowSessionDetails(false)}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Close
							</button>
							<button
								onClick={() => {
									console.log("Record attendance for session:", selectedSession.id);
									setShowSessionDetails(false);
								}}
								className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
							>
								Record Attendance
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default TrainingCalendar;