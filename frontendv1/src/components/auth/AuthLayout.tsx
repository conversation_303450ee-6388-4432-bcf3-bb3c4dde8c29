import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  showLogo?: boolean;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showLogo = true
}) => {
  return (
    <div className="min-h-screen bg-[#eaede8] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 font-[Inter]">
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Header */}
        {showLogo && (
          <div className="text-center">
            <Link to="/" className="inline-block">
              <img
                src="/image/logo.png"
                alt="Workforce Logo"
                className="h-16 w-auto mx-auto"
              />
            </Link>
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              {title}
            </h2>
            {subtitle && (
              <p className="mt-2 text-sm text-gray-600">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Auth Form Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {children}
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            © 2024 Workforce. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
