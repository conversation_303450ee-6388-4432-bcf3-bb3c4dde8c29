import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuthContext';
import { PermissionLevel } from '../../types/auth';

interface RoleProtectedRouteProps {
	children: React.ReactNode;
	allowedRoles?: string[];
	requiredPermissions?: {
		resource: string;
		action: string;
		level?: PermissionLevel;
	}[];
	requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission.
	redirectTo?: string;
	fallbackComponent?: React.ComponentType;
}

export function RoleProtectedRoute({
	children,
	allowedRoles,
	requiredPermissions,
	requireAll = false,
	redirectTo = '/',
	fallbackComponent: FallbackComponent,
}: RoleProtectedRouteProps) {
	const { user, hasPermission, isAuthenticated, isLoading } = useAuth();

	// Show loading state while checking authentication
	if (isLoading) {
		return <div className="flex items-center justify-center min-h-screen">
			<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
		</div>;
	}

	// Redirect to login if not authenticated
	if (!isAuthenticated || !user) {
		return <Navigate to="/login" replace />;
	}

	// Check role-based access
	if (allowedRoles && allowedRoles.length > 0) {
		const hasRequiredRole = allowedRoles.includes(user.role.name);
		if (!hasRequiredRole) {
			if (FallbackComponent) {
				return <FallbackComponent />;
			}
			return <Navigate to={redirectTo} replace />;
		}
	}

	// Check permission-based access
	if (requiredPermissions && requiredPermissions.length > 0) {
		const permissionChecks = requiredPermissions.map(permission =>
			hasPermission(permission.resource, permission.action, permission.level)
		);

		const hasAccess = requireAll
			? permissionChecks.every(check => check)
			: permissionChecks.some(check => check);

		if (!hasAccess) {
			if (FallbackComponent) {
				return <FallbackComponent />;
			}
			return <Navigate to={redirectTo} replace />;
		}
	}

	return <>{children}</>;
}

export default RoleProtectedRoute;
