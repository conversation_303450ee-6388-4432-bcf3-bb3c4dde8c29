import React from 'react';
import { useAuth } from '../../hooks/useAuthContext';
import { PermissionLevel } from '../../types/auth';

interface RoleProtectedComponentProps {
	allowedRoles?: string[];
	requiredPermissions?: {
		resource: string;
		action: string;
		level?: PermissionLevel;
	}[];
	children: React.ReactNode;
	fallback?: React.ReactNode;
	requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission.
}

export function RoleProtectedComponent({
	allowedRoles,
	requiredPermissions,
	children,
	fallback = null,
	requireAll = false,
}: RoleProtectedComponentProps) {
	const { user, hasPermission } = useAuth();

	if (!user) {
		return <>{fallback}</>;
	}

	// Check role-based access
	if (allowedRoles && allowedRoles.length > 0) {
		const hasRequiredRole = allowedRoles.includes(user.role.name);
		if (!hasRequiredRole) {
			return <>{fallback}</>;
		}
	}

	// Check permission-based access
	if (requiredPermissions && requiredPermissions.length > 0) {
		const permissionChecks = requiredPermissions.map(permission =>
			hasPermission(permission.resource, permission.action, permission.level)
		);

		const hasAccess = requireAll
			? permissionChecks.every(check => check)
			: permissionChecks.some(check => check);

		if (!hasAccess) {
			return <>{fallback}</>;
		}
	}

	return <>{children}</>;
}

export default RoleProtectedComponent;
