import React from "react";
import { Check<PERSON>ircle, AlertTriangle, XCircle } from "lucide-react";
import { CompanyEquipment } from "../../data/equipmentMockData";

type Props = {
  equipment: CompanyEquipment;
  onEdit?: () => void;
  onDelete?: () => void;
};

const complianceColor = (status?: string) => {
  switch (status) {
    case "compliant":
      return "bg-green-100 text-green-800";
    case "warning":
      return "bg-yellow-100 text-yellow-800";
    case "critical":
      return "bg-orange-100 text-orange-800";
    case "overdue":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const complianceIcon = (status?: string) => {
  switch (status) {
    case "compliant":
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    case "warning":
      return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    case "critical":
      return <AlertTriangle className="w-4 h-4 text-orange-600" />;
    case "overdue":
      return <XCircle className="w-4 h-4 text-red-600" />;
    default:
      return <AlertTriangle className="w-4 h-4 text-gray-400" />;
  }
};

const EquipmentHeader: React.FC<Props> = ({ equipment, onEdit, onDelete }) => {
  return (
    <div className="flex items-center justify-between gap-4">
      <div className="flex items-center gap-4">
        <div className="h-16 w-16 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center text-blue-700 text-sm font-semibold">
        {equipment.category?.slice(0, 2) || "EQ"}
        </div>
        <div className="flex-1">
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-xl font-semibold text-gray-900 mr-2">{equipment.name}</h1>
          <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {equipment.equipmentNumber}
          </span>
          {equipment.category && (
            <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {equipment.category}
            </span>
          )}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            equipment.overallStatus === "active"
              ? "bg-green-100 text-green-800"
              : equipment.overallStatus === "maintenance"
              ? "bg-yellow-100 text-yellow-800"
              : equipment.overallStatus === "inactive"
              ? "bg-gray-100 text-gray-800"
              : "bg-red-100 text-red-800"
          }`}>
            {equipment.overallStatus}
          </span>
          <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${complianceColor(
            equipment.complianceStatus
          )}`}>
            {complianceIcon(equipment.complianceStatus)}
            <span>
              {equipment.complianceStatus
                ? equipment.complianceStatus.charAt(0).toUpperCase() +
                  equipment.complianceStatus.slice(1)
                : "Unknown"}
            </span>
          </span>
        </div>
        <div className="text-sm text-gray-500 mt-1">
          {equipment.manufacturer && equipment.model && (
            <span>
              {equipment.manufacturer} • {equipment.model}
            </span>
          )}
          {equipment.currentSiteName && (
            <span className="ml-2">@ {equipment.currentSiteName}</span>
          )}
        </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {onEdit && (
          <button
            onClick={onEdit}
            className="inline-flex items-center px-4 py-2 border border-blue-600 rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50"
          >
            Edit
          </button>
        )}
        {onDelete && (
          <button
            onClick={onDelete}
            className="inline-flex items-center px-4 py-2 border border-red-600 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-red-50"
          >
            Remove
          </button>
        )}
      </div>
    </div>
  );
};

export default EquipmentHeader;
