import React from 'react';
import type { FilterConfig, FilterValues } from './UniversalFilterModal';

interface ActiveFiltersBarProps {
  values: FilterValues;
  config: FilterConfig[];
  onRemove: (filterId: string) => void;
  onClear: () => void;
  className?: string;
}

// Helper to format a single value based on config options
function formatValue(filter: FilterConfig, value: any): string {
  if (value == null || value === '' || (Array.isArray(value) && value.length === 0)) return '';

  // Date range
  if (filter.type === 'daterange' && typeof value === 'object') {
    const start = value.start || '';
    const end = value.end || '';
    if (!start && !end) return '';
    return `${start || 'Start'} - ${end || 'Present'}`;
  }

  // Multi-select / checkbox arrays
  if ((filter.type === 'multiselect' || filter.type === 'checkbox') && Array.isArray(value)) {
    if (!filter.options) return value.join(', ');
    const labels = value
      .map((v) => filter.options!.find((o) => o.value === v)?.label || String(v))
      .join(', ');
    return labels;
  }

  // Single value with options lookup
  if (filter.options) {
    const opt = filter.options.find((o) => o.value === value);
    if (opt) return opt.label;
  }

  return String(value);
}

const ActiveFiltersBar: React.FC<ActiveFiltersBarProps> = ({ values, config, onRemove, onClear, className = '' }) => {
  // Collect active entries
  const entries = config
    .map((filter) => {
      const value = values[filter.id];
      // Determine if "active"
      const isActive = Array.isArray(value)
        ? value.length > 0
        : typeof value === 'object' && value !== null
        ? Object.values(value).some((v) => v !== '' && v !== null)
        : value !== '' && value !== null && value !== false && value !== undefined;

      if (!isActive) return null;
      const displayValue = formatValue(filter, value);
      if (!displayValue) return null;
      return { id: filter.id, label: filter.label, displayValue };
    })
    .filter(Boolean) as { id: string; label: string; displayValue: string }[];

  if (entries.length === 0) return null;

  return (
    <div className={`mb-6 flex flex-wrap items-center gap-2 ${className}`}>
      {entries.map((e) => (
        <span key={e.id} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {e.label}: {e.displayValue}
          <button onClick={() => onRemove(e.id)} className="ml-2 text-green-600 hover:text-green-800" aria-label={`Remove ${e.label} filter`}>
            ×
          </button>
        </span>
      ))}
      <div className="ml-auto">
        <button onClick={onClear} className="text-sm font-medium text-green-600 hover:text-green-800">
          Clear
        </button>
      </div>
    </div>
  );
};

export default ActiveFiltersBar;
