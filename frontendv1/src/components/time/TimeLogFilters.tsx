import React from 'react';
import { Search, Filter, Download, Calendar, RefreshCw } from 'lucide-react';
import { TimeLogFilters as Filters } from '../../types/time';

interface TimeLogFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  onExport?: () => void;
  onSync?: () => void;
  trades: string[];
  showActions?: boolean;
}

const TimeLogFilters: React.FC<TimeLogFiltersProps> = ({
  filters,
  onFiltersChange,
  onExport,
  onSync,
  trades,
  showActions = true
}) => {
	const handleFilterChange = (key: keyof Filters, value: string) => {
		onFiltersChange({
			...filters,
			[key]: value,
		});
	};

	const statusOptions = [
		{ value: "all", label: "All Status" },
		{ value: "on-site", label: "On-site" },
		{ value: "late", label: "Late" },
		{ value: "absent", label: "Absent" },
		{ value: "off-site", label: "Off-site" },
	];

	return (
		<div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center w-full">
				{/* Date Selector */}
				<div className="flex items-center gap-2">
					<Calendar className="h-4 w-4 text-gray-400" />
					<input
						type="date"
						value={filters.date}
						onChange={(e) => handleFilterChange("date", e.target.value)}
						className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
					/>
				</div>

				{/* Search Input */}
				<div className="flex-1 relative min-w-0">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
					<input
						type="text"
						placeholder="Search workers by name, ID, or trade..."
						value={filters.search}
						onChange={(e) => handleFilterChange("search", e.target.value)}
						className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-green-500 focus:border-green-500"
					/>
				</div>

				{/* Status Filter */}
				<div className="flex items-center gap-2">
					<Filter className="h-4 w-4 text-gray-400" />
					<select
						value={filters.status}
						onChange={(e) => handleFilterChange("status", e.target.value)}
						className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
					>
						{statusOptions.map((option) => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
				</div>

				{/* Trade Filter */}
				<div className="flex items-center gap-2">
					<select
						value={filters.trade}
						onChange={(e) => handleFilterChange("trade", e.target.value)}
						className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
					>
						<option value="">All Trades</option>
						{trades.map((trade) => (
							<option key={trade} value={trade}>
								{trade}
							</option>
						))}
					</select>
				</div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex items-center gap-2">
            {onSync && (
              <button
                onClick={onSync}
                className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors whitespace-nowrap"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Devices
              </button>
            )}
            {onExport && (
              <button
                onClick={onExport}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors whitespace-nowrap"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            )}
          </div>
        )}
      </div>
  );
};

export default TimeLogFilters;
