import React, { useState, useMemo } from 'react';
import { Users, Clock, AlertTriangle, CheckCircle, Wifi, RefreshCw, Download, Search, Filter as FilterIcon } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import TimeLogTable from './TimeLogTable';
import EditTimeLogModal from './EditTimeLogModal';
import { TimeLog } from '../../types';
import { TimeLogFilters as Filters, AttendanceSummary, TerminalStatus, EditTimeLogData } from '../../types/time';
import { getCurrentDate, determineWorkerStatus, calculateTotalHours } from '../../utils/timeUtils';
import { getTerminalsForSite, getTimeLogsForDate } from '../../data/timeMock';
import UniversalFilterModal, { FilterValues, FilterConfig } from '../common/UniversalFilterModal';
import ActiveFiltersBar from '../common/ActiveFiltersBar';

interface AttendanceTabProps {
	siteId: string;
}

const AttendanceTab: React.FC<AttendanceTabProps> = ({ siteId }) => {
	const [filters, setFilters] = useState<Filters>({
		search: '',
		status: 'all',
		trade: '',
		date: getCurrentDate()
	});

	// Pull terminals for the site from mock data
	const terminals: TerminalStatus[] = useMemo(() => getTerminalsForSite(siteId), [siteId]);

	// Time logs for selected date from processed attendance
	const [timeLogs, setTimeLogs] = useState<TimeLog[]>(getTimeLogsForDate(siteId, getCurrentDate()));

	// Refresh time logs when date or site changes
	React.useEffect(() => {
		setTimeLogs(getTimeLogsForDate(siteId, filters.date));
	}, [siteId, filters.date]);

	// Calculate terminal statistics
	const terminalStats = useMemo(() => {
		const onlineTerminals = terminals.filter(
			(t) => t.status === "online",
		).length;
		const totalTerminals = terminals.length;
		const totalCheckInsToday = terminals.reduce(
			(sum, t) => sum + t.totalCheckInsToday,
			0,
		);

    return {
      onlineTerminals,
      totalTerminals,
      totalCheckInsToday
    };
  }, [terminals]);
  const [selectedTimeLog, setSelectedTimeLog] = useState<TimeLog | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [loading, _setLoading] = useState(false);
//   const [isSyncing, setIsSyncing] = useState(false);
  // No sync modal; Device navigation replaces sync

	// Universal Filter Modal state (replaces inline filters)
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const handleApplyAttendanceFilters = (values: FilterValues) => {
		setFilters(prev => ({
			...prev,
			status: ((values.status as string) || 'all') as Filters['status'],
			trade: (values.trade as string) || ''
		}));
	};

	const handleClearAttendanceFilters = () => {
		setFilters(prev => ({ ...prev, status: 'all', trade: '' }));
	};

	const attendanceFilterConfig: FilterConfig[] = useMemo(() => {
		const todayLogs = timeLogs.filter(l => l.date === filters.date);
		const tradeOptions = Array.from(new Set(todayLogs.map(l => l.workerTrade)))
			.sort()
			.map(trade => ({
				value: trade,
				label: trade,
				count: todayLogs.filter(l => l.workerTrade === trade).length
			}));

		const statusValues = ['on-site','late','off-site','absent'];
		const statusOptions = statusValues.map(s => ({
			value: s,
			label: s.replace('-', ' '),
			count: todayLogs.filter(l => l.status === s).length
		}));

		return [
			{
				id: 'status',
				label: 'Status',
				type: 'dropdown',
				placeholder: 'Select status',
				options: statusOptions
			},
			{
				id: 'trade',
				label: 'Trade',
				type: 'dropdown',
				placeholder: 'Select trade',
				options: tradeOptions
			}
		];
	}, [timeLogs, filters.date]);

	// Filter time logs
	const filteredTimeLogs = useMemo(() => {
		return timeLogs.filter((log) => {
			// Date filter
			if (log.date !== filters.date) return false;

			// Search filter
			if (filters.search) {
				const searchTerm = filters.search.toLowerCase();
				if (
					!log.workerName.toLowerCase().includes(searchTerm) &&
					!log.workerId.toLowerCase().includes(searchTerm) &&
					!log.workerTrade.toLowerCase().includes(searchTerm)
				) {
					return false;
				}
			}

			// Status filter
			if (filters.status !== "all" && log.status !== filters.status) {
				return false;
			}

			// Trade filter
			if (filters.trade && log.workerTrade !== filters.trade) {
				return false;
			}

			return true;
		});
	}, [timeLogs, filters]);

	// Calculate attendance summary
	const attendanceSummary: AttendanceSummary = useMemo(() => {
		const todayLogs = timeLogs.filter((log) => log.date === filters.date);

    return {
      totalWorkers: todayLogs.length,
      presentToday: todayLogs.filter(log => log.status === 'on-site' || log.status === 'late' || log.status === 'off-site').length,
      late: todayLogs.filter(log => log.status === 'late').length,
      absent: todayLogs.filter(log => log.status === 'absent').length,
      onSite: todayLogs.filter(log => log.status === 'on-site').length,
      verifiedByHikvision: todayLogs.filter(log => log.isVerifiedByHikvision === true).length,
      manualEntries: todayLogs.filter(log => log.isVerifiedByHikvision === false || log.isManuallyEdited === true).length
    };
  }, [timeLogs, filters.date]);

	// Removed activeFilterCount; ActiveFiltersBar handles display and clear

	const handleEditTimeLog = (timeLog: TimeLog) => {
		setSelectedTimeLog(timeLog);
		setIsEditModalOpen(true);
	};

	const handleSaveTimeLog = (timeLogId: string, data: EditTimeLogData) => {
		setTimeLogs((prev) =>
			prev.map((log) => {
				if (log.id === timeLogId) {
					const updatedLog = {
						...log,
						clockIn: data.clockIn || log.clockIn,
						clockOut: data.clockOut || log.clockOut,
						breakDuration: data.breakDuration,
						isManuallyEdited: true,
						editReason: data.reason,
						editedBy: "Current User",
						editedAt: new Date().toISOString(),
					};

					// Recalculate hours and status
					if (updatedLog.clockIn && updatedLog.clockOut) {
						updatedLog.totalHours = calculateTotalHours(
							updatedLog.clockIn,
							updatedLog.clockOut,
							updatedLog.breakDuration || 0,
						);
					}

					updatedLog.status = determineWorkerStatus(
						updatedLog.clockIn,
						updatedLog.clockOut,
					);

					return updatedLog;
				}
				return log;
			}),
		);
	};

  const handleExport = () => {
    console.log('Exporting time logs...', filteredTimeLogs);
    alert('Export functionality would be implemented here');
  };

  const handleOpenDevices = () => {
    // Navigate to devices view within TimeManagement by setting hash
    window.location.hash = '#devices';
  };

	return (
		<>
			<div className="space-y-8">
				{/* KPI Cards */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
					<KPICard
						title="Total Workers"
						value={attendanceSummary.totalWorkers}
						icon={<Users className="h-6 w-6 text-blue-500" />}
					/>
					<KPICard
						title="Present Today"
						value={attendanceSummary.presentToday}
						icon={<CheckCircle className="h-6 w-6 text-green-500" />}
					/>
					<KPICard
						title="Late Arrivals"
						value={attendanceSummary.late}
						icon={<AlertTriangle className="h-6 w-6 text-orange-500" />}
					/>
					<KPICard
						title="Absent"
						value={attendanceSummary.absent}
						icon={<Clock className="h-6 w-6 text-red-500" />}
					/>
					<KPICard
						title="Terminal Status"
						value={`${terminalStats.onlineTerminals}/${terminalStats.totalTerminals} Online`}
						change={terminalStats.totalCheckInsToday}
						icon={<Wifi className="h-6 w-6 text-cyan-500" />}
					/>
				</div>

				{/* Time Management Interface */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">

					{/* Toolbar: Title left, controls right */}
					<div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
						<h2 className="text-lg font-semibold text-gray-900">Attendance</h2>

						<div className="flex items-center w-full md:w-auto gap-3">
							{/* Search */}
							<div className="relative flex-1 md:flex-initial md:w-80">
								<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
									<Search className="h-4 w-4 text-gray-500" />
								</div>
								<input
									type="text"
									className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
									placeholder="Search by worker name, ID or trade..."
									value={filters.search}
									onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
								/>
							</div>

							{/* Date selector */}
							<input
								type="date"
								value={filters.date}
								onChange={(e) => setFilters(prev => ({ ...prev, date: e.target.value }))}
								className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
							/>

							{/* Filter button (UniversalFilterModal) */}
							<button
								onClick={() => setIsFilterOpen(true)}
								className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
							>
								<FilterIcon className="h-4 w-4 mr-2" />
								Filters
								{(filters.status !== 'all' || !!filters.trade) && (
									<span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
										{(filters.status !== 'all' ? 1 : 0) + (filters.trade ? 1 : 0)}
									</span>
								)}
							</button>

							{/* Devices */}
							<button
								onClick={handleOpenDevices}
								className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								Devices
							</button>

							{/* Export */}
							<button
								onClick={handleExport}
								className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
							>
								<Download className="h-4 w-4 mr-2" />
								Export
							</button>
						</div>
					</div>

					{/* Active Filters Display - shared */}
					<ActiveFiltersBar
						values={{
							// Map our local filters to modal-like values, so the bar can render nicely
							status: filters.status === 'all' ? '' : filters.status,
							trade: filters.trade || ''
						}}
						config={attendanceFilterConfig}
						onRemove={(filterId) => {
							if (filterId === 'status') setFilters(prev => ({ ...prev, status: 'all' }));
							if (filterId === 'trade') setFilters(prev => ({ ...prev, trade: '' }));
						}}
						onClear={() => setFilters(prev => ({ ...prev, status: 'all', trade: '' }))}
					/>

					{/* Time Logs Table */}
					<div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
						<TimeLogTable
							timeLogs={filteredTimeLogs}
							onEdit={handleEditTimeLog}
							loading={loading}
						/>
					</div>
				</div>
			</div>

      {/* Edit Modal */}
      <EditTimeLogModal
        timeLog={selectedTimeLog}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedTimeLog(null);
        }}
        onSave={handleSaveTimeLog}
      />

      {/* Attendance Filters Modal */}
			<UniversalFilterModal
				isOpen={isFilterOpen}
				onClose={() => setIsFilterOpen(false)}
				title="Filter Attendance"
				filters={attendanceFilterConfig}
				initialValues={{
					status: filters.status === 'all' ? '' : filters.status,
					trade: filters.trade || ''
				}}
				onApplyFilters={handleApplyAttendanceFilters}
				onClearFilters={handleClearAttendanceFilters}
				size="xl"
			/>

    </>
  );
};

export default AttendanceTab;
