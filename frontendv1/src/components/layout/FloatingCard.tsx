import { ReactNode, useEffect } from "react";
import TopBar from "./TopBar";
import { useLayout } from "../../hooks/useLayoutContext";
import { useTopBarConfig } from "../../hooks/useTopBarContext";

interface FloatingCardProps {
  children: ReactNode;
  title?: string; // Now optional since TopBar can auto-detect
  subtitle?: string; // optional subtitle text
  layout?: 'default' | 'custom';
  // Legacy props for backward compatibility during transition
  topBarShowBack?: boolean;
  topBarOnBack?: () => void;
  topBarRightActions?: React.ReactNode;
  topBarMinimal?: boolean;
  // Deprecated props - will be removed after migration
  breadcrumbs?: { name: string; path: string }[];
}




const FloatingCard = ({
  children,
  title,
  subtitle,
  layout = 'default',
  topBarShowBack,
  topBarOnBack,
  topBarRightActions,
  topBarMinimal
}: FloatingCardProps) => {
  const { isFullWidth } = useLayout();
  const { setTitle, setMinimal, setShowBack } = useTopBarConfig();

  // Apply legacy props to TopBar context for backward compatibility
  useEffect(() => {
    if (title) {
      setTitle(title);
    }
  }, [title, setTitle]);

  useEffect(() => {
    if (topBarMinimal !== undefined) {
      setMinimal(topBarMinimal);
    }
  }, [topBarMinimal, setMinimal]);

  useEffect(() => {
    if (topBarShowBack !== undefined) {
      setShowBack(topBarShowBack);
    }
  }, [topBarShowBack, setShowBack]);

  // Calculate container styles based on sidebar visibility
  const containerStyle = isFullWidth
    ? { marginLeft: '0', width: '100vw' }
    : { marginLeft: '72px', width: 'calc(100vw - 72px)' };

  return (
    <div className="p-2.5 min-w-0 h-screen box-border overflow-x-hidden" style={containerStyle}>
      <div className="bg-[#fdfdf9] rounded-[10px] h-full flex flex-col overflow-hidden min-w-0">
        <TopBar rightActions={topBarRightActions} onBack={topBarOnBack} subtitle={subtitle} />
        {layout === 'default' ? (
          <div className="flex-1 overflow-auto p-6 min-w-0">
            {children}
          </div>
        ) : (
          <div className="flex-1 overflow-hidden min-w-0">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingCard;
