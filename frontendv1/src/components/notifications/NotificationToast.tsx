import React from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  Calendar,
  AlertCircle,
  X
} from 'lucide-react';
import { AppNotification, NotificationPriority } from '../../types/notifications';
import { sanitizeToastMessage } from '../../utils/toastUtils';

interface NotificationToastProps {
  notification: AppNotification;
  onClose?: () => void;
  onMarkAsRead?: (id: number) => void;
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onMarkAsRead
}) => {
  const getNotificationIcon = (type: string) => {
    const iconSize = "h-6 w-6";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconSize} />;
      case 'training_expired':
        return <AlertTriangle className={iconSize} />;
      case 'training_assigned':
      case 'training_completed':
      case 'worker_added':
        return <User className={iconSize} />;
      case 'system_alert':
        return <AlertCircle className={iconSize} />;
      case 'permit_expiring':
      case 'permit_expired':
        return <CheckCircle className={iconSize} />;
      case 'safety_incident':
        return <AlertTriangle className={iconSize} />;
      case 'reminder':
        return <Calendar className={iconSize} />;
      default:
        return <Bell className={iconSize} />;
    }
  };

  const getPriorityStyles = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return { iconColor: 'text-red-600' };
      case NotificationPriority.HIGH:
        return { iconColor: 'text-orange-600' };
      case NotificationPriority.MEDIUM:
        return { iconColor: 'text-yellow-600' };
      case NotificationPriority.LOW:
        return { iconColor: 'text-blue-600' };
      default:
        return { iconColor: 'text-gray-600' };
    }
  };

  const priorityStyles = getPriorityStyles(notification.priority);

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClose) {
      onClose();
    }
  };

  const navigate = useNavigate();

  const handleClick = () => {
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  return (
    <div
      className={`relative w-full max-w-sm md:max-w-md p-4 md:p-5 bg-white rounded-xl shadow-xl ring-1 ring-black/5 cursor-pointer hover:shadow-2xl hover:-translate-y-[1px] transition-all duration-200`}
      onClick={handleClick}
      role="alert"
      aria-live="polite"
    >
      {/* Close button */}
      <button
        onClick={handleClose}
        className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-white/50 transition-colors"
      >
        <X className="h-4 w-4" />
      </button>

      <div className="flex items-start gap-4 pr-8">
        {/* Icon */}
        <div className={`flex-shrink-0 p-3 rounded-xl bg-white ring-1 ring-gray-100 shadow ${priorityStyles.iconColor}`}>
          {getNotificationIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-1">
            <h4 className="text-[15px] font-semibold text-gray-900 truncate">
              {notification.title}
            </h4>
          </div>

          <p className="text-sm text-gray-700 leading-relaxed mb-3 line-clamp-2">
            {sanitizeToastMessage(notification.message, 6)}
          </p>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              Just now
            </span>

            <div className="flex items-center space-x-2">
              {notification.actionLabel && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-md text-xs text-green-700 bg-green-50 ring-1 ring-green-100 font-medium">
                  {notification.actionLabel}
                </span>
              )}

              {!notification.readAt && (
                <button
                  onClick={handleMarkAsRead}
                  className="text-xs text-gray-500 hover:text-gray-700 font-medium underline underline-offset-2"
                >
                  Mark as read
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Toast notification functions
export const showNotificationToast = (
  notification: AppNotification,
  onMarkAsRead?: (id: number) => void
) => {
  const toastId = `notification-${notification.id}`;

  // Deduplicate: if a toast with this ID is already active, do not show another
  if (toast.isActive(toastId)) {
    return;
  }

  // Determine toast type based on priority
  const toastType = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return 'error';
      case NotificationPriority.HIGH:
        return 'warning';
      case NotificationPriority.MEDIUM:
        return 'info';
      case NotificationPriority.LOW:
      default:
        return 'default';
    }
  })();

  // Determine auto-close delay based on priority
  const autoClose = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return false; // Don't auto-close critical notifications
      case NotificationPriority.HIGH:
        return 10000; // 10 seconds
      case NotificationPriority.MEDIUM:
        return 7000; // 7 seconds
      case NotificationPriority.LOW:
      default:
        return 5000; // 5 seconds
    }
  })();

  toast(
    <NotificationToast
      notification={notification}
      onMarkAsRead={onMarkAsRead}
      onClose={() => toast.dismiss(toastId)}
    />,
    {
      toastId,
      type: toastType,
      autoClose,
      closeButton: false,
      className: 'notification-toast',

      hideProgressBar: true,
      position: 'top-right',
    }
  );
};

export const dismissNotificationToast = (notificationId: number) => {
  toast.dismiss(`notification-${notificationId}`);
};

export default NotificationToast;
