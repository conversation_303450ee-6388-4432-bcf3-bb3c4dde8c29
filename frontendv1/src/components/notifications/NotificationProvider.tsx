import React, { createContext, useContext, useEffect, useCallback } from 'react';
import { useSubscription } from '@apollo/client';
import { ON_NOTIFICATION } from '../../graphql/subscriptions';
import { useNotifications } from '../../hooks/useNotifications';
import { showNotificationToast } from './NotificationToast';
import { AppNotification, NotificationPriority, NotificationEvent, NotificationStatus } from '../../types/notifications';

interface NotificationContextType {
  // Add any context methods if needed in the future
}

const NotificationContext = createContext<NotificationContextType | null>(null);

interface NotificationProviderProps {
  children: React.ReactNode;
  enableToasts?: boolean;
  enableSounds?: boolean;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  enableToasts = true,
  enableSounds = true
}) => {
  const { markAsRead } = useNotifications({ enableRealTime: false }); // Disable real-time in hook to avoid duplicate subscriptions

  // Deterministic hash for temporary IDs to avoid duplicates
  const hashEvent = useCallback((event: NotificationEvent): number => {
    const base = [
      event.type || '',
      event.title || '',
      event.message || '',
      event.entity || '',
      event.operation || '',
      JSON.stringify(event.metadata ?? {})
    ].join('|');
    // djb2 hash
    let hash = 5381;
    for (let i = 0; i < base.length; i++) {
      hash = ((hash << 5) + hash) + base.charCodeAt(i);
      hash |= 0; // Force 32-bit
    }
    return Math.abs(hash);
  }, []);

  // Convert NotificationEvent to AppNotification
  const convertEventToNotification = useCallback((event: NotificationEvent): AppNotification => {
    return {
      id: hashEvent(event), // Deterministic ID for real-time events
      type: event.type,
      title: event.title,
      message: event.message,
      priority: NotificationPriority.MEDIUM, // Default priority since it's not in the event
      status: NotificationStatus.PENDING,
      entity: event.entity,
      operation: event.operation,
      metadata: event.metadata,
      sentAt: new Date().toISOString(),
      userId: 0, // Will be set by backend
      tenantId: 0, // Will be set by backend
      createdAt: new Date().toISOString(),
      createdBy: 'system',
      deliveries: []
    };
  }, [hashEvent]);

  // Play notification sound
  const playNotificationSound = useCallback((priority: NotificationPriority) => {
    if (!enableSounds) return;

    try {
      const audio = new Audio();

      switch (priority) {
        case NotificationPriority.CRITICAL:
          audio.src = '/sounds/critical-notification.mp3';
          break;
        case NotificationPriority.HIGH:
          audio.src = '/sounds/high-notification.mp3';
          break;
        case NotificationPriority.MEDIUM:
          audio.src = '/sounds/medium-notification.mp3';
          break;
        default:
          audio.src = '/sounds/low-notification.mp3';
      }

      audio.volume = 0.5;
      audio.play().catch(() => {
        // Ignore autoplay restrictions
        console.debug('Could not play notification sound due to browser restrictions');
      });
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [enableSounds]);

  // Handle new notifications from subscription
  const handleNewNotification = useCallback((notification: AppNotification) => {
    // Play sound for high priority notifications
    if (notification.priority === NotificationPriority.HIGH ||
      notification.priority === NotificationPriority.CRITICAL) {
      playNotificationSound(notification.priority);
    }

    // Show toast notification
    if (enableToasts) {
      showNotificationToast(notification, markAsRead);
    }

    // Request notification permission if not granted
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    // Show browser notification for critical notifications
    if (notification.priority === NotificationPriority.CRITICAL &&
      'Notification' in window &&
      Notification.permission === 'granted') {

      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: `notification-${notification.id}`,
        requireInteraction: true, // Keep notification visible until user interacts
      });

      browserNotification.onclick = () => {
        window.focus();
        if (notification.actionUrl) {
          window.location.href = notification.actionUrl;
        }
        browserNotification.close();
      };

      // Auto-close after 30 seconds for critical notifications
      setTimeout(() => {
        browserNotification.close();
      }, 30000);
    }
  }, [enableToasts, playNotificationSound, markAsRead]);

  // Subscribe to real-time notifications
  const { error: subscriptionError } = useSubscription(ON_NOTIFICATION, {
    onSubscriptionData: ({ subscriptionData }) => {
      if (subscriptionData.data?.onNotification) {
        const notificationEvent = subscriptionData.data.onNotification as NotificationEvent;
        const appNotification = convertEventToNotification(notificationEvent);
        handleNewNotification(appNotification);
      }
    },
    onError: (error) => {
      console.error('Notification subscription error:', error);
    }
  });

  // Log subscription errors
  useEffect(() => {
    if (subscriptionError) {
      console.error('Failed to establish notification subscription:', subscriptionError);
    }
  }, [subscriptionError]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('Browser notifications enabled');
        }
      });
    }
  }, []);

  const contextValue: NotificationContextType = {
    // Add context methods here if needed
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationProvider;
