import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Play,
  Calendar,
  MapPin,
  Clock,
  CheckCircle
} from 'lucide-react';
import { mockJobs, type MockJob, type JobStatus } from '../../data/mockJobs';
import TaskStatusBadge from './shared/TaskStatusBadge';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface ActiveTasksListProps {
  siteId: string;
}

const ActiveTasksList: React.FC<ActiveTasksListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    // Simulate loading
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  // Status filters for active jobs (APPROVED only for active tasks)
  const statusFilters: TagOption[] = [
    { id: 'APPROVED', name: 'Approved' }
  ];

  // Get status icon for job status
  const getStatusIcon = (status: JobStatus) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Filtered jobs using useMemo for performance
  const filteredJobsAll = useMemo(() => {
    return mockJobs.filter((job) => {
      // Only show jobs that are approved (ready to start or in progress)
      const isActive = job.status === 'APPROVED';
      
      if (!isActive) return false;

      // Search filter
      const matchesSearch = searchQuery === "" ||
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (job.description && job.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.location && job.location.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.chiefEngineer?.name && job.chiefEngineer.name.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = selectedStatus === "all" ||
        job.status === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [searchQuery, selectedStatus]);

  // Paginated results
  const filteredJobs = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredJobsAll.slice(startIndex, endIndex);
  }, [filteredJobsAll, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredJobsAll.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleJobClick = (jobId: number) => {
    navigate(`/sites/${siteId}/tasks/${jobId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading active jobs...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Active Jobs</h2>
          <p className="text-sm text-gray-600">Approved jobs ready to start or in progress</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredJobsAll.length} jobs
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by job title, location, description, or chief engineer..."
        tags={[{ id: "all", name: "All" }, ...statusFilters]}
        selectedTagId={selectedStatus}
        onTagChange={setSelectedStatus}
      />

      {/* Active Jobs Table */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        {filteredJobs.length === 0 ? (
          <div className="p-12 text-center">
            <Play className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No active jobs found</h3>
            <p className="text-gray-500">
              {searchQuery || selectedStatus !== 'all'
                ? 'Try adjusting your search criteria or filters.'
                : 'No jobs are currently active.'
              }
            </p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Job Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Required Permits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timeline
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Persons
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredJobs.map((job) => (
                <tr key={job.id} className="hover:bg-gray-50 transition-colors">
                  {/* Job Details */}
                  <td className="px-6 py-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(job.status)}
                        <h4 className="text-sm font-medium text-gray-900">
                          {job.title}
                        </h4>
                      </div>
                      <p className="text-xs text-gray-600 mb-1 line-clamp-2">
                        {job.description || 'No description provided'}
                      </p>
                      <div className="flex items-center text-xs text-gray-500">
                        <MapPin className="h-3 w-3 mr-1" />
                        {job.location || 'Location TBD'}
                      </div>
                    </div>
                  </td>

                  {/* Required Permits */}
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {job.requiredPermits?.map((permit, index) => (
                        <span key={index} className={`px-2 py-1 rounded text-xs ${
                          permit === 'GENERAL_WORK_PERMIT' ? 'bg-orange-100 text-orange-700' :
                          permit === 'HOT_WORK_PERMIT' ? 'bg-red-100 text-red-700' :
                          permit === 'EXCAVATION_PERMIT' ? 'bg-blue-100 text-blue-700' :
                          permit === 'WORK_AT_HEIGHT_PERMIT' ? 'bg-purple-100 text-purple-700' :
                          permit === 'CONFINED_SPACE_ENTRY_PERMIT' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {permit.replace('_', ' ').replace('PERMIT', '').trim()}
                        </span>
                      )) || (
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                          No permits required
                        </span>
                      )}
                    </div>
                  </td>

                  {/* Timeline */}
                  <td className="px-6 py-4">
                    <div>
                      <div className="flex items-center text-sm text-gray-900 mb-1">
                        <Calendar className="h-4 w-4 mr-1" />
                        Start: {new Date(job.startDate).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="text-xs text-gray-500">
                        Due: {job.dueDate ? new Date(job.dueDate).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric'
                        }) : 'TBD'}
                      </div>
                      <div className="text-xs text-gray-500">
                        Duration: {job.timeForCompletion || 'TBD'}
                      </div>
                    </div>
                  </td>

                  {/* Persons */}
                  <td className="px-6 py-4">
                    <div className="text-xs">
                      {job.requestedBy && (
                        <div className="text-gray-900 mb-1">
                          Requester: {job.requestedBy.name}
                        </div>
                      )}
                      {job.reviewedBy && (
                        <div className="text-gray-900 mb-1">
                          Reviewer: {job.reviewedBy.name}
                        </div>
                      )}
                      {job.approvedBy && (
                        <div className="text-gray-900 mb-1">
                          Approver: {job.approvedBy.name}
                        </div>
                      )}
                      {job.chiefEngineer && (
                        <div className="text-gray-900">
                          Chief Engineer: {job.chiefEngineer.name}
                        </div>
                      )}
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-6 py-4">
                    <TaskStatusBadge status={job.status.toLowerCase().replace('_', '-') as any} size="sm" />
                  </td>

                  {/* Actions */}
                  <td className="px-6 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleJobClick(job.id)}
                        className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                        style={{ borderRadius: '5px' }}
                        title="View Details"
                      >
                        View
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}

        {/* Pagination */}
        {filteredJobsAll.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredJobsAll.length}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
};

export default ActiveTasksList;
