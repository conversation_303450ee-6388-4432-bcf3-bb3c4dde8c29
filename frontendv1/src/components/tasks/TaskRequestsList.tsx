import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock } from 'lucide-react';
import { mockJobs, type MockJob, type JobStatus } from '../../data/mockJobs';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface TaskRequestsListProps {
  siteId: string;
}

const TaskRequestsList: React.FC<TaskRequestsListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12; // Increased for better grid layout

  useEffect(() => {
    // Simulate loading
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  // Status filters for request management (REQUESTED, BLOCKED, PENDING_APPROVAL, DISAPPROVED)
  const statusFilters: TagOption[] = [
    { id: 'REQUESTED', name: 'Requested' },
    { id: 'BLOCKED', name: 'Blocked' },
    { id: 'PENDING_APPROVAL', name: 'Pending Approval' },
    { id: 'DISAPPROVED', name: 'Disapproved' }
  ];

  // Filtered jobs using useMemo for performance
  const filteredJobsAll = useMemo(() => {
    return mockJobs.filter((job) => {
      // Only show jobs that are in request management states
      const isRequestState = job.status === 'REQUESTED' ||
                            job.status === 'BLOCKED' ||
                            job.status === 'PENDING_APPROVAL' ||
                            job.status === 'DISAPPROVED';

      if (!isRequestState) return false;

      // Search filter
      const matchesSearch = searchQuery === "" ||
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (job.description && job.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.location && job.location.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.requestedBy?.name && job.requestedBy.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.reviewedBy?.name && job.reviewedBy.name.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = selectedStatus === "all" ||
        job.status === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [searchQuery, selectedStatus]);

  // Paginated results
  const filteredJobs = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredJobsAll.slice(startIndex, endIndex);
  }, [filteredJobsAll, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredJobsAll.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleJobClick = (jobId: number) => {
    navigate(`/sites/${siteId}/tasks/${jobId}`);
  };

  // Helper function to render status tags with proper styling
  const renderStatusTag = (status: JobStatus) => {
    const statusMap: Record<JobStatus, { label: string; classes: string }> = {
      'REQUESTED': {
        label: 'Requested',
        classes: 'bg-blue-100 text-blue-800 border border-blue-200'
      },
      'BLOCKED': {
        label: 'Blocked',
        classes: 'bg-red-100 text-red-800 border border-red-200'
      },
      'PENDING_APPROVAL': {
        label: 'Pending Approval',
        classes: 'bg-yellow-100 text-yellow-800 border border-yellow-200'
      },
      'DISAPPROVED': {
        label: 'Disapproved',
        classes: 'bg-red-100 text-red-800 border border-red-200'
      },
      'APPROVED': {
        label: 'Approved',
        classes: 'bg-green-100 text-green-800 border border-green-200'
      },
      'FINISHED': {
        label: 'Finished',
        classes: 'bg-green-100 text-green-800 border border-green-200'
      }
    };

    const statusInfo = statusMap[status];
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded ${statusInfo.classes}`}>
        {statusInfo.label}
      </span>
    );
  };

  // Category tag styling
  const categoryTagClasses = 'bg-gray-100 text-gray-800 border border-gray-200';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading job requests...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Job Requests</h2>
          <p className="text-sm text-gray-600">Manage job requests requiring approval or review</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredJobsAll.length} requests
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by job title, location, description, or requester..."
        tags={[{ id: "all", name: "All" }, ...statusFilters]}
        selectedTagId={selectedStatus}
        onTagChange={setSelectedStatus}
      />

      {/* Job Requests Cards Grid */}
      {filteredJobs.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-12 text-center">
          <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No job requests found</h3>
          <p className="text-gray-500">
            {searchQuery || selectedStatus !== 'all'
              ? 'Try adjusting your search criteria or filters.'
              : 'No job requests are currently pending.'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {filteredJobs.map((job) => (
            <div
              key={job.id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm p-5 hover:shadow-md transition-shadow min-h-[280px] flex flex-col"
            >
              {/* Header */}
              <div>
                <h3 className="text-base font-semibold text-gray-900">{job.title}</h3>
                <p className="text-sm text-gray-600 mt-1 leading-relaxed line-clamp-2">
                  {job.description || 'No description provided'}
                </p>
              </div>

              {/* Status and Permits Tags */}
              <div className="mt-3 flex items-center gap-2 flex-wrap">
                {renderStatusTag(job.status)}
                {job.category && (
                  <span className={`px-2 py-1 text-xs font-medium rounded ${categoryTagClasses}`}>
                    {job.category.description}
                  </span>
                )}
              </div>

              {/* Required Permits */}
              {job.requiredPermits && job.requiredPermits.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs font-medium text-gray-500 mb-1">Required Permits:</p>
                  <div className="flex flex-wrap gap-1">
                    {job.requiredPermits.map((permit, index) => (
                      <span key={index} className={`px-2 py-1 rounded text-xs ${
                        permit === 'GENERAL_WORK_PERMIT' ? 'bg-orange-100 text-orange-700' :
                        permit === 'HOT_WORK_PERMIT' ? 'bg-red-100 text-red-700' :
                        permit === 'EXCAVATION_PERMIT' ? 'bg-blue-100 text-blue-700' :
                        permit === 'WORK_AT_HEIGHT_PERMIT' ? 'bg-purple-100 text-purple-700' :
                        permit === 'CONFINED_SPACE_ENTRY_PERMIT' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {permit.replace('_', ' ').replace('PERMIT', '').trim()}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Bottom section: metadata + divider + footer */}
              <div className="mt-auto">
                {/* Metadata */}
                <div className="mt-3 space-y-1.5 text-xs text-gray-700">
                  <div><span className="text-gray-500">Location:</span> <span>{job.location || 'TBD'}</span></div>
                  {job.requestedBy && (
                    <div><span className="text-gray-500">Requester:</span> <span>{job.requestedBy.name}</span></div>
                  )}
                  {job.reviewedBy && (
                    <div><span className="text-gray-500">Reviewer:</span> <span>{job.reviewedBy.name}</span></div>
                  )}
                  {job.approvedBy && (
                    <div><span className="text-gray-500">Approver:</span> <span>{job.approvedBy.name}</span></div>
                  )}
                  {job.blockedBy && (
                    <div><span className="text-gray-500">Blocked by:</span> <span>{job.blockedBy.name}</span></div>
                  )}
                </div>

                {/* Divider */}
                <div className="my-4 border-t border-gray-200" />

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    <div className="font-medium text-gray-700">JOB-2024-{job.id.toString().padStart(3, '0')}</div>
                    <div className="mt-0.5">Requested: {new Date(job.createdAt).toLocaleDateString()}</div>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleJobClick(job.id)}
                    className="px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors w-20"
                  >
                    View
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {filteredJobsAll.length > 0 && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredJobsAll.length}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default TaskRequestsList;
