import React, { useState, useEffect, useMemo } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>dingUp,
	Download,
	Printer,
	CheckCircle,
	Clock,
	Users,
	AlertTriangle,
	Calendar,
	FileText,
	Target
} from "lucide-react";
import { mockJobs, mockJobStats, type MockJob, type JobStatus } from '../../data/mockJobs';
import {
	BarChart,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	<PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
	Cell,
	LineChart,
	Line,
} from "recharts";

interface TasksReportsProps {
	siteId: string;
}

// Generate realistic report data from actual job data
const generateReportData = () => {
	// Job completion rates by status
	const jobsByStatus = mockJobs.reduce((acc, job) => {
		const status = job.status;
		const existing = acc.find(item => item.name === status);
		if (existing) {
			existing.count++;
		} else {
			acc.push({ name: status, count: 1 });
		}
		return acc;
	}, [] as { name: string; count: number }[]);

	// Permit type distribution
	const permitDistribution = mockJobs.reduce((acc, job) => {
		job.requiredPermits?.forEach(permit => {
			const existing = acc.find(item => item.name === permit);
			if (existing) {
				existing.count++;
			} else {
				acc.push({ name: permit, count: 1 });
			}
		});
		return acc;
	}, [] as { name: string; count: number }[]);

	// Average approval times (mock realistic data)
	const approvalTimes = [
		{ status: 'REQUESTED', avgHours: 2.5 },
		{ status: 'PENDING_APPROVAL', avgHours: 8.2 },
		{ status: 'APPROVED', avgHours: 0 },
		{ status: 'BLOCKED', avgHours: 24.5 },
		{ status: 'DISAPPROVED', avgHours: 6.8 }
	];

	// Worker productivity (based on jobs assigned)
	const workerProductivity = mockJobs.reduce((acc, job) => {
		if (job.chiefEngineer) {
			const existing = acc.find(item => item.name === job.chiefEngineer!.name);
			if (existing) {
				existing.jobsAssigned++;
				if (job.status === 'FINISHED') existing.jobsCompleted++;
			} else {
				acc.push({
					name: job.chiefEngineer.name,
					jobsAssigned: 1,
					jobsCompleted: job.status === 'FINISHED' ? 1 : 0,
					efficiency: 0
				});
			}
		}
		return acc;
	}, [] as { name: string; jobsAssigned: number; jobsCompleted: number; efficiency: number }[]);

	// Calculate efficiency percentages
	workerProductivity.forEach(worker => {
		worker.efficiency = worker.jobsAssigned > 0 ? Math.round((worker.jobsCompleted / worker.jobsAssigned) * 100) : 0;
	});

	return {
		jobsByStatus,
		permitDistribution,
		approvalTimes,
		workerProductivity
	};
};

const mockTasksByCategory = [
	{ name: "Construction", count: 15, percentage: 36 },
	{ name: "Electrical", count: 8, percentage: 19 },
	{ name: "Plumbing", count: 6, percentage: 14 },
	{ name: "HVAC", count: 5, percentage: 12 },
	{ name: "Safety", count: 4, percentage: 10 },
	{ name: "Other", count: 4, percentage: 9 },
];

const mockWeeklyProgress = [
	{ week: "Week 1", planned: 12, completed: 10, efficiency: 83 },
	{ week: "Week 2", planned: 15, completed: 14, efficiency: 93 },
	{ week: "Week 3", planned: 18, completed: 16, efficiency: 89 },
	{ week: "Week 4", planned: 14, completed: 13, efficiency: 93 },
];

const mockProductivityTrends = [
	{ month: "Oct", productivity: 85, onTime: 78 },
	{ month: "Nov", productivity: 88, onTime: 82 },
	{ month: "Dec", productivity: 92, onTime: 87 },
	{ month: "Jan", productivity: 89, onTime: 85 },
];

// Permit-related mock data
const mockPermitsByStatus = [
  { name: 'Active', count: 8, percentage: 33 },
  { name: 'Pending Approval', count: 3, percentage: 13 },
  { name: 'Expired', count: 2, percentage: 8 },
  { name: 'Closed', count: 11, percentage: 46 }
];

const mockPermitsByType = [
  { name: 'Hot Work', count: 6, percentage: 25 },
  { name: 'Confined Space', count: 4, percentage: 17 },
  { name: 'Working at Height', count: 5, percentage: 21 },
  { name: 'Electrical Work', count: 3, percentage: 13 },
  { name: 'Excavation', count: 3, percentage: 13 },
  { name: 'Other', count: 3, percentage: 13 }
];

const COLORS = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#6B7280'];

const TasksReports: React.FC<TasksReportsProps> = ({ siteId }) => {
	const [reportType, setReportType] = useState("overview");
	const [dateRange, setDateRange] = useState("last-30");
	const [loading, setLoading] = useState(true);

	// Generate report data from actual job data
	const reportData = useMemo(() => generateReportData(), []);

	useEffect(() => {
		// Simulate loading
		setLoading(true);
		setTimeout(() => setLoading(false), 1000);
	}, [siteId, reportType, dateRange]);

	const generateReport = () => {
		// Generate custom report based on selected parameters
		console.log("Generating custom task report...");
	};

	const exportReport = (format: "pdf" | "excel") => {
		console.log(`Exporting task report as ${format}`);
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-gray-500">Loading reports...</div>
			</div>
		);
	}

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tasks & Permits Reports</h2>
        <div className="flex space-x-2">
          <button 
            onClick={() => exportReport('pdf')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Printer className="h-4 w-4 mr-1" />
            Print
          </button>
          <button 
            onClick={() => exportReport('excel')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Jobs</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{mockJobs.length}</p>
            </div>
            <div className="text-blue-600">
              <FileText className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Completion Rate</p>
              <p className="mt-2 text-2xl font-semibold text-green-600">
                {Math.round((mockJobs.filter(j => j.status === 'FINISHED').length / mockJobs.length) * 100)}%
              </p>
            </div>
            <div className="text-green-600">
              <Target className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Avg. Approval Time</p>
              <p className="mt-2 text-2xl font-semibold text-yellow-600">6.2h</p>
            </div>
            <div className="text-yellow-600">
              <Clock className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Workers</p>
              <p className="mt-2 text-2xl font-semibold text-purple-600">
                {new Set(mockJobs.flatMap(j => j.workers?.map(w => w.id) || [])).size}
              </p>
            </div>
            <div className="text-purple-600">
              <Users className="h-6 w-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Report Templates */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 className="text-lg font-medium mb-4">Quick Reports</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <PieChart className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Job Status Distribution</h4>
            </div>
            <p className="text-sm text-gray-500">Breakdown of jobs by current status</p>
          </button>

					<button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
						<div className="flex items-center mb-2">
							<TrendingUp className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Productivity Trends</h4>
						</div>
						<p className="text-sm text-gray-500">
							Weekly and monthly productivity analysis
						</p>
					</button>

          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Performance Metrics</h4>
            </div>
            <p className="text-sm text-gray-500">Completion rates and efficiency metrics</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Permit Analysis</h4>
            </div>
            <p className="text-sm text-gray-500">Permit approval times and compliance</p>
          </button>
        </div>
      </div>

			{/* Custom Report Builder */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Custom Report</h3>
				<div className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Report Type
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={reportType}
								onChange={(e) => setReportType(e.target.value)}
							>
								<option value="overview">Overview Report</option>
								<option value="productivity">Productivity Report</option>
								<option value="delays">Delays & Bottlenecks</option>
								<option value="worker-performance">Worker Performance</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Date Range
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={dateRange}
								onChange={(e) => setDateRange(e.target.value)}
							>
								<option value="last-7">Last 7 Days</option>
								<option value="last-30">Last 30 Days</option>
								<option value="last-90">Last 90 Days</option>
								<option value="year-to-date">Year to Date</option>
								<option value="custom">Custom Range</option>
							</select>
						</div>
						<div className="flex items-end">
							<button
								onClick={generateReport}
								className="w-full px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
							>
								Generate Report
							</button>
						</div>
					</div>
				</div>
			</div>

      {/* Report Visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Jobs by Status */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Jobs by Status</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <RechartsPieChart data={reportData.jobsByStatus} cx="50%" cy="50%" outerRadius={80} dataKey="count">
                  {reportData.jobsByStatus.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </RechartsPieChart>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            {reportData.jobsByStatus.map((item, index) => (
              <div key={item.name} className="flex items-center text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                ></div>
                <span className="text-gray-600">{item.name.replace('_', ' ')}: {item.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Permit Type Distribution */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Required Permits Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={reportData.permitDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [value, 'Jobs Requiring']}
                  labelFormatter={(label) => label.replace(/_/g, ' ').replace('PERMIT', '').trim()}
                />
                <Bar dataKey="count" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Average Approval Times */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Average Approval Times by Status</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={reportData.approvalTimes}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="status"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis label={{ value: 'Hours', angle: -90, position: 'insideLeft' }} />
                <Tooltip
                  formatter={(value) => [`${value} hours`, 'Avg. Time']}
                  labelFormatter={(label) => label.replace('_', ' ')}
                />
                <Bar dataKey="avgHours" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Permits by Type */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Permits by Type</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockPermitsByType}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Additional Reports Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Worker Productivity */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Chief Engineer Productivity</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={reportData.workerProductivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    value,
                    name === 'jobsAssigned' ? 'Jobs Assigned' :
                    name === 'jobsCompleted' ? 'Jobs Completed' :
                    'Efficiency %'
                  ]}
                />
                <Bar dataKey="jobsAssigned" fill="#93C5FD" name="Jobs Assigned" />
                <Bar dataKey="jobsCompleted" fill="#10B981" name="Jobs Completed" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-blue-300 mr-2"></div>
                <span>Jobs Assigned</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span>Jobs Completed</span>
              </div>
            </div>
          </div>
        </div>

        {/* Timeline Adherence */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Timeline Adherence Analysis</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-green-800">On Time Completion</p>
                <p className="text-xs text-green-600">Jobs finished within planned timeframe</p>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {Math.round((mockJobs.filter(j => j.status === 'FINISHED').length / mockJobs.length) * 100)}%
              </div>
            </div>

            <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-yellow-800">Average Duration</p>
                <p className="text-xs text-yellow-600">Typical job completion time</p>
              </div>
              <div className="text-2xl font-bold text-yellow-600">
                {mockJobStats.averageCompletionTime}h
              </div>
            </div>

            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-blue-800">Productivity Score</p>
                <p className="text-xs text-blue-600">Overall efficiency rating</p>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {mockJobStats.productivityScore}%
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 className="text-lg font-medium mb-4">Summary Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {mockJobs.filter(j => j.status === 'FINISHED').length}
            </div>
            <div className="text-sm text-gray-600">Completed Jobs</div>
            <div className="text-xs text-gray-500 mt-1">
              {Math.round((mockJobs.filter(j => j.status === 'FINISHED').length / mockJobs.length) * 100)}% of total
            </div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {mockJobs.filter(j => j.status === 'PENDING_APPROVAL').length}
            </div>
            <div className="text-sm text-gray-600">Pending Approval</div>
            <div className="text-xs text-gray-500 mt-1">
              Awaiting HSE review
            </div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600 mb-2">
              {new Set(mockJobs.flatMap(j => j.requiredPermits || [])).size}
            </div>
            <div className="text-sm text-gray-600">Permit Types</div>
            <div className="text-xs text-gray-500 mt-1">
              Different permit categories
            </div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {new Set(mockJobs.map(j => j.chiefEngineer?.name).filter(Boolean)).size}
            </div>
            <div className="text-sm text-gray-600">Active Engineers</div>
            <div className="text-xs text-gray-500 mt-1">
              Managing jobs
            </div>
          </div>
        </div>
      </div>

			{/* Task Summary Table */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<div className="flex justify-between items-center mb-4">
					<h3 className="text-lg font-medium">Task Summary</h3>
					<div className="flex space-x-2">
						<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
							<Printer className="h-4 w-4 inline mr-1" />
							Print
						</button>
						<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
							<Download className="h-4 w-4 inline mr-1" />
							Export
						</button>
					</div>
				</div>

				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Category
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Total Tasks
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Completed
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									In Progress
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Completion Rate
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{mockTasksByCategory.map((category) => (
								<tr key={category.name}>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
										{category.name}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{category.count}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(category.count * 0.7)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(category.count * 0.3)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(70 + Math.random() * 25)}%
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default TasksReports;
