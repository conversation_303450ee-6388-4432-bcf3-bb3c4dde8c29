import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
	ClipboardCheck,
	Clock,
	XCircle,
	Timer,
	Users,
	MapPin,
	Download,
	ExternalLink,
} from "lucide-react";
import { PermitStats, Permit } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";
import KPICard from "./shared/KPICard";
import QuickActions, { QuickActionItem } from "../shared/QuickActions";
 

interface PermitsDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

// Mock data - replace with actual API calls
const mockPermitStats: PermitStats = {
	totalPermits: 156,
	activePermits: 12,
	pendingApproval: 6,
	expiringSoon: 2,
	expired: 0,
	closedToday: 5,
	averageApprovalTime: 2.5,
};

 

const mockRecentPermits: Permit[] = [
	{
		id: "permit-1",
		permitNumber: "HW-2024-001",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Structural Steel Welding - Level 3",
		description: "Welding operations for structural steel installation",
		location: "Zone A - Level 3",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T08:00:00"),
		validFrom: new Date("2024-01-15T09:00:00"),
		validUntil: new Date("2024-01-15T17:00:00"),
		status: "open",
		priority: "high",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		assignedWorkers: [],
		supervisorName: "John Smith",
		taskReference: "TSK-2024-001",
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T07:30:00"),
		updatedAt: new Date("2024-01-15T09:00:00"),
		history: [],
	},
	{
		id: "permit-2",
		permitNumber: "CS-2024-002",
		permitType: {
			id: "confined-space",
			name: "Confined Space Entry",
			description: "For entry into confined spaces",
			category: "High Risk",
			defaultValidityHours: 4,
			requiredTrainings: ["confined-space-entry"],
			requiredCertifications: ["gas-monitor-cert"],
			
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Tank Inspection - Storage Tank 3",
		description: "Internal inspection of water storage tank",
		location: "Zone B - Tank Farm",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T10:00:00"),
		status: "open",
		priority: "critical",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		assignedWorkers: [],
		supervisorName: "John Smith",
		taskReference: "TSK-2024-002",
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T09:45:00"),
		updatedAt: new Date("2024-01-15T09:45:00"),
		history: [],
	},
];

const PermitsDashboard: React.FC<PermitsDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	const navigate = useNavigate();
	const [stats, _setStats] = useState<PermitStats>(mockPermitStats);
	const [recentPermits, _setRecentPermits] =
		useState<Permit[]>(mockRecentPermits);
	

	useEffect(() => {
		// Fetch permit statistics and recent permits
		// This would be replaced with actual API calls
		console.log(`Fetching permit data for site ${siteId}`);
	}, [siteId]);
	

	const handlePermitClick = (permit: Permit) => {
		const permitTypeMap: { [key: string]: string } = {
			'hot-work': 'hot-work',
			'confined-space': 'confined-space',
			'work-at-height': 'work-at-height',
			'excavation': 'excavation',
			'general-work': 'general-work'
		};

		const permitType = permitTypeMap[permit.permitType.id] || 'general-work';
		if (siteId) {
			navigate(`/sites/${siteId}/permits/${permitType}/${permit.id}`);
		} else {
			navigate(`/permits/${permitType}/${permit.id}`);
		}
	};

	const handleTaskReferenceClick = (taskReference: string) => {
		const match = taskReference.match(/TASK-(\d{6})/);
		if (match) {
			const taskId = match[1];
			console.log("Navigate to task:", taskId);
		}
	};

	return (
		<div className="space-y-6">
			{/* KPI Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<KPICard
					title="Active Permits"
					value={stats.activePermits}
					change={8}
					icon={<ClipboardCheck className="h-6 w-6 text-green-500" />}
					onClick={() => onNavigateToTab('active')}
				/>
				<KPICard
					title="Pending Permits (Tomorrow)"
					value={stats.pendingApproval}
					icon={<Clock className="h-6 w-6 text-orange-500" />}
					onClick={() => onNavigateToTab('pending')}
				/>
				<KPICard
					title="Cancelled"
					value={stats.expired}
					icon={<XCircle className="h-6 w-6 text-red-500" />}
				/>
				<KPICard
					title="Overtime"
					value={stats.expiringSoon}
					icon={<Timer className="h-6 w-6 text-yellow-500" />}
				/>
			</div>

			{/* Quick Actions (Reusable) */}
			<div className="mb-2">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
				<QuickActions
				actions={([
					{
						title: "Generate Permit",
						description: "Create a new permit",
						icon: <ClipboardCheck className="h-6 w-6 text-indigo-600" />,
						onClick: () => onNavigateToTab('pending'),
					},
					{
						title: "Active Permits",
						description: "View active permits",
						icon: <Clock className="h-6 w-6 text-orange-600" />,
						onClick: () => onNavigateToTab('active'),
					},
					{
						title: "Permit History",
						description: "Browse historical permits",
						icon: <XCircle className="h-6 w-6 text-blue-600" />,
						onClick: () => onNavigateToTab('history'),
					},
					{
						title: "Learn More",
						description: "About permit types",
						icon: <Timer className="h-6 w-6 text-purple-600" />,
						onClick: () => onNavigateToTab('types'),
					},
				] as QuickActionItem[])}
				className=""
			/>
			</div>

			{/* Removed: Quick Actions, Permits by Type, Today's Summary */}

			{/* Recent Permits - aligned with Active Permits layout */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
					<div>
						<h3 className="text-lg font-medium text-gray-900">Recent Permits</h3>
						{/* Removed subheader text to match requirement */}
					</div>
					<button
						onClick={() => onNavigateToTab("active")}
						className="text-sm text-green-600 hover:text-green-800"
					>
						View All
					</button>
				</div>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permit Details</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task Details</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Persons</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Actions</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{recentPermits.map((permit) => (
							<tr key={permit.id} className="hover:bg-gray-50 transition-colors">
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center space-x-2 mb-1">
											<h4 className="text-sm font-medium text-gray-900">{permit.permitNumber}</h4>
											<PermitStatusBadge status={permit.status} size="sm" />
										</div>
										<p className="text-xs text-gray-600 mb-1">{permit.permitType.name}</p>
										<div className="flex items-center text-xs text-orange-600">
											<Clock className="h-3 w-3 mr-1" />
											{permit.validUntil ? 'Expires end of day' : 'Pending approval'}
										</div>
									</div>
								</td>
								<td className="px-6 py-4">
									<div>
										<h4 className="text-sm font-medium text-gray-900 mb-1">{permit.title}</h4>
										<p className="text-xs text-gray-600 mb-1 line-clamp-2">{permit.description}</p>
										<div className="flex items-center text-xs text-gray-500 mb-2">
											<MapPin className="h-3 w-3 mr-1" />
											{permit.location}
										</div>
										{(permit as any).taskReference && (
											<div className="mt-2">
												<button
													onClick={() => handleTaskReferenceClick((permit as any).taskReference)}
													className="flex items-center text-xs text-blue-600 font-mono hover:text-blue-800 hover:underline transition-colors"
													title="Click to view related task"
												>
													Task: {(permit as any).taskReference}
													<ExternalLink className="h-3 w-3 ml-1" />
												</button>
											</div>
										)}
									</div>
								</td>
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center text-sm text-gray-900 mb-1">
											<Users className="h-4 w-4 mr-1" />
											{permit.assignedWorkers?.length || 0} persons
										</div>
										<div className="text-xs text-gray-600">Supervisor: {permit.supervisorName || 'John Smith'}</div>
									</div>
								</td>
								<td className="px-6 py-4">
									<div className="flex space-x-2">
										<button
											onClick={() => handlePermitClick(permit)}
											className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
											style={{ borderRadius: '5px' }}
										>
											View
										</button>
										<button
											onClick={(e) => { e.stopPropagation(); console.log("Downloading permit:", permit.id); }}
											className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors flex items-center"
											style={{ borderRadius: '5px' }}
										>
											<Download className="h-3 w-3" />
										</button>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
 
			
		</div>
	);
};

export default PermitsDashboard;
