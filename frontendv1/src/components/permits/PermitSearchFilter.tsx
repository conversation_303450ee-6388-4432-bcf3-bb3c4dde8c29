import React from 'react';
import { Search } from 'lucide-react';

export interface PermitTypeFilter {
  id: string;
  name: string;
  color: string;
}

export interface PermitSearchFilterProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  searchPlaceholder?: string;
  
  // Permit type filters
  permitTypes: PermitTypeFilter[];
  selectedPermitType: string;
  onPermitTypeChange: (type: string) => void;
  
  // Results count
  resultsCount?: number;
  totalCount?: number;
  
  className?: string;
}

export const PermitSearchFilter: React.FC<PermitSearchFilterProps> = ({
  searchQuery,
  onSearchChange,
  searchPlaceholder = "Search by task name, description, engineer, or location...",
  permitTypes,
  selectedPermitType,
  onPermitTypeChange,
  className = ""
}) => {
  return (
    <div className={`${className}`}>
      {/* Search and Filter Row */}
      <div className="flex items-center justify-between gap-4">
        {/* Search Bar - Takes 1/3 of available width */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder={searchPlaceholder}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>

        {/* Permit Type Filter Pills - Right aligned */}
        <div className="flex items-center space-x-2">
          {/* All permits pill */}
          <button
            onClick={() => onPermitTypeChange("all")}
            className={`px-3 py-1.5 text-sm font-medium rounded-full border transition-colors ${
              selectedPermitType === "all"
                ? "bg-blue-50 text-blue-700 border-blue-200"
                : "bg-[#f3f4f6] text-gray-700 border-gray-200 hover:bg-gray-100"
            }`}
            style={{}}
          >
            All
          </button>

          {/* Individual permit type pills */}
          {permitTypes.map((permitType) => (
            <button
              key={permitType.id}
              onClick={() => onPermitTypeChange(permitType.id)}
              className={`px-3 py-1.5 text-sm font-medium rounded-full border transition-colors ${
                selectedPermitType === permitType.id
                  ? "bg-blue-50 text-blue-700 border-blue-200"
                  : "bg-[#f3f4f6] text-gray-700 border-gray-200 hover:bg-gray-100"
              }`}
              style={{}}
            >
              {permitType.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PermitSearchFilter;
