import React from 'react';
import { User, Phone, Mail, Award, Shield, Calendar } from 'lucide-react';
import { PermitWorker } from '../../../types/permits';

interface WorkerTabProps {
  worker: PermitWorker;
}

const WorkerTab: React.FC<WorkerTabProps> = ({ worker }) => {
  if (!worker) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">Worker Information</h3>
          <p>No worker data available.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{worker.workerName}</h1>
              <p className="text-sm text-gray-600">{worker.primaryTrade}</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Name:</div>
                  <div className="text-sm text-gray-900">{worker.workerName}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-6 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Worker ID:</div>
                  <div className="text-sm text-gray-900 font-mono">{worker.workerId}</div>
                </div>
                <div className="flex items-center">
                  <Award className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Trade:</div>
                  <div className="text-sm text-gray-900">{worker.primaryTrade}</div>
                </div>
                {/* {worker.competencyLevel && (
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 text-green-400 mr-2" />
                    <div className="w-24 text-sm font-medium text-gray-500">Level:</div>
                    <div className="text-sm text-green-600 font-medium">{worker.competencyLevel}</div>
                  </div>
                )} */}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Phone:</div>
                  <div className="text-sm text-gray-900">+****************</div>
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Email:</div>
                  <div className="text-sm text-gray-900">{worker.workerName.toLowerCase().replace(' ', '.')}@company.com</div>
                </div>
              </div>
            </div>
          </div>

          {/* Certifications */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Certifications & Training</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Shield className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium text-green-900">Safety Training</h4>
                </div>
                <p className="text-sm text-green-700">Valid until: Dec 31, 2024</p>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Award className="h-5 w-5 text-blue-600 mr-2" />
                  <h4 className="font-medium text-blue-900">{worker.primaryTrade} Certification</h4>
                </div>
                <p className="text-sm text-blue-700">Valid until: Jun 30, 2025</p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Calendar className="h-5 w-5 text-yellow-600 mr-2" />
                  <h4 className="font-medium text-yellow-900">First Aid Training</h4>
                </div>
                <p className="text-sm text-yellow-700">Valid until: Mar 15, 2025</p>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Shield className="h-5 w-5 text-purple-600 mr-2" />
                  <h4 className="font-medium text-purple-900">Site Induction</h4>
                </div>
                <p className="text-sm text-purple-700">Completed: Jan 15, 2024</p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Permit Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-900">Hot Work Permit - Zone A</div>
                <div className="text-xs text-gray-500">2 days ago</div>
              </div>
              <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-900">Confined Space Permit - Tank 3</div>
                <div className="text-xs text-gray-500">1 week ago</div>
              </div>
              <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-900">Work at Height Permit - Building A</div>
                <div className="text-xs text-gray-500">2 weeks ago</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkerTab;
