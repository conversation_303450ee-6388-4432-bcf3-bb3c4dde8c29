import React from 'react';
import { Clock, MapPin, User, Users, Calendar, AlertTriangle } from 'lucide-react';
import { Permit } from '../../../types/permits';
import PermitStatusBadge from '../shared/PermitStatusBadge';

interface PermitDetailsTabProps {
  permit: Permit;
}

const PermitDetailsTab: React.FC<PermitDetailsTabProps> = ({ permit }) => {
  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{permit.title}</h1>
              <p className="text-sm text-gray-600 mt-1">{permit.permitNumber}</p>
            </div>
            <div className="flex items-center space-x-3">
              <PermitStatusBadge status={permit.status} />
              <div className="text-xs text-orange-600 bg-orange-50 px-3 py-1 rounded-full border border-orange-200">
                Expires end of day
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Permit Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-32 text-sm font-medium text-gray-500">Permit Type:</div>
                  <div className="text-sm text-gray-900">{permit.permitType.name}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-32 text-sm font-medium text-gray-500">Priority:</div>
                  <div className={`text-sm font-medium ${
                    permit.priority === 'critical' ? 'text-red-600' :
                    permit.priority === 'high' ? 'text-orange-600' :
                    permit.priority === 'medium' ? 'text-yellow-600' :
                    'text-green-600'
                  }`}>
                    {permit.priority?.charAt(0).toUpperCase() + permit.priority?.slice(1)}
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-32 text-sm font-medium text-gray-500">Description:</div>
                  <div className="text-sm text-gray-900 flex-1">{permit.description}</div>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Location:</div>
                  <div className="text-sm text-gray-900">{permit.location}</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Requested:</div>
                  <div className="text-sm text-gray-900">
                    {permit.requestedDate.toLocaleDateString()} at {permit.requestedDate.toLocaleTimeString()}
                  </div>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Valid From:</div>
                  <div className="text-sm text-gray-900">
                    {permit.validFrom?.toLocaleDateString()} at {permit.validFrom?.toLocaleTimeString()}
                  </div>
                </div>
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-orange-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Valid Until:</div>
                  <div className="text-sm text-orange-600 font-medium">
                    {permit.validUntil?.toLocaleDateString()} at {permit.validUntil?.toLocaleTimeString()}
                  </div>
                </div>
                {permit.actualStartTime && (
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-green-400 mr-2" />
                    <div className="w-28 text-sm font-medium text-gray-500">Started:</div>
                    <div className="text-sm text-green-600">
                      {permit.actualStartTime.toLocaleDateString()} at {permit.actualStartTime.toLocaleTimeString()}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* People Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Requestor Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Requested By:</div>
                  <div className="text-sm text-gray-900">{permit.requestedByName}</div>
                </div>
                {permit.supervisorName && (
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-blue-400 mr-2" />
                    <div className="w-28 text-sm font-medium text-gray-500">Supervisor:</div>
                    <div className="text-sm text-gray-900">{permit.supervisorName}</div>
                  </div>
                )}
                {permit.engineerName && (
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-green-400 mr-2" />
                    <div className="w-28 text-sm font-medium text-gray-500">Engineer:</div>
                    <div className="text-sm text-gray-900">{permit.engineerName}</div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Assigned Workers</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-28 text-sm font-medium text-gray-500">Total:</div>
                  <div className="text-sm text-gray-900">{permit.assignedWorkers?.length || 0} persons</div>
                </div>
                {permit.assignedWorkers && permit.assignedWorkers.length > 0 && (
                  <div className="mt-3">
                    <div className="text-sm font-medium text-gray-500 mb-2">Worker List:</div>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {permit.assignedWorkers.map((worker) => (
                        <div key={worker.workerId} className="text-sm text-gray-700 bg-gray-50 px-3 py-1 rounded">
                          {worker.workerName} - {worker.primaryTrade}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Task Reference */}
          {permit.taskReference && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Information</h3>
              <div className="flex items-center">
                <div className="w-32 text-sm font-medium text-gray-500">Task Reference:</div>
                <div className="text-sm text-blue-600 font-mono bg-blue-50 px-2 py-1 rounded">
                  {permit.taskReference}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PermitDetailsTab;
