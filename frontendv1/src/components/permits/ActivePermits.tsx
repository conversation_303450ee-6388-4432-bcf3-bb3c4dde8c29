import React, { useState, useEffect, useRef, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
	Clock,
	Users,
	MapPin,
	ClipboardCheck,
	X,
	ChevronDown,
	Download,
	ExternalLink,
} from "lucide-react";
import { Permit, PermitFilters } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";
import Pagination from "../shared/Pagination";

interface ActivePermitsProps {
	siteId: string;
}

// Local types
interface NotifyUser {
  id: string;
  name: string;
  role: string;
}

// Mock data - replace with actual API calls
const mockActivePermits: Permit[] = [
	{
		id: "permit-1",
		permitNumber: "HW-2024-001",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Structural Steel Welding - Level 3",
		description: "Welding operations for structural steel installation",
		location: "Zone A - Level 3",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T08:00:00"),
		validFrom: new Date("2024-01-15T09:00:00"),
		validUntil: new Date("2024-01-15T17:00:00"),
		actualStartTime: new Date("2024-01-15T09:00:00"),
		status: "open",
		priority: "high",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		supervisorId: "supervisor-1",
		supervisorName: "John Smith",
		engineerId: "engineer-1",
		engineerName: "Sarah Wilson",
		taskReference: "TSK-2024-001",
		assignedWorkers: [
			{
				workerId: "worker-1",
				workerName: "Mike Johnson",
				primaryTrade: "Welder",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-15T08:45:00"),
			},
			{
				workerId: "worker-2",
				workerName: "David Wilson",
				primaryTrade: "Welder",
				role: "supervisor",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-15T08:45:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T07:30:00"),
		updatedAt: new Date("2024-01-15T09:00:00"),
		history: [],
	},
	{
		id: "permit-2",
		permitNumber: "CS-2024-002",
		permitType: {
			id: "confined-space",
			name: "Confined Space Entry",
			description: "For entry into confined spaces",
			category: "High Risk",
			defaultValidityHours: 4,
			requiredTrainings: ["confined-space-entry"],
			requiredCertifications: ["gas-monitor-cert"],
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Tank Inspection - Storage Tank 3",
		description: "Internal inspection of water storage tank",
		location: "Zone B - Tank Farm",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T10:00:00"),
		validFrom: new Date("2024-01-15T10:00:00"),
		validUntil: new Date("2024-01-15T18:00:00"),
		actualStartTime: new Date("2024-01-15T10:00:00"),
		status: "open",
		priority: "critical",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		supervisorId: "supervisor-2",
		supervisorName: "Sarah Johnson",
		engineerId: "engineer-2",
		engineerName: "Michael Chen",
		taskReference: "TSK-2024-002",
		assignedWorkers: [
			{
				workerId: "worker-3",
				workerName: "Robert Brown",
				primaryTrade: "Inspector",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T09:45:00"),
		updatedAt: new Date("2024-01-15T09:45:00"),
		history: [],
	},
];

// Helper function to calculate relative time until expiry
// const getExpiryText = (validUntil: Date): string => {
// 	const now = new Date();
// 	const diffMs = validUntil.getTime() - now.getTime();

// 	if (diffMs <= 0) {
// 		// For expired permits, show how long ago they expired
// 		const expiredMs = Math.abs(diffMs);
// 		const expiredMinutes = Math.floor(expiredMs / (1000 * 60));
// 		const expiredHours = Math.floor(expiredMs / (1000 * 60 * 60));
// 		const expiredDays = Math.floor(expiredMs / (1000 * 60 * 60 * 24));

// 		if (expiredDays > 0) {
// 			return `Expired ${expiredDays} day${expiredDays > 1 ? 's' : ''} ago`;
// 		} else if (expiredHours > 0) {
// 			return `Expired ${expiredHours} hour${expiredHours > 1 ? 's' : ''} ago`;
// 		} else {
// 			return `Expired ${expiredMinutes} minute${expiredMinutes > 1 ? 's' : ''} ago`;
// 		}
// 	}

// 	const diffMinutes = Math.floor(diffMs / (1000 * 60));
// 	const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
// 	const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

// 	if (diffDays > 0) {
// 		return `Expires in ${diffDays} day${diffDays > 1 ? 's' : ''}`;
// 	} else if (diffHours > 0) {
// 		return `Expires in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
// 	} else {
// 		return `Expires in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
// 	}
// };

const ActivePermits: React.FC<ActivePermitsProps> = ({ siteId }) => {
	const navigate = useNavigate();
	const [permits, _setPermits] = useState<Permit[]>(mockActivePermits);
	const [_filteredPermits, setFilteredPermits] =
		useState<Permit[]>(mockActivePermits);

	const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
	const [isReturnModalOpen, setIsReturnModalOpen] = useState(false);
	const [selectedPermit, setSelectedPermit] = useState<Permit | null>(null);
	const [cancelReason, setCancelReason] = useState("");
	const [returnFormData, setReturnFormData] = useState<any>({});
	const [selectedNotifyUsers, setSelectedNotifyUsers] = useState<string[]>([]);
	const [isNotifyDropdownOpen, setIsNotifyDropdownOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

  // Mock available users for notification dropdown
  const availableUsers: NotifyUser[] = [
    { id: 'u1', name: 'Sarah Wilson', role: 'Engineer' },
    { id: 'u2', name: 'John Smith', role: 'Supervisor' },
    { id: 'u3', name: 'Michael Chen', role: 'Engineer' },
    { id: 'u4', name: 'Lisa Rodriguez', role: 'Safety Officer' },
  ];
	const [filters, _setFilters] = useState<PermitFilters>({
		search: "",
		status: "all",
		permitType: "",
		priority: "all",
		assignedWorker: "",
		dateRange: {},
		location: "",
	});

	// New search and filter state for standardized interface
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedPermitType, setSelectedPermitType] = useState("all");

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 20;

	// Permit type filters for the new interface
	const permitTypeFilters: TagOption[] = [
		{ id: "hot-work", name: "Hot Work" },
		{ id: "confined-space", name: "Confined Space" },
		{ id: "work-at-height", name: "Work at Height" },
		{ id: "excavation", name: "Excavation" },
		{ id: "electrical", name: "Electrical" },
		{ id: "general", name: "General Work" }
	];

	useEffect(() => {
		// Fetch active permits for the site
		console.log(`Fetching active permits for site ${siteId}`);
	}, [siteId]);

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setIsNotifyDropdownOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	// Function to extract task ID from task reference and navigate
	const handleTaskReferenceClick = (taskReference: string) => {
		// Extract task ID from reference (e.g., "TSK-2024-001" -> "task-1")
		// This is a simplified mapping - in real app, you'd have proper ID mapping
		const taskIdMap: { [key: string]: string } = {
			'TSK-2024-001': 'task-1',
			'TSK-2024-002': 'task-2',
			'TSK-2024-003': 'task-3',
			'TSK-2024-004': 'task-4'
		};

		const taskId = taskIdMap[taskReference] || 'task-1';
		navigate(`/sites/${siteId}/tasks/${taskId}`);
	};

	// New filtering logic using the standardized search interface
	const filteredPermitsAll = useMemo(() => {
		return permits.filter(permit => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				permit.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
				permit.permitNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
				permit.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
				permit.location.toLowerCase().includes(searchQuery.toLowerCase());

			// Permit type filter - fix: use permitType.id instead of type
			const matchesPermitType = selectedPermitType === "all" ||
				permit.permitType.id === selectedPermitType;

			return matchesSearch && matchesPermitType;
		});
	}, [permits, searchQuery, selectedPermitType]);

	// Paginated results
	const filteredPermitsNew = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredPermitsAll.slice(startIndex, endIndex);
	}, [filteredPermitsAll, currentPage, itemsPerPage]);

	// Pagination calculations
	const totalPages = Math.ceil(filteredPermitsAll.length / itemsPerPage);

	useEffect(() => {
		// Apply filters (keeping old logic for backward compatibility)
		let filtered = permits;

		if (filters.search) {
			filtered = filtered.filter(
				(permit) =>
					permit.title.toLowerCase().includes(filters.search.toLowerCase()) ||
					permit.permitNumber
						.toLowerCase()
						.includes(filters.search.toLowerCase()) ||
					permit.description
						.toLowerCase()
						.includes(filters.search.toLowerCase()),
			);
		}

		if (filters.status !== "all") {
			filtered = filtered.filter((permit) => permit.status === filters.status);
		}

		if (filters.priority !== "all") {
			filtered = filtered.filter(
				(permit) => permit.priority === filters.priority,
			);
		}

		if (filters.location) {
			filtered = filtered.filter((permit) =>
				permit.location.toLowerCase().includes(filters.location.toLowerCase()),
			);
		}

		setFilteredPermits(filtered);
	}, [permits, filters]);

	// Handlers for the new search interface
	const handleSearchChange = (query: string) => {
		setSearchQuery(query);
		setCurrentPage(1); // Reset to first page when search changes
	};

	const handlePermitTypeChange = (type: string) => {
		setSelectedPermitType(type);
		setCurrentPage(1); // Reset to first page when filter changes
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

  // Navigate to permit details based on permit type id
  const handlePermitClick = (permit: Permit) => {
    const permitTypeMap: { [key: string]: string } = {
      'hot-work': 'hot-work',
      'confined-space': 'confined-space',
      'work-at-height': 'work-at-height',
      'excavation': 'excavation',
      'general-work': 'general-work',
      'electrical': 'electrical',
    };
    const permitType = permitTypeMap[permit.permitType.id] || 'general-work';
    navigate(`/sites/${siteId}/permits/${permitType}/${permit.id}`);
  };

  // Cancel modal confirm action
  const handleConfirmCancel = () => {
    if (!cancelReason.trim() || !selectedPermit) return;
    console.log('Confirm cancel permit:', selectedPermit.id, 'reason:', cancelReason);
    setIsCancelModalOpen(false);
    setSelectedPermit(null);
    setCancelReason("");
  };

  // Return modal form change helper
  const handleReturnFormChange = (key: string, value: string) => {
    setReturnFormData((prev: any) => ({ ...prev, [key]: value }));
  };

  // Toggle users in notify list
  const handleUserToggle = (userId: string) => {
    setSelectedNotifyUsers((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    );
  };

  // Confirm return
  const handleConfirmReturn = () => {
    if (!selectedPermit) return;
    console.log('Confirm return permit:', selectedPermit.id, {
      returnFormData,
      selectedNotifyUsers,
    });
    setIsReturnModalOpen(false);
    setSelectedPermit(null);
    setReturnFormData({});
    setSelectedNotifyUsers([]);
    setIsNotifyDropdownOpen(false);
  };

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Active Permits</h2>
					<p className="text-sm text-gray-600">Currently active permits requiring monitoring and management</p>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						Total {filteredPermitsAll.length} permits
					</div>
					<div className="text-xs text-orange-600 bg-orange-50 px-3 py-1 rounded-full border border-orange-200">
						All permits expire after 24 hours
					</div>
				</div>
			</div>

			{/* Search and Filter */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={handleSearchChange}
				searchPlaceholder="Search by permit title, number, description, or location..."
				tags={[{ id: "all", name: "All" }, ...permitTypeFilters]}
				selectedTagId={selectedPermitType}
				onTagChange={handlePermitTypeChange}
			/>

			{/* Permits Table */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Permit Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Task Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Persons
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Permit Actions
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								User Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredPermitsNew.map((permit) => (
							<tr key={permit.id} className="hover:bg-gray-50 transition-colors">
								{/* Permit Details */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center space-x-2 mb-1">
											<h4 className="text-sm font-medium text-gray-900">
												{permit.permitNumber}
											</h4>
											<PermitStatusBadge status={permit.status} size="sm" />
										</div>
										<p className="text-xs text-gray-600 mb-1">
											{permit.permitType.name}
										</p>
										<div className="flex items-center text-xs text-orange-600">
											<Clock className="h-3 w-3 mr-1" />
											Expires end of day
										</div>
									</div>
								</td>

								{/* Task Details */}
								<td className="px-6 py-4">
									<div>
										<h4 className="text-sm font-medium text-gray-900 mb-1">
											{permit.title}
										</h4>
										<p className="text-xs text-gray-600 mb-1 line-clamp-2">
											{permit.description}
										</p>
										<div className="flex items-center text-xs text-gray-500 mb-2">
											<MapPin className="h-3 w-3 mr-1" />
											{permit.location}
										</div>
										{/* Task Reference Link */}
										{permit.taskReference && (
											<div className="mt-2">
												<button
													onClick={() => handleTaskReferenceClick(permit.taskReference!)}
													className="flex items-center text-xs text-blue-600 font-mono hover:text-blue-800 hover:underline transition-colors"
													title="Click to view related task"
												>
													Task: {permit.taskReference}
													<ExternalLink className="h-3 w-3 ml-1" />
												</button>
											</div>
										)}
									</div>
								</td>

								{/* Persons */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center text-sm text-gray-900 mb-1">
											<Users className="h-4 w-4 mr-1" />
											{permit.assignedWorkers?.length || 0} persons
										</div>
										{permit.supervisorName && (
											<div className="text-xs text-gray-600">
												Supervisor: {permit.supervisorName}
											</div>
										)}
									</div>
								</td>

								{/* Permit Actions */}
								<td className="px-6 py-4">
									<div className="flex space-x-2">
										<button
											onClick={(e) => {
												e.stopPropagation();
												setSelectedPermit(permit);
												setIsReturnModalOpen(true);
											}}
											className="px-3 py-1 text-xs bg-green-50 border border-green-300 text-green-700 rounded hover:bg-green-100 transition-colors font-medium"
											style={{ borderRadius: '5px' }}
										>
											Return
										</button>
										<button
											onClick={(e) => {
												e.stopPropagation();
												setSelectedPermit(permit);
												setIsCancelModalOpen(true);
											}}
											className="px-3 py-1 text-xs bg-red-50 border border-red-300 text-red-700 rounded hover:bg-red-100 transition-colors font-medium"
											style={{ borderRadius: '5px' }}
										>
											Cancel
										</button>
									</div>
								</td>

								{/* User Actions */}
								<td className="px-6 py-4">
									<div className="flex space-x-2">
										<button
											onClick={() => handlePermitClick(permit)}
											className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
											style={{ borderRadius: '5px' }}
										>
											View
										</button>
										<button
											onClick={(e) => {
												e.stopPropagation();
												console.log("Downloading permit:", permit.id);
											}}
											className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors flex items-center"
											style={{ borderRadius: '5px' }}
										>
											<Download className="h-3 w-3" />
										</button>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredPermitsAll.length === 0 && (
					<div className="p-12 text-center">
						<ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No permits found
						</h3>
						<p className="text-gray-500">
							{searchQuery || selectedPermitType !== "all"
								? "Try adjusting your filters to see more results."
								: "Create your first permit to get started."}
						</p>
					</div>
				)}

				{/* Pagination */}
				{filteredPermitsAll.length > 0 && (
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						totalItems={filteredPermitsAll.length}
						itemsPerPage={itemsPerPage}
						onPageChange={handlePageChange}
					/>
				)}
			</div>



			{/* Cancel Permit Modal */}
			{isCancelModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
						<div className="flex justify-between items-center p-6 border-b border-gray-200">
							<h2 className="text-xl font-semibold text-gray-900">Cancel Permit</h2>
							<button
								onClick={() => {
									setIsCancelModalOpen(false);
									setSelectedPermit(null);
									setCancelReason("");
								}}
								className="text-gray-400 hover:text-gray-600 transition-colors"
							>
								<X className="h-6 w-6" />
							</button>
						</div>

						<div className="p-6">
							<div className="mb-4">
								<p className="text-sm text-gray-600 mb-2">
									You are about to cancel permit: <strong>{selectedPermit?.title}</strong>
								</p>
								<p className="text-sm text-gray-600 mb-4">
									Permit Number: <strong>{selectedPermit?.permitNumber}</strong>
								</p>
							</div>

							<div className="mb-6">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Reason for cancellation *
								</label>
								<textarea
									value={cancelReason}
									onChange={(e) => setCancelReason(e.target.value)}
									placeholder="Please provide a reason for cancelling this permit..."
									className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
									rows={4}
									required
								/>
							</div>

							<div className="flex space-x-3">
								<button
									onClick={() => {
										setIsCancelModalOpen(false);
										setSelectedPermit(null);
										setCancelReason("");
									}}
									className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
								>
									Back
								</button>
								<button
									onClick={handleConfirmCancel}
									disabled={!cancelReason.trim()}
									className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
								>
									Cancel Permit
								</button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Return Permit Modal */}
			{isReturnModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
						<div className="flex justify-between items-center p-6 border-b border-gray-200">
							<h2 className="text-xl font-semibold text-gray-900">Return Permit</h2>
							<button
								onClick={() => {
									setIsReturnModalOpen(false);
									setSelectedPermit(null);
									setReturnFormData({});
									setSelectedNotifyUsers([]);
									setIsNotifyDropdownOpen(false);
								}}
								className="text-gray-400 hover:text-gray-600 transition-colors"
							>
								<X className="h-6 w-6" />
							</button>
						</div>

						<div className="p-6 space-y-6">
							{/* Permit Return Section */}
							<div className="bg-white border border-gray-200 rounded-lg p-4">
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Permit Return</h3>
								<p className="text-sm text-gray-600 mb-4">
									I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept.
								</p>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{/* Competent Person (Permit Receiver) */}
									<div className="border border-gray-200 rounded p-3">
										<h4 className="text-sm font-medium text-gray-800 mb-2">Competent Person (Permit Receiver)</h4>
										<div className="space-y-2">
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Name *</label>
												<input
													type="text"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Name'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Name', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Date *</label>
												<input
													type="date"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Date'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Date', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Time *</label>
												<input
													type="time"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Time'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Time', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Signature *</label>
												<div className="border border-gray-300 rounded-md p-2 text-center text-xs bg-gray-50 text-gray-500">
													Signature Pad
												</div>
											</div>
										</div>
									</div>

									{/* Authorizing Person (Permit Issuer) */}
									<div className="border border-gray-200 rounded p-3">
										<h4 className="text-sm font-medium text-gray-800 mb-2">Authorizing Person (Permit Issuer)</h4>
										<div className="space-y-2">
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Name *</label>
												<input
													type="text"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Name'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Name', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Date *</label>
												<input
													type="date"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Date'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Date', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Time *</label>
												<input
													type="time"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Time'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Time', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Signature *</label>
												<div className="border border-gray-300 rounded-md p-2 text-center text-xs bg-gray-50 text-gray-500">
													Signature Pad
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							{/* People to Notify Section */}
							<div className="bg-white border border-gray-200 rounded-lg p-4">
								<h3 className="text-lg font-semibold text-gray-900 mb-3">People to Notify</h3>
								<div className="relative" ref={dropdownRef}>
									<label className="block text-sm font-medium text-gray-700 mb-2">Select users to notify</label>
									<button
										type="button"
										onClick={() => setIsNotifyDropdownOpen(!isNotifyDropdownOpen)}
										className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white text-left flex items-center justify-between"
									>
										<span className="text-sm text-gray-700">
											{selectedNotifyUsers.length === 0
												? "Select users to notify..."
												: `${selectedNotifyUsers.length} user(s) selected`
											}
										</span>
										<ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isNotifyDropdownOpen ? 'rotate-180' : ''}`} />
									</button>

									{isNotifyDropdownOpen && (
										<div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
											{availableUsers.map((user) => (
												<label
													key={user.id}
													className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
												>
													<input
														type="checkbox"
														checked={selectedNotifyUsers.includes(user.id)}
														onChange={() => handleUserToggle(user.id)}
														className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded mr-3"
													/>
													<div className="flex-1">
														<div className="text-sm font-medium text-gray-900">{user.name}</div>
														<div className="text-xs text-gray-500">{user.role}</div>
													</div>
												</label>
											))}
										</div>
									)}
								</div>
							</div>

							{/* Action Buttons */}
							<div className="flex space-x-3">
								<button
									onClick={() => {
										setIsReturnModalOpen(false);
										setSelectedPermit(null);
										setReturnFormData({});
										setSelectedNotifyUsers([]);
										setIsNotifyDropdownOpen(false);
									}}
									className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md text-sm font-medium hover:bg-gray-700 transition-colors"
								>
									Cancel
								</button>
								<button
									onClick={handleConfirmReturn}
									className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
								>
									Return Permit
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default ActivePermits;
