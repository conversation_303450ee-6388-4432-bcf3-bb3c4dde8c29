import React, { useState, useCallback } from 'react';
import {
  User,
  GraduationCap,
  Shield,
  Camera,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Save
} from 'lucide-react';
import { toast } from 'react-toastify';

import { EnhancedWorkerCreationInput } from '../../types/credentials';
import { BasicInfoStep } from './steps/BasicInfoStep';
import { PermanentCredentialsStep } from './steps/PermanentCredentialsStep';
import { TemporaryCredentialsStep } from './steps/TemporaryCredentialsStep';
import { DocumentsAndPhotoStep } from './steps/DocumentsAndPhotoStep';
import { ReviewAndSubmitStep } from './steps/ReviewAndSubmitStep';

// Form step configuration
interface FormStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
  isOptional?: boolean;
}

const FORM_STEPS: FormStep[] = [
  {
    id: 'basic-info',
    title: 'Basic Information',
    description: 'Personal and employment details',
    icon: <User className="h-5 w-5" />,
    component: BasicInfoStep
  },
  {
    id: 'permanent-credentials',
    title: 'Permanent Credentials',
    description: 'Diplomas, degrees, and licenses',
    icon: <GraduationCap className="h-5 w-5" />,
    component: PermanentCredentialsStep,
    isOptional: true
  },
  {
    id: 'temporary-credentials',
    title: 'Temporary Credentials',
    description: 'Safety training and certifications',
    icon: <Shield className="h-5 w-5" />,
    component: TemporaryCredentialsStep,
    isOptional: true
  },
  {
    id: 'documents-photo',
    title: 'Documents & Photo',
    description: 'Profile picture and additional documents',
    icon: <Camera className="h-5 w-5" />,
    component: DocumentsAndPhotoStep
  },
  {
    id: 'review-submit',
    title: 'Review & Submit',
    description: 'Verify information and create worker',
    icon: <CheckCircle className="h-5 w-5" />,
    component: ReviewAndSubmitStep
  }
];

interface EnhancedWorkerCreationFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  initialData?: Partial<EnhancedWorkerCreationInput>;
}

export const EnhancedWorkerCreationForm: React.FC<EnhancedWorkerCreationFormProps> = ({
  onSuccess,
  onCancel,
  initialData
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [formData, setFormData] = useState<EnhancedWorkerCreationInput>({
    name: '',
    company: '',
    nationalId: '',
    gender: 'MALE',
    phoneNumber: '',
    email: '',
    dateOfBirth: '',
    employeeNumber: '',
    hireDate: new Date().toISOString().split('T')[0],
    tradeIds: [],
    skillIds: [],
    permanentCredentials: [],
    temporaryCredentials: [],
    assignToSite: false,
    ...initialData
  });

  const [stepValidation, setStepValidation] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDraft, setIsDraft] = useState(false);

  const currentStep = FORM_STEPS[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === FORM_STEPS.length - 1;

  // Calculate progress percentage
  const progressPercentage = ((currentStepIndex + 1) / FORM_STEPS.length) * 100;

  // Update form data
  const updateFormData = useCallback((updates: Partial<EnhancedWorkerCreationInput>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  // Validate current step
  const validateCurrentStep = useCallback((): boolean => {
    const stepId = currentStep.id;
    let isValid = true;

    switch (stepId) {
      case 'basic-info':
        isValid = !!(
          formData.name?.trim() &&
          formData.company?.trim() &&
          formData.nationalId?.trim() &&
          formData.phoneNumber?.trim() &&
          formData.gender
        );
        break;

      case 'permanent-credentials':
        // Optional step - always valid
        isValid = true;
        break;

      case 'temporary-credentials':
        // Optional step - always valid
        isValid = true;
        break;

      case 'documents-photo':
        isValid = !!(formData.profilePicture);
        break;

      case 'review-submit':
        // Final validation happens in the review step
        isValid = true;
        break;

      default:
        isValid = true;
    }

    setStepValidation(prev => ({ ...prev, [stepId]: isValid }));
    return isValid;
  }, [currentStep.id, formData]);

  // Navigate to next step
  const goToNextStep = useCallback(() => {
    if (validateCurrentStep() && !isLastStep) {
      setCurrentStepIndex(prev => prev + 1);
    }
  }, [validateCurrentStep, isLastStep]);

  // Navigate to previous step
  const goToPreviousStep = useCallback(() => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1);
    }
  }, [isFirstStep]);

  // Jump to specific step
  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < FORM_STEPS.length) {
      setCurrentStepIndex(stepIndex);
    }
  }, []);

  // Save as draft
  const saveAsDraft = useCallback(async () => {
    setIsDraft(true);
    try {
      // TODO: Implement draft saving logic
      toast.success('Draft saved successfully');
    } catch (error) {
      toast.error('Failed to save draft');
    } finally {
      setIsDraft(false);
    }
  }, [formData]);

  // Submit form
  const handleSubmit = useCallback(async () => {
    if (!validateCurrentStep()) {
      toast.error('Please complete all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Implement actual submission logic
      console.log('Submitting worker data:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success('Worker created successfully!');
      onSuccess?.(formData);
    } catch (error) {
      console.error('Error creating worker:', error);
      toast.error('Failed to create worker. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateCurrentStep, onSuccess]);

  // Render step indicator
  const renderStepIndicator = () => (
    <div className="mb-8">
      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
        <div
          className="bg-green-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {/* Step indicators */}
      <div className="flex justify-between">
        {FORM_STEPS.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isAccessible = index <= currentStepIndex || stepValidation[FORM_STEPS[index].id];

          return (
            <button
              key={step.id}
              onClick={() => isAccessible && goToStep(index)}
              disabled={!isAccessible}
              className={`
                flex flex-col items-center space-y-2 p-2 rounded-lg transition-all duration-200
                ${isCurrent ? 'bg-green-50 text-green-700' : ''}
                ${isCompleted ? 'text-green-600' : 'text-gray-500'}
                ${isAccessible ? 'hover:bg-gray-50 cursor-pointer' : 'cursor-not-allowed opacity-50'}
              `}
            >
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                ${isCompleted ? 'bg-green-600 border-green-600 text-white' : ''}
                ${isCurrent ? 'border-green-600 text-green-600' : 'border-gray-300'}
              `}>
                {isCompleted ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  step.icon
                )}
              </div>
              <div className="text-center">
                <div className="text-sm font-medium">{step.title}</div>
                <div className="text-xs text-gray-500">{step.description}</div>
                {step.isOptional && (
                  <div className="text-xs text-gray-400">(Optional)</div>
                )}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );

  // Render current step content
  const renderStepContent = () => {
    const StepComponent = currentStep.component;

    return (
      <StepComponent
        formData={formData}
        updateFormData={updateFormData}
        onNext={goToNextStep}
        onPrevious={goToPreviousStep}
        isValid={stepValidation[currentStep.id]}
        onValidationChange={(isValid: boolean) =>
          setStepValidation(prev => ({ ...prev, [currentStep.id]: isValid }))
        }
      />
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Create New Worker
        </h1>
        <p className="text-gray-600">
          Complete the form below to add a new worker to your workforce management system.
        </p>
      </div>

      {/* Step indicator */}
      {renderStepIndicator()}

      {/* Form content */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-6">
          {renderStepContent()}
        </div>

        {/* Form actions */}
        <div className="border-t border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {!isFirstStep && (
                <button
                  type="button"
                  onClick={goToPreviousStep}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </button>
              )}

              <button
                type="button"
                onClick={saveAsDraft}
                disabled={isDraft}
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
              >
                <Save className="h-4 w-4 mr-1" />
                {isDraft ? 'Saving...' : 'Save Draft'}
              </button>
            </div>

            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                Cancel
              </button>

              {isLastStep ? (
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !stepValidation[currentStep.id]}
                  className="flex items-center px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Creating Worker...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Create Worker
                    </>
                  )}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={goToNextStep}
                  disabled={!stepValidation[currentStep.id]}
                  className="flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
