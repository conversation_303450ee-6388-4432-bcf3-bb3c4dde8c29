import React, { useState } from 'react';
import { GraduationCap, Plus, Trash2, Upload, FileText, AlertCircle } from 'lucide-react';
import { EnhancedWorkerCreationInput, PermanentCredential } from '../../../types/credentials';
import { generateCredentialName, validateCredential, isValidCredentialFileType, isValidCredentialFileSize } from '../../../utils/credentialUtils';
import { toast } from 'react-toastify';

interface PermanentCredentialsStepProps {
  formData: EnhancedWorkerCreationInput;
  updateFormData: (updates: Partial<EnhancedWorkerCreationInput>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  onValidationChange: (isValid: boolean) => void;
}

const PERMANENT_CREDENTIAL_CATEGORIES = [
  { value: 'DIPLOMA', label: 'Diploma' },
  { value: 'DEGREE', label: 'Degree' },
  { value: 'PROFESSIONAL_LICENSE', label: 'Professional License' },
  { value: 'TRADE_CERTIFICATE', label: 'Trade Certificate' },
  { value: 'OTHER', label: 'Other' }
] as const;

export const PermanentCredentialsStep: React.FC<PermanentCredentialsStepProps> = ({
  formData,
  updateFormData
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCredential, setNewCredential] = useState<Partial<PermanentCredential>>({
    type: 'PERMANENT',
    category: 'DIPLOMA',
    name: '',
    institution: '',
    issuingAuthority: '',
    issueDate: '',
    status: 'VALID'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Add new credential
  const handleAddCredential = () => {
    const validation = validateCredential(newCredential);

    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        if (error.includes('name')) errorMap.name = error;
        if (error.includes('authority')) errorMap.issuingAuthority = error;
        if (error.includes('institution')) errorMap.institution = error;
        if (error.includes('date')) errorMap.issueDate = error;
      });
      setErrors(errorMap);
      return;
    }

    // Generate auto-naming for the credential
    if (newCredential.documentFile && formData.name) {
      const namingContext = {
        workerName: formData.name,
        credentialType: 'PERMANENT' as const,
        category: newCredential.category || 'OTHER',
        issuingAuthority: newCredential.issuingAuthority || '',
        issueDate: newCredential.issueDate || ''
      };

      const autoName = generateCredentialName(namingContext);
      newCredential.name = newCredential.name || autoName.displayName;
    }

    const credential: PermanentCredential = {
      id: `perm_${Date.now()}`,
      type: 'PERMANENT',
      category: newCredential.category as any || 'OTHER',
      name: newCredential.name || '',
      institution: newCredential.institution || '',
      issuingAuthority: newCredential.issuingAuthority || '',
      issueDate: newCredential.issueDate || '',
      status: 'VALID',
      certificateNumber: newCredential.certificateNumber,
      fieldOfStudy: newCredential.fieldOfStudy,
      graduationYear: newCredential.graduationYear,
      licenseNumber: newCredential.licenseNumber,
      documentFile: newCredential.documentFile,
      notes: newCredential.notes
    };

    const updatedCredentials = [...(formData.permanentCredentials || []), credential];
    updateFormData({ permanentCredentials: updatedCredentials });

    // Reset form
    setNewCredential({
      type: 'PERMANENT',
      category: 'DIPLOMA',
      name: '',
      institution: '',
      issuingAuthority: '',
      issueDate: '',
      status: 'VALID'
    });
    setErrors({});
    setShowAddForm(false);

    toast.success('Permanent credential added successfully');
  };

  // Remove credential
  const handleRemoveCredential = (credentialId: string) => {
    const updatedCredentials = formData.permanentCredentials?.filter(c => c.id !== credentialId) || [];
    updateFormData({ permanentCredentials: updatedCredentials });
    toast.success('Credential removed');
  };

  // Handle file upload
  const handleFileUpload = (file: File) => {
    if (!isValidCredentialFileType(file)) {
      toast.error('Please upload a PDF, JPEG, PNG, or WebP file');
      return;
    }

    if (!isValidCredentialFileSize(file)) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setNewCredential(prev => ({ ...prev, documentFile: file }));
  };

  // Update form field
  const updateField = (field: keyof PermanentCredential, value: any) => {
    setNewCredential(prev => ({ ...prev, [field]: value }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="space-y-8">
      {/* Step header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Permanent Credentials</h2>
        <p className="text-gray-600">
          Add diplomas, degrees, professional licenses, and other permanent qualifications that don't expire.
          This step is optional - you can skip it if the worker doesn't have any permanent credentials yet.
        </p>
      </div>

      {/* Existing credentials */}
      {formData.permanentCredentials && formData.permanentCredentials.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Added Credentials</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {formData.permanentCredentials.map((credential) => (
              <div key={credential.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <GraduationCap className="h-5 w-5 text-green-600" />
                      <h4 className="text-lg font-medium text-gray-900">{credential.name}</h4>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {credential.category.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Institution:</span> {credential.institution}
                      </div>
                      <div>
                        <span className="font-medium">Issuing Authority:</span> {credential.issuingAuthority}
                      </div>
                      <div>
                        <span className="font-medium">Issue Date:</span> {new Date(credential.issueDate).toLocaleDateString()}
                      </div>
                      {credential.fieldOfStudy && (
                        <div>
                          <span className="font-medium">Field of Study:</span> {credential.fieldOfStudy}
                        </div>
                      )}
                      {credential.certificateNumber && (
                        <div>
                          <span className="font-medium">Certificate Number:</span> {credential.certificateNumber}
                        </div>
                      )}
                      {credential.licenseNumber && (
                        <div>
                          <span className="font-medium">License Number:</span> {credential.licenseNumber}
                        </div>
                      )}
                    </div>
                    {credential.documentFile && (
                      <div className="mt-3 flex items-center text-sm text-gray-500">
                        <FileText className="h-4 w-4 mr-1" />
                        Document: {credential.documentFile.name}
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => handleRemoveCredential(credential.id!)}
                    className="ml-4 p-2 text-red-600 hover:bg-red-50 rounded-md"
                    title="Remove credential"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add new credential */}
      {!showAddForm ? (
        <button
          onClick={() => setShowAddForm(true)}
          className="w-full flex items-center justify-center px-6 py-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Permanent Credential
        </button>
      ) : (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Add New Permanent Credential</h3>
            <button
              onClick={() => {
                setShowAddForm(false);
                setErrors({});
                setNewCredential({
                  type: 'PERMANENT',
                  category: 'DIPLOMA',
                  name: '',
                  institution: '',
                  issuingAuthority: '',
                  issueDate: '',
                  status: 'VALID'
                });
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                value={newCredential.category}
                onChange={(e) => updateField('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                {PERMANENT_CREDENTIAL_CATEGORIES.map(cat => (
                  <option key={cat.value} value={cat.value}>{cat.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Credential Name *
              </label>
              <input
                type="text"
                value={newCredential.name}
                onChange={(e) => updateField('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., Bachelor of Engineering"
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Institution *
              </label>
              <input
                type="text"
                value={newCredential.institution}
                onChange={(e) => updateField('institution', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.institution ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., University of Nairobi"
              />
              {errors.institution && <p className="text-red-500 text-sm mt-1">{errors.institution}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issuing Authority *
              </label>
              <input
                type="text"
                value={newCredential.issuingAuthority}
                onChange={(e) => updateField('issuingAuthority', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.issuingAuthority ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., Ministry of Education"
              />
              {errors.issuingAuthority && <p className="text-red-500 text-sm mt-1">{errors.issuingAuthority}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issue Date *
              </label>
              <input
                type="date"
                value={newCredential.issueDate}
                onChange={(e) => updateField('issueDate', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.issueDate ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.issueDate && <p className="text-red-500 text-sm mt-1">{errors.issueDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Field of Study
              </label>
              <input
                type="text"
                value={newCredential.fieldOfStudy || ''}
                onChange={(e) => updateField('fieldOfStudy', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="e.g., Civil Engineering"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Certificate Number
              </label>
              <input
                type="text"
                value={newCredential.certificateNumber || ''}
                onChange={(e) => updateField('certificateNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Certificate or license number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                License Number
              </label>
              <input
                type="text"
                value={newCredential.licenseNumber || ''}
                onChange={(e) => updateField('licenseNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Professional license number"
              />
            </div>
          </div>

          {/* File upload */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Document
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <input
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.webp"
                onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                className="hidden"
                id="credential-upload"
              />
              <label
                htmlFor="credential-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">
                  Click to upload or drag and drop
                </span>
                <span className="text-xs text-gray-500 mt-1">
                  PDF, JPEG, PNG, WebP up to 10MB
                </span>
              </label>
              {newCredential.documentFile && (
                <div className="mt-3 flex items-center text-sm text-green-600">
                  <FileText className="h-4 w-4 mr-1" />
                  {newCredential.documentFile.name}
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={newCredential.notes || ''}
              onChange={(e) => updateField('notes', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Additional notes about this credential..."
            />
          </div>

          {/* Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAddCredential}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
            >
              Add Credential
            </button>
          </div>
        </div>
      )}

      {/* Info box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">About Permanent Credentials</p>
            <p>
              Permanent credentials are long-term qualifications that typically don't expire, such as diplomas,
              degrees, and professional licenses. These credentials establish the worker's foundational qualifications
              and expertise in their field.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
