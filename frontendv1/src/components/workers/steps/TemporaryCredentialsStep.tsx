import React, { useState } from 'react';
import { Shield, Plus, Trash2, Upload, FileText, AlertCircle, Clock, AlertTriangle } from 'lucide-react';
import { EnhancedWorkerCreationInput, TemporaryCredential } from '../../../types/credentials';
import { generateCredentialName, validateCredential, isValidCredentialFileType, isValidCredentialFileSize, calculateDaysUntilExpiry } from '../../../utils/credentialUtils';
import { toast } from 'react-toastify';

interface TemporaryCredentialsStepProps {
  formData: EnhancedWorkerCreationInput;
  updateFormData: (updates: Partial<EnhancedWorkerCreationInput>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  onValidationChange: (isValid: boolean) => void;
}

const TEMPORARY_CREDENTIAL_CATEGORIES = [
  { value: 'SAFETY_TRAINING', label: 'Safety Training' },
  { value: 'EQUIPMENT_CERTIFICATION', label: 'Equipment Certification' },
  { value: 'FIRST_AID', label: 'First Aid' },
  { value: 'COMPLIANCE_TRAINING', label: 'Compliance Training' },
  { value: 'OTHER', label: 'Other' }
] as const;

const COMPETENCY_LEVELS = [
  { value: 'BASIC', label: 'Basic' },
  { value: 'INTERMEDIATE', label: 'Intermediate' },
  { value: 'ADVANCED', label: 'Advanced' },
  { value: 'EXPERT', label: 'Expert' }
] as const;

export const TemporaryCredentialsStep: React.FC<TemporaryCredentialsStepProps> = ({
  formData,
  updateFormData
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCredential, setNewCredential] = useState<Partial<TemporaryCredential>>({
    type: 'TEMPORARY',
    category: 'SAFETY_TRAINING',
    name: '',
    trainingProvider: '',
    issuingAuthority: '',
    issueDate: '',
    expiryDate: '',
    renewalRequired: true,
    renewalPeriodMonths: 12,
    status: 'VALID'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Add new credential
  const handleAddCredential = () => {
    const validation = validateCredential(newCredential);

    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        if (error.includes('name')) errorMap.name = error;
        if (error.includes('authority')) errorMap.issuingAuthority = error;
        if (error.includes('provider')) errorMap.trainingProvider = error;
        if (error.includes('issue date')) errorMap.issueDate = error;
        if (error.includes('expiry') || error.includes('Expiry')) errorMap.expiryDate = error;
        if (error.includes('renewal')) errorMap.renewalPeriodMonths = error;
      });
      setErrors(errorMap);
      return;
    }

    // Generate auto-naming for the credential
    if (newCredential.documentFile && formData.name) {
      const namingContext = {
        workerName: formData.name,
        credentialType: 'TEMPORARY' as const,
        category: newCredential.category || 'OTHER',
        trainingName: newCredential.name,
        issuingAuthority: newCredential.issuingAuthority || '',
        issueDate: newCredential.issueDate || ''
      };

      const autoName = generateCredentialName(namingContext);
      newCredential.name = newCredential.name || autoName.displayName;
    }

    const credential: TemporaryCredential = {
      id: `temp_${Date.now()}`,
      type: 'TEMPORARY',
      category: newCredential.category as any || 'OTHER',
      name: newCredential.name || '',
      trainingProvider: newCredential.trainingProvider || '',
      issuingAuthority: newCredential.issuingAuthority || '',
      issueDate: newCredential.issueDate || '',
      expiryDate: newCredential.expiryDate || '',
      renewalRequired: newCredential.renewalRequired ?? true,
      renewalPeriodMonths: newCredential.renewalPeriodMonths || 12,
      status: 'VALID',
      certificateNumber: newCredential.certificateNumber,
      trainingDurationHours: newCredential.trainingDurationHours,
      competencyLevel: newCredential.competencyLevel,
      documentFile: newCredential.documentFile,
      notes: newCredential.notes
    };

    const updatedCredentials = [...(formData.temporaryCredentials || []), credential];
    updateFormData({ temporaryCredentials: updatedCredentials });

    // Reset form
    setNewCredential({
      type: 'TEMPORARY',
      category: 'SAFETY_TRAINING',
      name: '',
      trainingProvider: '',
      issuingAuthority: '',
      issueDate: '',
      expiryDate: '',
      renewalRequired: true,
      renewalPeriodMonths: 12,
      status: 'VALID'
    });
    setErrors({});
    setShowAddForm(false);

    toast.success('Temporary credential added successfully');
  };

  // Remove credential
  const handleRemoveCredential = (credentialId: string) => {
    const updatedCredentials = formData.temporaryCredentials?.filter(c => c.id !== credentialId) || [];
    updateFormData({ temporaryCredentials: updatedCredentials });
    toast.success('Credential removed');
  };

  // Handle file upload
  const handleFileUpload = (file: File) => {
    if (!isValidCredentialFileType(file)) {
      toast.error('Please upload a PDF, JPEG, PNG, or WebP file');
      return;
    }

    if (!isValidCredentialFileSize(file)) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setNewCredential(prev => ({ ...prev, documentFile: file }));
  };

  // Update form field
  const updateField = (field: keyof TemporaryCredential, value: any) => {
    setNewCredential(prev => ({ ...prev, [field]: value }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Get status badge for credential
  const getStatusBadge = (credential: TemporaryCredential) => {
    const daysUntilExpiry = calculateDaysUntilExpiry(credential.expiryDate);

    if (daysUntilExpiry < 0) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Expired</span>;
    } else if (daysUntilExpiry <= 30) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Expiring Soon</span>;
    } else {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Valid</span>;
    }
  };

  return (
    <div className="space-y-8">
      {/* Step header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Temporary Credentials</h2>
        <p className="text-gray-600">
          Add safety training, equipment certifications, first aid, and other time-limited credentials that require renewal.
          This step is optional - you can skip it if the worker doesn't have any temporary credentials yet.
        </p>
      </div>

      {/* Existing credentials */}
      {formData.temporaryCredentials && formData.temporaryCredentials.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Added Credentials</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {formData.temporaryCredentials.map((credential) => {
              const daysUntilExpiry = calculateDaysUntilExpiry(credential.expiryDate);

              return (
                <div key={credential.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Shield className="h-5 w-5 text-green-600" />
                        <h4 className="text-lg font-medium text-gray-900">{credential.name}</h4>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          {credential.category.replace('_', ' ')}
                        </span>
                        {getStatusBadge(credential)}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Training Provider:</span> {credential.trainingProvider}
                        </div>
                        <div>
                          <span className="font-medium">Issuing Authority:</span> {credential.issuingAuthority}
                        </div>
                        <div>
                          <span className="font-medium">Issue Date:</span> {new Date(credential.issueDate).toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium">Expiry Date:</span>
                          <span className={`ml-1 ${daysUntilExpiry <= 30 ? 'text-red-600 font-medium' : ''}`}>
                            {new Date(credential.expiryDate).toLocaleDateString()}
                          </span>
                          {daysUntilExpiry <= 30 && daysUntilExpiry >= 0 && (
                            <AlertTriangle className="h-4 w-4 ml-1 text-yellow-500" />
                          )}
                        </div>
                        {credential.renewalPeriodMonths && (
                          <div>
                            <span className="font-medium">Renewal Period:</span> {credential.renewalPeriodMonths} months
                          </div>
                        )}
                        {credential.competencyLevel && (
                          <div>
                            <span className="font-medium">Competency Level:</span> {credential.competencyLevel}
                          </div>
                        )}
                        {credential.trainingDurationHours && (
                          <div>
                            <span className="font-medium">Training Duration:</span> {credential.trainingDurationHours} hours
                          </div>
                        )}
                        {credential.certificateNumber && (
                          <div>
                            <span className="font-medium">Certificate Number:</span> {credential.certificateNumber}
                          </div>
                        )}
                      </div>

                      {daysUntilExpiry <= 30 && daysUntilExpiry >= 0 && (
                        <div className="mt-3 flex items-center text-sm text-yellow-700 bg-yellow-50 px-3 py-2 rounded-md">
                          <Clock className="h-4 w-4 mr-2" />
                          Expires in {daysUntilExpiry} days - renewal required
                        </div>
                      )}

                      {credential.documentFile && (
                        <div className="mt-3 flex items-center text-sm text-gray-500">
                          <FileText className="h-4 w-4 mr-1" />
                          Document: {credential.documentFile.name}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveCredential(credential.id!)}
                      className="ml-4 p-2 text-red-600 hover:bg-red-50 rounded-md"
                      title="Remove credential"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Add new credential */}
      {!showAddForm ? (
        <button
          onClick={() => setShowAddForm(true)}
          className="w-full flex items-center justify-center px-6 py-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Temporary Credential
        </button>
      ) : (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Add New Temporary Credential</h3>
            <button
              onClick={() => {
                setShowAddForm(false);
                setErrors({});
                setNewCredential({
                  type: 'TEMPORARY',
                  category: 'SAFETY_TRAINING',
                  name: '',
                  trainingProvider: '',
                  issuingAuthority: '',
                  issueDate: '',
                  expiryDate: '',
                  renewalRequired: true,
                  renewalPeriodMonths: 12,
                  status: 'VALID'
                });
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                value={newCredential.category}
                onChange={(e) => updateField('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                {TEMPORARY_CREDENTIAL_CATEGORIES.map(cat => (
                  <option key={cat.value} value={cat.value}>{cat.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Name *
              </label>
              <input
                type="text"
                value={newCredential.name}
                onChange={(e) => updateField('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., Site Safety Induction"
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Provider *
              </label>
              <input
                type="text"
                value={newCredential.trainingProvider}
                onChange={(e) => updateField('trainingProvider', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.trainingProvider ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., Safety Training Institute"
              />
              {errors.trainingProvider && <p className="text-red-500 text-sm mt-1">{errors.trainingProvider}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issuing Authority *
              </label>
              <input
                type="text"
                value={newCredential.issuingAuthority}
                onChange={(e) => updateField('issuingAuthority', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.issuingAuthority ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="e.g., OSHA Kenya"
              />
              {errors.issuingAuthority && <p className="text-red-500 text-sm mt-1">{errors.issuingAuthority}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issue Date *
              </label>
              <input
                type="date"
                value={newCredential.issueDate}
                onChange={(e) => updateField('issueDate', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.issueDate ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.issueDate && <p className="text-red-500 text-sm mt-1">{errors.issueDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiry Date *
              </label>
              <input
                type="date"
                value={newCredential.expiryDate}
                onChange={(e) => updateField('expiryDate', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.expiryDate ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.expiryDate && <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Renewal Period (Months) *
              </label>
              <input
                type="number"
                min="1"
                max="120"
                value={newCredential.renewalPeriodMonths}
                onChange={(e) => updateField('renewalPeriodMonths', parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.renewalPeriodMonths ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="12"
              />
              {errors.renewalPeriodMonths && <p className="text-red-500 text-sm mt-1">{errors.renewalPeriodMonths}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Competency Level
              </label>
              <select
                value={newCredential.competencyLevel || ''}
                onChange={(e) => updateField('competencyLevel', e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Select level</option>
                {COMPETENCY_LEVELS.map(level => (
                  <option key={level.value} value={level.value}>{level.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Certificate Number
              </label>
              <input
                type="text"
                value={newCredential.certificateNumber || ''}
                onChange={(e) => updateField('certificateNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Certificate number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Duration (Hours)
              </label>
              <input
                type="number"
                min="0"
                step="0.5"
                value={newCredential.trainingDurationHours || ''}
                onChange={(e) => updateField('trainingDurationHours', parseFloat(e.target.value) || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="8"
              />
            </div>
          </div>

          {/* File upload */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Certificate
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <input
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.webp"
                onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                className="hidden"
                id="temp-credential-upload"
              />
              <label
                htmlFor="temp-credential-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">
                  Click to upload or drag and drop
                </span>
                <span className="text-xs text-gray-500 mt-1">
                  PDF, JPEG, PNG, WebP up to 10MB
                </span>
              </label>
              {newCredential.documentFile && (
                <div className="mt-3 flex items-center text-sm text-green-600">
                  <FileText className="h-4 w-4 mr-1" />
                  {newCredential.documentFile.name}
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={newCredential.notes || ''}
              onChange={(e) => updateField('notes', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Additional notes about this credential..."
            />
          </div>

          {/* Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAddCredential}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
            >
              Add Credential
            </button>
          </div>
        </div>
      )}

      {/* Info box */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 mr-3" />
          <div className="text-sm text-orange-700">
            <p className="font-medium mb-1">About Temporary Credentials</p>
            <p>
              Temporary credentials are time-limited qualifications that require periodic renewal, such as safety training,
              equipment certifications, and first aid certificates. The system will automatically track expiration dates
              and send renewal reminders.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
