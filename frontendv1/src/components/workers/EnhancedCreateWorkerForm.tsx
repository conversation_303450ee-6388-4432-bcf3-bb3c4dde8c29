import React, { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  Save,
  X,
  IdCard,
  AlertTriangle
} from 'lucide-react';

import FloatingCard from '../layout/FloatingCard';
import TopBar from '../layout/TopBar';
import {CREATE_WORKER_WITH_TRAINING } from '../../graphql/mutations';
// import { GET_ALL_TRADES, GET_ALL_SKILLS, GET_ALL_TRAININGS } from '../../graphql/queries';
// import {
//   TrainingComplianceStatus,
//   getWorkerTrainingRequirements
// } from '../../data/workers';
import { mockTrades, mockSkills } from '../../data/mockData';
import { useSiteContext } from '../../hooks/useSiteContext';

interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  phoneNumber: string;
  email: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | 'Other';
  employeeNumber: string;
  hireDate: string;
  tradeIds: number[];
  skillIds: number[];
  profilePictureUrl?: string;
  
  // Site assignment (if creating from site context)
  assignToSite: boolean;
  siteRole: string;
  hourlyRate: number;
  overtimeRate: number;
}

const EnhancedCreateWorkerForm: React.FC<CreateWorkerFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  const { siteId: contextSiteId } = useSiteContext();
  const currentSiteId = siteId || contextSiteId;

  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: 'ABC Construction',
    nationalId: '',
    phoneNumber: '',
    email: '',
    dateOfBirth: '',
    gender: 'Male',
    employeeNumber: '',
    hireDate: new Date().toISOString().split('T')[0],
    tradeIds: [],
    skillIds: [],
    assignToSite: !!currentSiteId,
    siteRole: '',
    hourlyRate: 25.00,
    overtimeRate: 37.50
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate employee number automatically
  const generateEmployeeNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `EMP-${timestamp}`;
  };

  // Auto-generate employee number when component mounts
  React.useEffect(() => {
    if (!formData.employeeNumber) {
      setFormData(prev => ({
        ...prev,
        employeeNumber: generateEmployeeNumber()
      }));
    }
  }, []);

  // Calculate required trainings based on selected trades
  const requiredTrainings = useMemo(() => {
    const trainings = new Set<string>();
    formData.tradeIds.forEach(tradeId => {
      const trade = mockTrades.find(t => t.id === tradeId);
      if (trade && trade.requiredCertifications) {
        trade.requiredCertifications.forEach(cert => trainings.add(cert));
      }
    });
    return Array.from(trainings);
  }, [formData.tradeIds]);

  // Fetch data (using mock data for now)
  const trades = mockTrades;
  const skills = mockSkills;

  const [createWorker] = useMutation(CREATE_WORKER_WITH_TRAINING, {
    onCompleted: (data) => {
      toast.success('Worker created successfully!');
      if (onSuccess) {
        onSuccess(data.createCompanyWorker);
      } else {
        navigate(currentSiteId ? `/sites/${currentSiteId}/workers` : '/workers');
      }
    },
    onError: (error) => {
      toast.error(`Failed to create worker: ${error.message}`);
    }
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.nationalId.trim()) newErrors.nationalId = 'National ID is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
    if (!formData.employeeNumber.trim()) newErrors.employeeNumber = 'Employee number is required';
    if (!formData.hireDate) newErrors.hireDate = 'Hire date is required';
    if (formData.tradeIds.length === 0) newErrors.tradeIds = 'At least one trade must be selected';
    
    if (formData.assignToSite && !formData.siteRole.trim()) {
      newErrors.siteRole = 'Site role is required when assigning to site';
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Validate phone number format
    const phoneRegex = /^\+?[\d\s-()]+$/;
    if (formData.phoneNumber && !phoneRegex.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setIsSubmitting(true);

    try {
      const input = {
        name: formData.name,
        company: formData.company,
        nationalId: formData.nationalId,
        phoneNumber: formData.phoneNumber,
        email: formData.email,
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        employeeNumber: formData.employeeNumber,
        hireDate: formData.hireDate,
        tradeIds: formData.tradeIds,
        skillIds: formData.skillIds,
        profilePictureUrl: formData.profilePictureUrl,
        
        // Site assignment if applicable
        ...(formData.assignToSite && currentSiteId && {
          siteId: currentSiteId,
          role: formData.siteRole,
          hourlyRate: formData.hourlyRate,
          overtimeRate: formData.overtimeRate
        })
      };

      await createWorker({ variables: { input } });
    } catch (error) {
      console.error('Error creating worker:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate(currentSiteId ? `/sites/${currentSiteId}/workers` : '/workers');
    }
  };

  const breadcrumbs = currentSiteId ? [
    { name: 'Dashboard', path: '/' },
    { name: `Site ${currentSiteId}`, path: `/sites/${currentSiteId}/dashboard` },
    { name: 'Workers', path: `/sites/${currentSiteId}/workers` },
    { name: 'Add Worker', path: `/sites/${currentSiteId}/workers/create` }
  ] : [
    { name: 'Dashboard', path: '/' },
    { name: 'Workers', path: '/workers' },
    { name: 'Add Worker', path: '/workers/create' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <TopBar
        subtitle={currentSiteId ? `Create a new worker and assign to ${currentSiteId}` : "Create a new worker in the company database"}
      />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <FloatingCard title="Worker Information" breadcrumbs={breadcrumbs}>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter full name"
                  />
                  {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <IdCard className="w-4 h-4 inline mr-2" />
                    National ID *
                  </label>
                  <input
                    type="text"
                    value={formData.nationalId}
                    onChange={(e) => setFormData(prev => ({ ...prev, nationalId: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.nationalId ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter national ID"
                  />
                  {errors.nationalId && <p className="text-red-500 text-sm mt-1">{errors.nationalId}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="w-4 h-4 inline mr-2" />
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="+254 700 000 000"
                  />
                  {errors.phoneNumber && <p className="text-red-500 text-sm mt-1">{errors.phoneNumber}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="w-4 h-4 inline mr-2" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.email ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Date of Birth *
                  </label>
                  <input
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => setFormData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.dateOfBirth ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.dateOfBirth && <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Gender *
                  </label>
                  <select
                    value={formData.gender}
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as 'Male' | 'Female' | 'Other' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Employment Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Employment Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Building className="w-4 h-4 inline mr-2" />
                    Company
                  </label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Company name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Employee Number *
                  </label>
                  <input
                    type="text"
                    value={formData.employeeNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, employeeNumber: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.employeeNumber ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="EMP-001"
                  />
                  {errors.employeeNumber && <p className="text-red-500 text-sm mt-1">{errors.employeeNumber}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Hire Date *
                  </label>
                  <input
                    type="date"
                    value={formData.hireDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, hireDate: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.hireDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.hireDate && <p className="text-red-500 text-sm mt-1">{errors.hireDate}</p>}
                </div>
              </div>
            </div>

            {/* Trades and Skills */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Trades and Skills</h3>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trades * (Select at least one)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {trades.map((trade) => (
                    <label key={trade.id} className="flex items-center space-x-2 p-3 border rounded-md hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={formData.tradeIds.includes(trade.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              tradeIds: [...prev.tradeIds, trade.id]
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              tradeIds: prev.tradeIds.filter(id => id !== trade.id)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div>
                        <span className="text-sm font-medium text-gray-900">{trade.name}</span>
                        <p className="text-xs text-gray-500">{trade.description}</p>
                      </div>
                    </label>
                  ))}
                </div>
                {errors.tradeIds && <p className="text-red-500 text-sm mt-1">{errors.tradeIds}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Skills (Optional)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {skills.map((skill) => (
                    <label key={skill.id} className="flex items-center space-x-2 p-3 border rounded-md hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={formData.skillIds.includes(skill.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              skillIds: [...prev.skillIds, skill.id]
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              skillIds: prev.skillIds.filter(id => id !== skill.id)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div>
                        <span className="text-sm font-medium text-gray-900">{skill.name}</span>
                        <p className="text-xs text-gray-500">{skill.description}</p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Training Requirements Preview */}
            {requiredTrainings.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Required Training</h3>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-yellow-800">
                        Training Requirements
                      </h4>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Based on the selected trades, this worker will need the following training:</p>
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          {requiredTrainings.map((training, index) => (
                            <li key={index}>{training}</li>
                          ))}
                        </ul>
                        <p className="mt-2 text-xs">
                          Training compliance will be tracked automatically after worker creation.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Site Assignment (if creating from site context) */}
            {currentSiteId && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Site Assignment</h3>

                <div className="mb-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.assignToSite}
                      onChange={(e) => setFormData(prev => ({ ...prev, assignToSite: e.target.checked }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Assign to {currentSiteId} immediately
                    </span>
                  </label>
                </div>

                {formData.assignToSite && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Site Role *
                      </label>
                      <input
                        type="text"
                        value={formData.siteRole}
                        onChange={(e) => setFormData(prev => ({ ...prev, siteRole: e.target.value }))}
                        className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                          errors.siteRole ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="e.g., Senior Carpenter"
                      />
                      {errors.siteRole && <p className="text-red-500 text-sm mt-1">{errors.siteRole}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hourly Rate ($)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.hourlyRate}
                        onChange={(e) => setFormData(prev => ({ ...prev, hourlyRate: parseFloat(e.target.value) || 0 }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="25.00"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Overtime Rate ($)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.overtimeRate}
                        onChange={(e) => setFormData(prev => ({ ...prev, overtimeRate: parseFloat(e.target.value) || 0 }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="37.50"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <X className="w-4 h-4 mr-2 inline" />
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-4 h-4 mr-2 inline" />
                {isSubmitting ? 'Creating...' : 'Create Worker'}
              </button>
            </div>
          </form>
        </FloatingCard>
      </div>
    </div>
  );
};

export default EnhancedCreateWorkerForm;
