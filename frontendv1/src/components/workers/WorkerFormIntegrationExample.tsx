import React, { useState } from 'react';
import { User, Plus } from 'lucide-react';
import CreateWorkerForm from './CreateWorkerForm';
import QuickActionCard from '../data/shared/QuickActionCard';

/**
 * Example component showing how to integrate the CreateWorkerForm
 * into the existing DataDashboard or any other component
 */
const WorkerFormIntegrationExample: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleWorkerCreated = (worker: any) => {
    console.log('New worker created:', worker);
    // Here you would typically:
    // 1. Show a success toast notification
    // 2. Refresh the workers list
    // 3. Navigate to the worker details page
    // 4. Update any relevant statistics
    
    alert(`Worker "${worker.name}" has been created successfully!`);
    setShowCreateForm(false);
  };

  const handleCancel = () => {
    setShowCreateForm(false);
  };

  if (showCreateForm) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <CreateWorkerForm
            onSuccess={handleWorkerCreated}
            onCancel={handleCancel}
            useDummyData={true} // Set to false to use real GraphQL queries
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Example of how to add to DataDashboard Quick Actions */}
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Worker Management Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <QuickActionCard
            title="Create New Worker"
            description="Add a new worker to the system"
            icon={<User className="h-5 w-5" />}
            onClick={() => setShowCreateForm(true)}
          />
          <QuickActionCard
            title="Import Workers"
            description="Bulk import workers from CSV"
            icon={<Plus className="h-5 w-5" />}
            onClick={() => alert('Import functionality would go here')}
          />
          <QuickActionCard
            title="Worker Reports"
            description="Generate worker reports"
            icon={<User className="h-5 w-5" />}
            onClick={() => alert('Reports functionality would go here')}
          />
          <QuickActionCard
            title="Training Matrix"
            description="View worker training status"
            icon={<User className="h-5 w-5" />}
            onClick={() => alert('Training matrix would go here')}
          />
        </div>
      </div>

      {/* Integration Instructions */}
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Integration Instructions</h3>
        <div className="prose max-w-none">
          <h4 className="text-md font-medium mb-2">To add to DataDashboard:</h4>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
            <li>Import the CreateWorkerForm component</li>
            <li>Add state to manage form visibility: <code className="bg-gray-100 px-1 rounded">const [showWorkerForm, setShowWorkerForm] = useState(false)</code></li>
            <li>Add a new QuickActionCard with onClick handler to show the form</li>
            <li>Conditionally render the form when showWorkerForm is true</li>
            <li>Handle success/cancel callbacks to hide the form</li>
          </ol>

          <h4 className="text-md font-medium mb-2 mt-6">GraphQL Integration:</h4>
          <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
            <li>Set <code className="bg-gray-100 px-1 rounded">useDummyData={false}</code> to use real GraphQL queries</li>
            <li>Ensure your GraphQL endpoint supports the allTrainings, allTrades, and allSkills queries</li>
            <li>The createWorker mutation should match the schema provided</li>
          </ul>

          <h4 className="text-md font-medium mb-2 mt-6">Form Features:</h4>
          <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
            <li>Comprehensive validation for all required fields</li>
            <li>Photo upload with drag-and-drop support</li>
            <li>Multi-select checkboxes for skills, training, and trades</li>
            <li>Real-time validation feedback with error messages</li>
            <li>Loading states for form submission and data fetching</li>
            <li>Responsive design that works on mobile and desktop</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default WorkerFormIntegrationExample;
