import { useState, useEffect } from 'react';
// import { SafetyDashboardData, Incident, ToolboxTalk, SafetyObservation, SiteRAMS } from '../types/safety';
// import { useToolboxSessions } from '../../../hooks/useGraphQL';

interface UseSafetyDataOptions {
	date?: string;
	filters?: Record<string, any>;
	autoRefresh?: boolean;
}

export const useSafetyData = (
	siteId: string,
	endpoint: string,
	options: UseSafetyDataOptions = {},
) => {
	const [data, setData] = useState<any>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const queryParams = new URLSearchParams();
      if (options.date) queryParams.append('date', options.date);
      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value) queryParams.append(key, value);
        });
      }

      // For backend integration, this would be:
      // const url = `/api/sites/${siteId}/safety/${endpoint}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      // const response = await fetch(url);
      // const result = await response.json();
      // setData(result);

      // For now, return mock data based on endpoint
      const mockData = getMockData(endpoint, siteId);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setData(mockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

	useEffect(() => {
		fetchData();
	}, [siteId, endpoint, JSON.stringify(options)]);

	const mutate = {
		create: async (newData: any) => {
			try {
				// Simulate API call
				await new Promise((resolve) => setTimeout(resolve, 300));

				// For now, just add to existing data
				if (Array.isArray(data)) {
					const newItem = {
						...newData,
						id: Date.now().toString(),
						createdAt: new Date().toISOString(),
					};
					setData([newItem, ...data]);
				}

				return newData;
			} catch (error) {
				throw new Error("Failed to create");
			}
		},

		update: async (id: string, updateData: any) => {
			try {
				// Simulate API call
				await new Promise((resolve) => setTimeout(resolve, 300));

				if (Array.isArray(data)) {
					setData(
						data.map((item) =>
							item.id === id
								? {
										...item,
										...updateData,
										updatedAt: new Date().toISOString(),
									}
								: item,
						),
					);
				}

				return updateData;
			} catch (error) {
				throw new Error("Failed to update");
			}
		},

		delete: async (id: string) => {
			try {
				// Simulate API call
				await new Promise((resolve) => setTimeout(resolve, 300));

				if (Array.isArray(data)) {
					setData(data.filter((item) => item.id !== id));
				}

				return true;
			} catch (error) {
				throw new Error("Failed to delete");
			}
		},
	};

	return {
		data,
		isLoading,
		error,
		refetch: fetchData,
		mutate,
	};
};

// Mock data generator for development
const getMockData = (endpoint: string, siteId: string) => {
  switch (endpoint) {
    case 'dashboard':
      return {
        totalIncidents: 12,
        lostTimeIncidents: 2,
        toolboxTalks: 15,
        toolboxAttendance: 95,
        safetyObservations: 24,
        ramsDocuments: 8,
        overdueActions: 3,
        incidentTrend: 'down' as const,
        incidentTrends: [
          { date: '2024-01-01', incidents: 2, observations: 8, toolboxTalks: 5 },
          { date: '2024-01-02', incidents: 1, observations: 12, toolboxTalks: 5 },
          { date: '2024-01-03', incidents: 0, observations: 15, toolboxTalks: 5 },
          { date: '2024-01-04', incidents: 3, observations: 10, toolboxTalks: 5 },
          { date: '2024-01-05', incidents: 1, observations: 18, toolboxTalks: 5 },
        ],
        recentActivities: [
          {
            id: '1',
            type: 'incident' as const,
            title: 'Minor injury reported',
            description: 'Worker cut finger on metal edge',
            timestamp: '2024-01-05T14:30:00Z',
            severity: 'low' as const,
            status: 'investigating'
          },
          {
            id: '2',
            type: 'toolbox-talk' as const,
            title: 'Fall protection training completed',
            description: '25 workers attended',
            timestamp: '2024-01-05T08:00:00Z',
            status: 'completed'
          },
          {
            id: '3',
            type: 'observation' as const,
            title: 'Positive safety behavior observed',
            description: 'Worker properly using PPE',
            timestamp: '2024-01-05T11:15:00Z',
            status: 'reviewed'
          }
        ],
        recentIncidents: [
          {
            id: '1',
            siteId,
            reportedByUserId: 'user1',
            reportedByName: 'John Doe',
            dateOfIncident: '2024-01-05',
            timeOfIncident: '14:30',
            locationOnSite: 'Building A - 2nd Floor',
            incidentType: 'accident-with-injury',
            severity: 'low',
            description: 'Worker cut finger on sharp metal edge while handling materials',
            status: 'investigating',
            createdAt: '2024-01-05T14:35:00Z'
          },
          {
            id: '2',
            siteId,
            reportedByUserId: 'user2',
            reportedByName: 'Sarah Wilson',
            dateOfIncident: '2024-01-04',
            timeOfIncident: '10:15',
            locationOnSite: 'Zone B - Equipment Area',
            incidentType: 'near-miss',
            severity: 'medium',
            description: 'Crane load swung close to worker due to wind',
            status: 'closed',
            createdAt: '2024-01-04T10:20:00Z'
          }
        ],
        recentToolboxTalks: [
          {
            id: '1',
            siteId,
            date: '2024-01-05',
            timeStarted: '08:00',
            topic: 'Fall Protection and Ladder Safety',
            supervisorName: 'John Smith',
            status: 'completed',
            createdAt: '2024-01-05T08:00:00Z'
          },
          {
            id: '2',
            siteId,
            date: '2024-01-04',
            timeStarted: '08:00',
            topic: 'PPE Requirements and Inspection',
            supervisorName: 'Sarah Johnson',
            status: 'completed',
            createdAt: '2024-01-04T08:00:00Z'
          }
        ],
        recentObservations: [
          {
            id: '1',
            siteId,
            reporterName: 'John Doe',
            dateOfObservation: '2024-01-05',
            timeOfObservation: '11:15',
            locationOnSite: 'Building A - Ground Floor',
            observationType: 'safe-behavior',
            category: 'ppe',
            description: 'Worker consistently wearing all required PPE including hard hat, safety glasses, and high-vis vest',
            status: 'completed',
            priority: 'low',
            createdAt: '2024-01-05T11:15:00Z'
          },
          {
            id: '2',
            siteId,
            dateOfObservation: '2024-01-04',
            timeOfObservation: '15:30',
            locationOnSite: 'Level 3 scaffolding platform',
            observationType: 'at-risk-condition',
            category: 'housekeeping',
            description: 'Tools and materials left scattered on scaffolding platform creating trip hazards',
            status: 'assigned',
            priority: 'high',
            createdAt: '2024-01-04T15:30:00Z'
          }
        ],
        upcomingDeadlines: [
          {
            id: '1',
            type: 'capa' as const,
            title: 'Install additional safety barriers',
            dueDate: '2024-01-10',
            priority: 'high' as const,
            assignedTo: 'John Smith'
          },
          {
            id: '2',
            type: 'rams-expiry' as const,
            title: 'Excavation RAMS renewal',
            dueDate: '2024-01-15',
            priority: 'medium' as const
          }
        ]
      };
      
    case 'incidents':
      return [
        {
          id: '1',
          siteId,
          reportedByUserId: 'user1',
          reportedByName: 'John Doe',
          dateOfIncident: '2024-01-05',
          timeOfIncident: '14:30',
          locationOnSite: 'Building A - 2nd Floor',
          incidentType: 'accident-with-injury' as const,
          severity: 'low' as const,
          description: 'Worker cut finger on sharp metal edge while handling materials',
          involvedPersons: [
            {
              workerId: 'worker1',
              name: 'Mike Johnson',
              type: 'worker' as const,
              injuryType: 'Cut',
              medicalAttentionRequired: true
            }
          ],
          status: 'investigating' as const,
          createdAt: '2024-01-05T14:35:00Z',
          updatedAt: '2024-01-05T14:35:00Z'
        },
        {
          id: '2',
          siteId,
          reportedByUserId: 'user2',
          reportedByName: 'Sarah Smith',
          dateOfIncident: '2024-01-03',
          timeOfIncident: '10:15',
          locationOnSite: 'Parking Area',
          incidentType: 'near-miss' as const,
          severity: 'medium' as const,
          description: 'Crane load swung close to workers due to wind',
          involvedPersons: [],
          status: 'closed' as const,
          createdAt: '2024-01-03T10:20:00Z',
          updatedAt: '2024-01-04T16:00:00Z'
        }
      ];
      
    case 'toolbox-talks':
      return [
        {
          id: '1',
          siteId,
          date: '2024-01-05',
          timeStarted: '08:00',
          topic: 'Fall Protection and Ladder Safety',
          supervisorId: 'supervisor1',
          supervisorName: 'John Smith',
          attendees: [
            { workerId: 'worker1', workerName: 'Mike Johnson', attended: true, attendedAt: '08:05' },
            { workerId: 'worker2', workerName: 'Sarah Wilson', attended: true, attendedAt: '08:02' },
            { workerId: 'worker3', workerName: 'David Brown', attended: false },
            { workerId: 'worker4', workerName: 'Lisa Chen', attended: true, attendedAt: '08:03' },
            { workerId: 'worker5', workerName: 'Tom Wilson', attended: true, attendedAt: '08:07' }
          ],
          status: 'completed' as const,
          createdAt: '2024-01-05T08:00:00Z',
          updatedAt: '2024-01-05T08:30:00Z'
        },
        {
          id: '2',
          siteId,
          date: '2024-01-04',
          timeStarted: '08:00',
          topic: 'PPE Requirements and Inspection',
          supervisorId: 'supervisor2',
          supervisorName: 'Sarah Johnson',
          attendees: [
            { workerId: 'worker1', workerName: 'Mike Johnson', attended: true, attendedAt: '08:02' },
            { workerId: 'worker2', workerName: 'Sarah Wilson', attended: true, attendedAt: '08:01' },
            { workerId: 'worker3', workerName: 'David Brown', attended: true, attendedAt: '08:04' },
            { workerId: 'worker6', workerName: 'Alex Rodriguez', attended: false },
            { workerId: 'worker7', workerName: 'Emma Davis', attended: true, attendedAt: '08:06' }
          ],
          status: 'completed' as const,
          createdAt: '2024-01-04T08:00:00Z',
          updatedAt: '2024-01-04T08:25:00Z'
        },
        {
          id: '3',
          siteId,
          date: '2024-01-03',
          timeStarted: '08:00',
          topic: 'Electrical Safety and Lockout/Tagout Procedures',
          supervisorId: 'supervisor1',
          supervisorName: 'John Smith',
          attendees: [
            { workerId: 'worker1', workerName: 'Mike Johnson', attended: true, attendedAt: '08:03' },
            { workerId: 'worker2', workerName: 'Sarah Wilson', attended: false },
            { workerId: 'worker3', workerName: 'David Brown', attended: true, attendedAt: '08:05' },
            { workerId: 'worker8', workerName: 'James Miller', attended: true, attendedAt: '08:02' }
          ],
          status: 'completed' as const,
          createdAt: '2024-01-03T08:00:00Z',
          updatedAt: '2024-01-03T08:35:00Z'
        },
        {
          id: '4',
          siteId,
          date: '2024-01-02',
          timeStarted: '08:00',
          topic: 'Weather-Related Safety Precautions',
          supervisorId: 'supervisor3',
          supervisorName: 'Lisa Chen',
          attendees: [
            { workerId: 'worker1', workerName: 'Mike Johnson', attended: false },
            { workerId: 'worker2', workerName: 'Sarah Wilson', attended: true, attendedAt: '08:01' },
            { workerId: 'worker9', workerName: 'Robert Taylor', attended: true, attendedAt: '08:04' }
          ],
          status: 'cancelled' as const,
          createdAt: '2024-01-02T08:00:00Z',
          updatedAt: '2024-01-02T07:45:00Z'
        },
        {
          id: '5',
          siteId,
          date: '2024-01-08',
          timeStarted: '08:00',
          topic: 'Crane and Heavy Equipment Safety',
          supervisorId: 'supervisor2',
          supervisorName: 'Sarah Johnson',
          attendees: [
            { workerId: 'worker1', workerName: 'Mike Johnson', attended: false },
            { workerId: 'worker2', workerName: 'Sarah Wilson', attended: false },
            { workerId: 'worker3', workerName: 'David Brown', attended: false },
            { workerId: 'worker10', workerName: 'Maria Garcia', attended: false }
          ],
          status: 'scheduled' as const,
          createdAt: '2024-01-07T16:00:00Z',
          updatedAt: '2024-01-07T16:00:00Z'
        }
      ];
      
    case 'observations':
      return [
        {
          id: '1',
          siteId,
          reportedByUserId: 'user1',
          reporterName: 'John Doe',
          dateOfObservation: '2024-01-05',
          timeOfObservation: '11:15',
          siteArea: 'Building A',
          locationOnSite: 'Ground Floor - Near main entrance',
          observationType: 'safe-behavior' as const,
          category: 'ppe' as const,
          description: 'Worker consistently wearing all required PPE including hard hat, safety glasses, and high-vis vest',
          actionTakenImmediate: 'Acknowledged the good practice',
          recommendedAction: 'Share this as a positive example in next toolbox talk',
          isAnonymous: false,
          status: 'completed' as const,
          priority: 'low' as const,
          reviewedBy: 'safety1',
          reviewedAt: '2024-01-05T12:00:00Z',
          reviewNotes: 'Excellent safety behavior observed. Will use as positive example.',
          assignedTo: 'safety2',
          assignedToName: 'Mike Johnson',
          assignedAt: '2024-01-05T12:05:00Z',
          assignedBy: 'safety1',
          actionTaken: 'Shared in morning toolbox talk as positive example',
          actionCompletedAt: '2024-01-06T08:30:00Z',
          actionCompletedBy: 'safety2',
          followUpRequired: false,
          createdAt: '2024-01-05T11:20:00Z',
          updatedAt: '2024-01-06T08:30:00Z'
        },
        {
          id: '2',
          siteId,
          reportedByUserId: 'user2',
          reporterName: undefined,
          dateOfObservation: '2024-01-04',
          timeOfObservation: '15:30',
          siteArea: 'Building B',
          locationOnSite: 'Level 3 scaffolding platform',
          observationType: 'at-risk-condition' as const,
          category: 'housekeeping' as const,
          description: 'Tools and materials left scattered on scaffolding platform creating trip hazards',
          actionTakenImmediate: 'Cleared the immediate area and secured loose tools',
          recommendedAction: 'Implement end-of-shift cleanup checklist and provide additional tool storage',
          photoUrls: ['photo_1704380100_0.jpg'],
          isAnonymous: true,
          status: 'assigned' as const,
          priority: 'high' as const,
          reviewedBy: 'safety1',
          reviewedAt: '2024-01-04T16:00:00Z',
          reviewNotes: 'Serious housekeeping issue that could lead to falls. Requires immediate action.',
          assignedTo: 'safety3',
          assignedToName: 'Lisa Chen',
          assignedAt: '2024-01-04T16:05:00Z',
          assignedBy: 'safety1',
          followUpRequired: true,
          followUpDate: '2024-01-11T00:00:00Z',
          createdAt: '2024-01-04T15:35:00Z',
          updatedAt: '2024-01-04T16:05:00Z'
        },
        {
          id: '3',
          siteId,
          reportedByUserId: 'user3',
          reporterName: 'Sarah Wilson',
          dateOfObservation: '2024-01-03',
          timeOfObservation: '09:45',
          siteArea: 'Equipment Yard',
          locationOnSite: 'Near the crane storage area',
          observationType: 'at-risk-behavior' as const,
          category: 'equipment-use' as const,
          description: 'Operator not performing pre-use inspection on mobile crane',
          actionTakenImmediate: 'Stopped operation and reminded operator of inspection requirements',
          recommendedAction: 'Refresher training on equipment inspection procedures',
          isAnonymous: false,
          status: 'in-progress' as const,
          priority: 'critical' as const,
          reviewedBy: 'safety1',
          reviewedAt: '2024-01-03T10:00:00Z',
          reviewNotes: 'Critical safety violation. Equipment inspection is mandatory before use.',
          assignedTo: 'safety4',
          assignedToName: 'David Brown',
          assignedAt: '2024-01-03T10:05:00Z',
          assignedBy: 'safety1',
          followUpRequired: true,
          followUpDate: '2024-01-04T00:00:00Z',
          followUpNotes: 'Ensure operator receives refresher training before returning to work',
          createdAt: '2024-01-03T09:50:00Z',
          updatedAt: '2024-01-03T10:05:00Z'
        },
        {
          id: '4',
          siteId,
          reportedByUserId: 'user4',
          reporterName: undefined,
          dateOfObservation: '2024-01-02',
          timeOfObservation: '14:20',
          siteArea: 'Main Entrance',
          locationOnSite: 'Security checkpoint area',
          observationType: 'safe-condition' as const,
          category: 'other' as const,
          description: 'New safety signage installed and clearly visible to all workers entering site',
          actionTakenImmediate: 'Took photos to document the improvement',
          recommendedAction: 'Consider similar signage for other high-traffic areas',
          isAnonymous: true,
          status: 'open' as const,
          priority: 'low' as const,
          followUpRequired: false,
          createdAt: '2024-01-02T14:25:00Z'
        },
        {
          id: '5',
          siteId,
          reportedByUserId: 'user5',
          reporterName: 'Lisa Chen',
          dateOfObservation: '2024-01-06',
          timeOfObservation: '10:30',
          siteArea: 'Workshop Area',
          locationOnSite: 'Tool storage room',
          observationType: 'recommendation' as const,
          category: 'housekeeping' as const,
          description: 'Tool storage could be improved with better labeling and organization system',
          actionTakenImmediate: 'None - suggestion for improvement',
          recommendedAction: 'Implement color-coded tool storage system with clear labeling',
          isAnonymous: false,
          status: 'open' as const,
          priority: 'low' as const,
          createdAt: '2024-01-06T10:35:00Z',
          updatedAt: '2024-01-06T10:35:00Z'
        },
        {
          id: '6',
          siteId,
          reportedByUserId: 'user6',
          reporterName: undefined,
          dateOfObservation: '2024-01-07',
          timeOfObservation: '14:15',
          siteArea: 'Loading Dock',
          locationOnSite: 'Material delivery area',
          observationType: 'alert' as const,
          category: 'traffic-management' as const,
          description: 'Delivery truck blocking emergency vehicle access route',
          actionTakenImmediate: 'Redirected truck to designated unloading area',
          recommendedAction: 'Install clearer signage for delivery vehicle routing',
          isAnonymous: true,
          status: 'under-review' as const,
          priority: 'high' as const,
          reviewedBy: 'safety1',
          reviewedAt: '2024-01-07T15:00:00Z',
          reviewNotes: 'Important access control issue. Signage improvement needed.',
          createdAt: '2024-01-07T14:20:00Z',
          updatedAt: '2024-01-07T15:00:00Z'
        }
      ];
      
    case 'rams':
      return [
        {
          id: '1',
          siteId,
          title: 'Excavation and Trenching Safety',
          version: 'v2.1',
          effectiveDate: '2024-01-01',
          expiryDate: '2024-12-31',
          sourceType: 'upload' as const,
          documentUrl: '/documents/excavation-rams-v2.1.pdf',
          status: 'active' as const,
          uploadedByUserId: 'user1',
          uploadedByName: 'Safety Manager',
          description: 'Comprehensive safety procedures for all excavation work',
          applicableActivities: ['Excavation', 'Trenching', 'Foundation Work'],
          createdAt: '2024-01-01T09:00:00Z',
          updatedAt: '2024-01-01T09:00:00Z'
        },
        {
          id: '2',
          siteId,
          title: 'Working at Height - General',
          version: 'v1.3',
          effectiveDate: '2023-12-01',
          expiryDate: '2024-11-30',
          sourceType: 'master-link' as const,
          masterRamsId: 'master-wah-001',
          status: 'active' as const,
          uploadedByUserId: 'user2',
          uploadedByName: 'Site Manager',
          description: 'Standard procedures for working at height activities',
          applicableActivities: ['Scaffolding', 'Ladder Work', 'Roof Work'],
          createdAt: '2023-12-01T10:00:00Z',
          updatedAt: '2023-12-01T10:00:00Z'
        }
      ];
      
    default:
      return null;
  }
};
