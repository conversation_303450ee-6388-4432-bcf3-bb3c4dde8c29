import React, { useState, useMemo } from 'react';
import { Message<PERSON>quare, Eye, Clock, MapPin, ThumbsUp, <PERSON>ertT<PERSON>gle, Lightbulb, Shield } from 'lucide-react';
import { useSafetyData } from './hooks/useSafetyData';
import { SafetyObservation, ObservationType } from './types/safety';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface SafetyObservationsProps {
	siteId: string;
}

// Badge components matching the incident dashboard style
const ObservationTypeBadge: React.FC<{ type: string }> = ({ type }) => {
	const getTypeConfig = () => {
		switch (type) {
			case "positive":
				return { label: "Positive", className: "bg-green-100 text-green-800" };
			case "negative":
				return { label: "Negative", className: "bg-red-100 text-red-800" };
			case "alert":
				return { label: "Alert", className: "bg-orange-100 text-orange-800" };
			case "recommendation":
				return { label: "Recommendation", className: "bg-blue-100 text-blue-800" };
			// Legacy support for existing observation types
			case "safe-behavior":
			case "safe-condition":
				return { label: "Positive", className: "bg-green-100 text-green-800" };
			case "at-risk-behavior":
			case "at-risk-condition":
				return { label: "Negative", className: "bg-red-100 text-red-800" };
			default:
				return { label: type, className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getTypeConfig();
	return (
		<span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
			{config.label}
		</span>
	);
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "open":
				return { className: "bg-blue-100 text-blue-800" };
			case "under-review":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "assigned":
				return { className: "bg-purple-100 text-purple-800" };
			case "in-progress":
				return { className: "bg-orange-100 text-orange-800" };
			case "completed":
				return { className: "bg-green-100 text-green-800" };
			case "closed":
				return { className: "bg-gray-100 text-gray-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}>
			{status.replace("-", " ")}
		</span>
	);
};

const SafetyObservations: React.FC<SafetyObservationsProps> = ({ siteId }) => {
	const { data: observations, isLoading } = useSafetyData(siteId, "observations");

	// State for search and filtering (matching IncidentManagement pattern)
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedObservationType, setSelectedObservationType] = useState("all");

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 20;

	// Modal state
	const [selectedObservation, setSelectedObservation] = useState<SafetyObservation | null>(null);
	const [modalNotes, setModalNotes] = useState("");
	const [modalStatus, setModalStatus] = useState("");
	const [isSaving, setIsSaving] = useState(false);

	// Observation type filters
	const observationTypeFilters: TagOption[] = [
		{ id: "positive", name: "Positive" },
		{ id: "negative", name: "Negative" },
		{ id: "alert", name: "Alert" },
		{ id: "recommendation", name: "Recommendation" }
	];

	const handleLogObservation = () => {
		console.log("Log observation clicked");
		// TODO: Navigate to observation logging form
	};

	const handleViewObservation = (observation: SafetyObservation) => {
		setSelectedObservation(observation);
		setModalNotes(observation.reviewNotes || "");
		setModalStatus(observation.status);
	};

	const handleCloseModal = () => {
		setSelectedObservation(null);
		setModalNotes("");
		setModalStatus("");
		setIsSaving(false);
	};

	const handleSaveObservation = async () => {
		if (!selectedObservation) return;

		setIsSaving(true);
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1000));

			console.log("Saving observation:", {
				id: selectedObservation.id,
				notes: modalNotes,
				status: modalStatus
			});

			// In a real implementation, this would update the observation via API
			// For now, we'll just close the modal
			handleCloseModal();

			// TODO: Show success message and refresh data
		} catch (error) {
			console.error("Error saving observation:", error);
			// TODO: Show error message
		} finally {
			setIsSaving(false);
		}
	};

	// Helper function to map observation types to new categories
	const mapObservationType = (observationType: ObservationType): string => {
		switch (observationType) {
			case "safe-behavior":
			case "safe-condition":
				return "positive";
			case "at-risk-behavior":
			case "at-risk-condition":
				return "negative";
			default:
				return observationType;
		}
	};

	// Filtering logic (matching IncidentManagement pattern)
	const filteredObservationsAll = useMemo(() => {
		if (!observations) return [];

		return observations.filter((observation: SafetyObservation) => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				observation.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
				observation.locationOnSite.toLowerCase().includes(searchQuery.toLowerCase()) ||
				observation.reporterName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
				observation.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
				observation.siteArea?.toLowerCase().includes(searchQuery.toLowerCase());

			// Observation type filter
			const observationTypeCategory = mapObservationType(observation.observationType);
			const matchesObservationType = selectedObservationType === "all" ||
				observationTypeCategory === selectedObservationType;

			return matchesSearch && matchesObservationType;
		});
	}, [observations, searchQuery, selectedObservationType]);

	// Paginated results
	const filteredObservations = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredObservationsAll.slice(startIndex, endIndex);
	}, [filteredObservationsAll, currentPage, itemsPerPage]);

	// Pagination calculations
	const totalPages = Math.ceil(filteredObservationsAll.length / itemsPerPage);

	// Handlers for search and filtering
	const handleSearchChange = (query: string) => {
		setSearchQuery(query);
		setCurrentPage(1);
	};

	const handleObservationTypeChange = (type: string) => {
		setSelectedObservationType(type);
		setCurrentPage(1);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-center py-12">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
						<p className="mt-2 text-sm text-gray-500">Loading observations...</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header - matching IncidentManagement style */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Safety Observations</h2>
					<p className="text-sm text-gray-600">Track and manage safety observations on site</p>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						Total {filteredObservationsAll.length} observations
					</div>
					<button
						onClick={handleLogObservation}
						className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
					>
						<MessageSquare className="h-4 w-4" />
						<span>Log Observation</span>
					</button>
				</div>
			</div>

			{/* Search and Filter - matching IncidentManagement style */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={handleSearchChange}
				searchPlaceholder="Search by observation description, location, reporter, or ID..."
				tags={[{ id: "all", name: "All" }, ...observationTypeFilters]}
				selectedTagId={selectedObservationType}
				onTagChange={handleObservationTypeChange}
			/>

			{/* Observations Table - matching IncidentManagement exact styling */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Observation Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Type
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Description
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Reporter
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredObservations.map((observation: SafetyObservation) => (
							<tr key={observation.id} className="hover:bg-gray-50 transition-colors">
								{/* Observation Details */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center space-x-2 mb-1">
											<h4 className="text-sm font-medium text-gray-900">
												ID: {observation.id.slice(-6)}
											</h4>
										</div>
										<div className="flex items-center text-xs text-gray-600 mb-1">
											<Clock className="h-3 w-3 mr-1" />
											{new Date(observation.dateOfObservation).toLocaleDateString()} at {observation.timeOfObservation}
										</div>
										<div className="flex items-center text-xs text-gray-500">
											<MapPin className="h-3 w-3 mr-1" />
											{observation.siteArea ? `${observation.siteArea} - ${observation.locationOnSite}` : observation.locationOnSite}
										</div>
									</div>
								</td>

								{/* Type */}
								<td className="px-6 py-4">
									<div className="flex items-center">
										{mapObservationType(observation.observationType) === 'positive' ? (
											<ThumbsUp className="h-4 w-4 text-green-500 mr-2" />
										) : mapObservationType(observation.observationType) === 'negative' ? (
											<AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
										) : mapObservationType(observation.observationType) === 'alert' ? (
											<Shield className="h-4 w-4 text-orange-500 mr-2" />
										) : (
											<Lightbulb className="h-4 w-4 text-blue-500 mr-2" />
										)}
										<div>
											<div className="text-sm font-medium text-gray-900">
												<ObservationTypeBadge type={mapObservationType(observation.observationType)} />
											</div>
											<div className="text-xs text-gray-500 mt-1 capitalize">
												{observation.category?.replace('-', ' ') || 'General'}
											</div>
										</div>
									</div>
								</td>

								{/* Description */}
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900 line-clamp-2">
										{observation.description}
									</div>
								</td>

								{/* Reporter */}
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900">
										{observation.isAnonymous ? (
											<span className="text-gray-500 italic">Anonymous</span>
										) : observation.reporterName ? (
											observation.reporterName
										) : (
											<span className="text-gray-500 italic">Not provided</span>
										)}
									</div>
									<StatusBadge status={observation.status} />
								</td>

								{/* Actions */}
								<td className="px-6 py-4">
									<button
										onClick={() => handleViewObservation(observation)}
										className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
										style={{ borderRadius: '5px' }}
									>
										View
									</button>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredObservationsAll.length === 0 && (
					<div className="p-12 text-center">
						<MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No observations found
						</h3>
						<p className="text-gray-500">
							{searchQuery || selectedObservationType !== "all"
								? "Try adjusting your filters to see more results."
								: "No safety observations have been logged yet."}
						</p>
					</div>
				)}

				{/* Pagination */}
				{filteredObservationsAll.length > 0 && (
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						totalItems={filteredObservationsAll.length}
						itemsPerPage={itemsPerPage}
						onPageChange={handlePageChange}
					/>
				)}
			</div>

			{/* Observation Details Modal */}
			{selectedObservation && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
					<div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
						{/* Modal Header */}
						<div className="flex items-center justify-between p-6 border-b border-gray-200">
							<h3 className="text-lg font-semibold text-gray-900">Observation Details</h3>
							<button
								onClick={handleCloseModal}
								className="text-gray-400 hover:text-gray-600 transition-colors"
								disabled={isSaving}
							>
								<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
								</svg>
							</button>
						</div>

						{/* Modal Content */}
						<div className="p-6 space-y-6">
							{/* Read-only Observation Information */}
							<div className="space-y-4">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Observation ID
										</label>
										<p className="text-sm text-gray-900 font-mono">
											{selectedObservation.id.slice(-6)}
										</p>
									</div>
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Date & Time
										</label>
										<p className="text-sm text-gray-900">
											{new Date(selectedObservation.dateOfObservation).toLocaleDateString()} at {selectedObservation.timeOfObservation}
										</p>
									</div>
								</div>

								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Location
									</label>
									<p className="text-sm text-gray-900">
										{selectedObservation.siteArea ? `${selectedObservation.siteArea} - ${selectedObservation.locationOnSite}` : selectedObservation.locationOnSite}
									</p>
								</div>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Type
										</label>
										<div className="flex items-center space-x-2">
											{mapObservationType(selectedObservation.observationType) === 'positive' ? (
												<ThumbsUp className="h-4 w-4 text-green-500" />
											) : mapObservationType(selectedObservation.observationType) === 'negative' ? (
												<AlertTriangle className="h-4 w-4 text-red-500" />
											) : mapObservationType(selectedObservation.observationType) === 'alert' ? (
												<Shield className="h-4 w-4 text-orange-500" />
											) : (
												<Lightbulb className="h-4 w-4 text-blue-500" />
											)}
											<ObservationTypeBadge type={mapObservationType(selectedObservation.observationType)} />
										</div>
									</div>
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Category
										</label>
										<p className="text-sm text-gray-900 capitalize">
											{selectedObservation.category?.replace('-', ' ') || 'General'}
										</p>
									</div>
								</div>

								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Description
									</label>
									<p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
										{selectedObservation.description}
									</p>
								</div>

								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Reporter
									</label>
									<p className="text-sm text-gray-900">
										{selectedObservation.isAnonymous ? (
											<span className="text-gray-500 italic">Anonymous</span>
										) : selectedObservation.reporterName ? (
											selectedObservation.reporterName
										) : (
											<span className="text-gray-500 italic">Not provided</span>
										)}
									</p>
								</div>

								{selectedObservation.actionTakenImmediate && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Immediate Action Taken
										</label>
										<p className="text-sm text-gray-900 bg-green-50 p-3 rounded-md">
											{selectedObservation.actionTakenImmediate}
										</p>
									</div>
								)}

								{selectedObservation.recommendedAction && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Recommended Action
										</label>
										<p className="text-sm text-gray-900 bg-blue-50 p-3 rounded-md">
											{selectedObservation.recommendedAction}
										</p>
									</div>
								)}

								{selectedObservation.photoUrls && selectedObservation.photoUrls.length > 0 && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Photos ({selectedObservation.photoUrls.length})
										</label>
										<div className="grid grid-cols-2 gap-2">
											{selectedObservation.photoUrls.map((_, index) => (
												<div key={index} className="aspect-square bg-gray-100 rounded-md flex items-center justify-center">
													<div className="text-center">
														<svg className="h-8 w-8 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
														</svg>
														<span className="text-xs text-gray-500">Photo {index + 1}</span>
													</div>
												</div>
											))}
										</div>
									</div>
								)}
							</div>

							{/* Interactive Section */}
							<div className="border-t pt-6 space-y-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Review Notes
									</label>
									<textarea
										value={modalNotes}
										onChange={(e) => setModalNotes(e.target.value)}
										rows={4}
										className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 resize-none"
										placeholder="Add notes about this observation, follow-up actions, or review comments..."
										disabled={isSaving}
									/>
								</div>

								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Status
									</label>
									<select
										value={modalStatus}
										onChange={(e) => setModalStatus(e.target.value)}
										className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
										disabled={isSaving}
									>
										<option value="open">Open</option>
										<option value="under-review">Under Review</option>
										<option value="assigned">Assigned</option>
										<option value="in-progress">In Progress</option>
										<option value="completed">Completed</option>
										<option value="closed">Closed</option>
									</select>
								</div>
							</div>
						</div>

						{/* Modal Footer */}
						<div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
							<button
								onClick={handleCloseModal}
								className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
								disabled={isSaving}
							>
								Cancel
							</button>
							<button
								onClick={handleSaveObservation}
								className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
								disabled={isSaving}
							>
								{isSaving && (
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
								)}
								<span>{isSaving ? 'Saving...' : 'Save'}</span>
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default SafetyObservations;
