import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { ListChe<PERSON>, Eye, Clock, MapPin, Users, CheckCircle, Calendar, User } from 'lucide-react';
import { useSafetyData } from './hooks/useSafetyData';
import { ToolboxTalk } from './types/safety';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface ToolboxTalksProps {
	siteId: string;
}

// Badge components matching the SafetyObservations style
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "completed":
				return { className: "bg-green-100 text-green-800" };
			case "cancelled":
				return { className: "bg-red-100 text-red-800" };
			case "in-progress":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "scheduled":
				return { className: "bg-blue-100 text-blue-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}>
			{status.replace("-", " ")}
		</span>
	);
};



const ToolboxTalks: React.FC<ToolboxTalksProps> = ({ siteId }) => {
	const navigate = useNavigate();
	const { data: toolboxTalks, isLoading } = useSafetyData(siteId, "toolbox-talks");

	// State for search and filtering (matching SafetyObservations pattern)
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedStatus, setSelectedStatus] = useState("all");

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 20;

	// Status filters
	const statusFilters: TagOption[] = [
		{ id: "completed", name: "Completed" },
		{ id: "cancelled", name: "Cancelled" },
		{ id: "in-progress", name: "In Progress" },
		{ id: "scheduled", name: "Scheduled" }
	];

	const handleStartToolbox = () => {
		// Navigate to the toolbox fill page using React Router
		navigate(`/sites/${siteId}/toolbox/fill`);
	};

	const handleViewToolbox = (toolbox: ToolboxTalk) => {
		console.log("View toolbox:", toolbox.id);
		// TODO: Navigate to toolbox details page
	};

	// Filtering logic (matching SafetyObservations pattern)
	const filteredToolboxTalksAll = useMemo(() => {
		if (!toolboxTalks) return [];

		return toolboxTalks.filter((toolbox: ToolboxTalk) => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				toolbox.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
				toolbox.supervisorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				toolbox.id.toLowerCase().includes(searchQuery.toLowerCase());

			// Status filter
			const matchesStatus = selectedStatus === "all" ||
				toolbox.status === selectedStatus;

			return matchesSearch && matchesStatus;
		});
	}, [toolboxTalks, searchQuery, selectedStatus]);

	// Paginated results
	const filteredToolboxTalks = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredToolboxTalksAll.slice(startIndex, endIndex);
	}, [filteredToolboxTalksAll, currentPage, itemsPerPage]);

	// Pagination calculations
	const totalPages = Math.ceil(filteredToolboxTalksAll.length / itemsPerPage);

	// Handlers for search and filtering
	const handleSearchChange = (query: string) => {
		setSearchQuery(query);
		setCurrentPage(1);
	};

	const handleStatusChange = (status: string) => {
		setSelectedStatus(status);
		setCurrentPage(1);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-center py-12">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
						<p className="mt-2 text-sm text-gray-500">Loading toolbox talks...</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header - matching SafetyObservations style */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Toolbox Talks</h2>
					<p className="text-sm text-gray-600">Manage and track safety toolbox talks on site</p>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						Total {filteredToolboxTalksAll.length} toolbox talks
					</div>
					<button
						onClick={handleStartToolbox}
						className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
					>
						<ListChecks className="h-4 w-4" />
						<span>Start Toolbox</span>
					</button>
				</div>
			</div>

			{/* Search and Filter - matching SafetyObservations style */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={handleSearchChange}
				searchPlaceholder="Search by topic, conductor, or ID..."
				tags={[{ id: "all", name: "All" }, ...statusFilters]}
				selectedTagId={selectedStatus}
				onTagChange={handleStatusChange}
			/>

			{/* Toolbox Talks Table - matching SafetyObservations exact styling */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Date & Time
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Conductor
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Attendees
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Topic
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredToolboxTalks.map((toolbox: ToolboxTalk) => (
							<tr key={toolbox.id} className="hover:bg-gray-50 transition-colors">
								{/* Date & Time */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center space-x-2 mb-1">
											<Calendar className="h-4 w-4 text-gray-400" />
											<span className="text-sm font-medium text-gray-900">
												{new Date(toolbox.date).toLocaleDateString()}
											</span>
										</div>
										<div className="flex items-center text-xs text-gray-500">
											<Clock className="h-3 w-3 mr-1" />
											{toolbox.timeStarted}
										</div>
									</div>
								</td>

								{/* Conductor */}
								<td className="px-6 py-4">
									<div className="flex items-center">
										<User className="h-4 w-4 text-blue-500 mr-2" />
										<div>
											<div className="text-sm font-medium text-gray-900">
												{toolbox.supervisorName}
											</div>
											<div className="text-xs text-gray-500">
												Supervisor
											</div>
										</div>
									</div>
								</td>

								{/* Attendees */}
								<td className="px-6 py-4">
									<div className="flex items-center">
										<Users className="h-4 w-4 text-green-500 mr-2" />
										<div>
											<div className="text-sm font-medium text-gray-900">
												{toolbox.attendees?.filter(a => a.attended).length || 0}/{toolbox.attendees?.length || 0} attended
											</div>
											<div className="text-xs text-gray-500">
												{toolbox.attendees?.length ?
													Math.round(((toolbox.attendees.filter(a => a.attended).length || 0) / toolbox.attendees.length) * 100) : 0}% attendance
											</div>
										</div>
									</div>
								</td>

								{/* Topic */}
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900 line-clamp-2">
										{toolbox.topic}
									</div>
								</td>

								{/* Status */}
								<td className="px-6 py-4">
									<StatusBadge status={toolbox.status} />
								</td>

								{/* Actions */}
								<td className="px-6 py-4">
									<button
										onClick={() => handleViewToolbox(toolbox)}
										className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
										style={{ borderRadius: '5px' }}
									>
										View
									</button>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredToolboxTalksAll.length === 0 && (
					<div className="p-12 text-center">
						<ListChecks className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No toolbox talks found
						</h3>
						<p className="text-gray-500">
							{searchQuery || selectedStatus !== "all"
								? "Try adjusting your filters to see more results."
								: "No toolbox talks have been conducted yet."}
						</p>
					</div>
				)}

				{/* Pagination */}
				{filteredToolboxTalksAll.length > 0 && (
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						totalItems={filteredToolboxTalksAll.length}
						itemsPerPage={itemsPerPage}
						onPageChange={handlePageChange}
					/>
				)}
			</div>
		</div>
	);
};

export default ToolboxTalks;
