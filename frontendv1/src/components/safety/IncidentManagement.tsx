import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>, Edit, Clock, MapPin } from 'lucide-react';
import { useSafetyData } from './hooks/useSafetyData';
import { Incident, IncidentType, IncidentSeverity } from './types/safety';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface IncidentManagementProps {
	siteId: string;
}

// Badge components matching the permit dashboard style
const IncidentTypeBadge: React.FC<{ type: IncidentType }> = ({ type }) => {
	const getTypeConfig = () => {
		switch (type) {
			case "accident-with-injury":
				return { label: "Accident w/ Injury", className: "bg-red-100 text-red-800" };
			case "near-miss":
				return { label: "Near Miss", className: "bg-yellow-100 text-yellow-800" };
			case "property-damage":
				return { label: "Property Damage", className: "bg-orange-100 text-orange-800" };
			case "environmental":
				return { label: "Environmental", className: "bg-green-100 text-green-800" };
			case "first-aid-only":
				return { label: "First Aid Only", className: "bg-blue-100 text-blue-800" };
			default:
				return { label: type, className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getTypeConfig();
	return (
		<span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
			{config.label}
		</span>
	);
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "reported":
				return { className: "bg-blue-100 text-blue-800" };
			case "investigating":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "pending-capa":
				return { className: "bg-orange-100 text-orange-800" };
			case "closed":
				return { className: "bg-green-100 text-green-800" };
			case "draft":
				return { className: "bg-gray-100 text-gray-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}>
			{status.replace("-", " ")}
		</span>
	);
};

const IncidentManagement: React.FC<IncidentManagementProps> = ({ siteId }) => {
	const { data: incidents, isLoading } = useSafetyData(siteId, "incidents");

	// State for search and filtering (matching ActivePermits pattern)
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedIncidentType, setSelectedIncidentType] = useState("all");

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 20;

	// Incident type filters
	const incidentTypeFilters: TagOption[] = [
		{ id: "accident-with-injury", name: "Accident w/ Injury" },
		{ id: "near-miss", name: "Near Miss" },
		{ id: "property-damage", name: "Property Damage" },
		{ id: "environmental", name: "Environmental" },
		{ id: "first-aid-only", name: "First Aid Only" }
	];

	const handleReportIncident = () => {
		console.log("Report incident clicked");
		// TODO: Navigate to incident reporting form
	};

	const handleViewIncident = (incident: Incident) => {
		console.log("View incident:", incident.id);
		// TODO: Navigate to incident details page
	};

	const handleEditIncident = (incident: Incident) => {
		console.log("Edit incident:", incident.id);
		// TODO: Navigate to incident edit form
	};

	// Filtering logic (matching ActivePermits pattern)
	const filteredIncidentsAll = useMemo(() => {
		if (!incidents) return [];

		return incidents.filter((incident: Incident) => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				incident.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
				incident.locationOnSite.toLowerCase().includes(searchQuery.toLowerCase()) ||
				incident.reportedByName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
				incident.id.toLowerCase().includes(searchQuery.toLowerCase());

			// Incident type filter
			const matchesIncidentType = selectedIncidentType === "all" ||
				incident.incidentType === selectedIncidentType;

			return matchesSearch && matchesIncidentType;
		});
	}, [incidents, searchQuery, selectedIncidentType]);

	// Paginated results
	const filteredIncidents = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredIncidentsAll.slice(startIndex, endIndex);
	}, [filteredIncidentsAll, currentPage, itemsPerPage]);

	// Pagination calculations
	const totalPages = Math.ceil(filteredIncidentsAll.length / itemsPerPage);

	// Handlers for search and filtering
	const handleSearchChange = (query: string) => {
		setSearchQuery(query);
		setCurrentPage(1);
	};

	const handleIncidentTypeChange = (type: string) => {
		setSelectedIncidentType(type);
		setCurrentPage(1);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-center py-12">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
						<p className="mt-2 text-sm text-gray-500">Loading incidents...</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header - matching ActivePermits style */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Incidents</h2>
					<p className="text-sm text-gray-600">Manage and track safety incidents on site</p>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						Total {filteredIncidentsAll.length} incidents
					</div>
					<button
						onClick={handleReportIncident}
						className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center space-x-2"
					>
						<AlertTriangle className="h-4 w-4" />
						<span>Report Incident</span>
					</button>
				</div>
			</div>

			{/* Search and Filter - matching ActivePermits style */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={handleSearchChange}
				searchPlaceholder="Search by incident description, location, reporter, or ID..."
				tags={[{ id: "all", name: "All" }, ...incidentTypeFilters]}
				selectedTagId={selectedIncidentType}
				onTagChange={handleIncidentTypeChange}
			/>

			{/* Incidents Table - matching ActivePermits exact styling */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Incident Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Type
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Description
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredIncidents.map((incident: Incident) => (
							<tr key={incident.id} className="hover:bg-gray-50 transition-colors">
								{/* Incident Details */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center space-x-2 mb-1">
											<h4 className="text-sm font-medium text-gray-900">
												ID: {incident.id.slice(-6)}
											</h4>
										</div>
										<div className="flex items-center text-xs text-gray-600 mb-1">
											<Clock className="h-3 w-3 mr-1" />
											{new Date(incident.dateOfIncident).toLocaleDateString()} at {incident.timeOfIncident}
										</div>
										<div className="flex items-center text-xs text-gray-500">
											<MapPin className="h-3 w-3 mr-1" />
											{incident.locationOnSite}
										</div>
									</div>
								</td>

								{/* Type */}
								<td className="px-6 py-4">
									<div className="flex items-center">
										<AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
										<div>
											<div className="text-sm font-medium text-gray-900">
												<IncidentTypeBadge type={incident.incidentType} />
											</div>
											<div className="text-xs text-gray-500 mt-1">
												Reported by: {incident.reportedByName || 'Unknown'}
											</div>
										</div>
									</div>
								</td>

								{/* Description */}
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900 line-clamp-2">
										{incident.description}
									</div>
								</td>

								{/* Status */}
								<td className="px-6 py-4">
									<StatusBadge status={incident.status} />
								</td>

								{/* Actions */}
								<td className="px-6 py-4">
									<div className="flex space-x-2">
										<button
											onClick={() => handleViewIncident(incident)}
											className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
											style={{ borderRadius: '5px' }}
										>
											View
										</button>
										<button
											onClick={() => handleEditIncident(incident)}
											className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors flex items-center"
											style={{ borderRadius: '5px' }}
										>
											<Edit className="h-3 w-3" />
										</button>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredIncidentsAll.length === 0 && (
					<div className="p-12 text-center">
						<AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No incidents found
						</h3>
						<p className="text-gray-500">
							{searchQuery || selectedIncidentType !== "all"
								? "Try adjusting your filters to see more results."
								: "No incidents have been reported yet."}
						</p>
					</div>
				)}

				{/* Pagination */}
				{filteredIncidentsAll.length > 0 && (
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						totalItems={filteredIncidentsAll.length}
						itemsPerPage={itemsPerPage}
						onPageChange={handlePageChange}
					/>
				)}
			</div>
		</div>
	);
};

export default IncidentManagement;
