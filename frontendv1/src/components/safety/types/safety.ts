// Safety KPI and Dashboard Types
export interface SafetyKPI {
	title: string;
	value: string | number;
	trend?: "up" | "down" | "stable";
	status?: "good" | "warning" | "danger";
	change?: number;
}

export interface SafetyDashboardData {
	totalIncidents: number;
	lostTimeIncidents: number;
	toolboxAttendance: number;
	overdueActions: number;
	incidentTrend: "up" | "down" | "stable";
	incidentTrends: SafetyTrendData[];
	recentActivities: SafetyActivity[];
	upcomingDeadlines: SafetyDeadline[];
}

export interface SafetyTrendData {
	date: string;
	incidents: number;
	observations: number;
	toolboxTalks: number;
}

export interface SafetyActivity {
	id: string;
	type: "incident" | "observation" | "toolbox-talk" | "capa";
	title: string;
	description: string;
	timestamp: string;
	severity?: IncidentSeverity;
	status?: string;
}

export interface SafetyDeadline {
	id: string;
	type: "capa" | "rams-expiry" | "training-renewal";
	title: string;
	dueDate: string;
	priority: "low" | "medium" | "high";
	assignedTo?: string;
}

// Toolbox Talk Types - Legacy interface for compatibility
// Note: New implementations should use ToolboxSession from main types
export interface ToolboxTalk {
	id: string;
	siteId: string;
	date: string;
	timeStarted: string;
	topic: string;
	supervisorId: string;
	supervisorName: string;
	attendees: ToolboxTalkAttendee[];
	supportingDocuments?: SafetyDocument[];
	status: "scheduled" | "in-progress" | "completed" | "cancelled";
	createdAt: string;
	updatedAt: string;
}

export interface ToolboxTalkAttendee {
	workerId: string;
	workerName: string;
	attended: boolean;
	attendedAt?: string;
	remark?: string;
	lateArrival?: boolean;
}

// Backend-aligned Toolbox Session Types
export interface BackendToolboxSession {
  id: number;
  sessionTime: string; // ISO DateTime string
  topic: string;
  conductor: string;
  photoUrl?: string;
  notes?: string;
  attendances: BackendToolboxAttendance[];
  // Audit fields
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface BackendToolboxAttendance {
  id: number;
  toolboxSessionId: number;
  workerId: number;
  wasPresent: boolean;
  notes?: string;
  // Audit fields
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

// Incident Types
export type IncidentType =
	| "accident-with-injury"
	| "near-miss"
	| "property-damage"
	| "environmental"
	| "first-aid-only";
export type IncidentSeverity = "low" | "medium" | "high" | "critical";
export type IncidentStatus =
	| "reported"
	| "investigating"
	| "pending-capa"
	| "closed"
	| "draft";

export interface Incident {
	id: string;
	siteId: string;
	reportedByUserId: string;
	reportedByName: string;
	dateOfIncident: string;
	timeOfIncident: string;
	locationOnSite: string;
	incidentType: IncidentType;
	severity: IncidentSeverity;
	description: string;
	involvedPersons: IncidentPerson[];
	witnesses?: IncidentWitness[];
	equipmentInvolved?: IncidentEquipment[];
	photos?: SafetyDocument[];
	immediateActionsTaken?: string;
	status: IncidentStatus;
	investigationDetails?: InvestigationDetails;
	capas?: CAPA[];
	createdAt: string;
	updatedAt: string;
}

export interface IncidentPerson {
	workerId?: string;
	name: string;
	type: "worker" | "external" | "visitor";
	injuryType?: string;
	medicalAttentionRequired?: boolean;
}

export interface IncidentWitness {
	name: string;
	contact?: string;
	statement?: string;
}

export interface IncidentEquipment {
	equipmentId?: string;
	name: string;
	type: string;
	damageDescription?: string;
}

export interface InvestigationDetails {
	investigatorId: string;
	investigatorName: string;
	investigationDate: string;
	findings: string;
	contributingFactors: string[];
	rootCause: string;
	lessonsLearned?: string;
	preventiveMeasures?: string[];
}

export interface CAPA {
	id: string;
	description: string;
	assignedToUserId: string;
	assignedToName: string;
	dueDate: string;
	priority: "low" | "medium" | "high";
	status: "open" | "in-progress" | "completed" | "verified" | "cancelled";
	completionDate?: string;
	verificationNotes?: string;
	createdAt: string;
	updatedAt: string;
}

// Safety Observation Types
export type ObservationType =
	| "safe-behavior"
	| "safe-condition"
	| "at-risk-behavior"
	| "at-risk-condition";
export type ObservationCategory =
	| "ppe"
	| "housekeeping"
	| "equipment-use"
	| "procedure"
	| "other";

export interface SafetyObservation {
	id: string;
	siteId: string;
	reportedByUserId?: string;
	reporterName?: string;
	dateOfObservation: string;
	timeOfObservation: string;
	siteArea?: string;
	locationOnSite: string;
	observationType: ObservationType;
	category: ObservationCategory;
	description: string;
	actionTakenImmediate?: string;
	recommendedAction?: string;
	photoUrls?: string[];
	isAnonymous: boolean;
	status: "open" | "under-review" | "assigned" | "in-progress" | "completed" | "closed";
	priority: "low" | "medium" | "high" | "critical";
	// Review and Assignment
	reviewedBy?: string;
	reviewedAt?: string;
	reviewNotes?: string;
	assignedTo?: string;
	assignedToName?: string;
	assignedAt?: string;
	assignedBy?: string;
	// Action Tracking
	actionTaken?: string;
	actionCompletedAt?: string;
	actionCompletedBy?: string;
	// Follow-up
	followUpRequired: boolean;
	followUpDate?: string;
	followUpNotes?: string;
	// Verification
	verifiedBy?: string;
	verifiedAt?: string;
	verificationNotes?: string;
	// Metadata
	createdAt: string;
	updatedAt?: string;
}

// Site RAMS Types
export interface SiteRAMS {
	id: string;
	siteId: string;
	title: string;
	version: string;
	effectiveDate: string;
	expiryDate?: string;
	sourceType: "upload" | "master-link";
	documentUrl?: string;
	masterRamsId?: string;
	status: "active" | "archived" | "expired";
	uploadedByUserId: string;
	uploadedByName: string;
	description?: string;
	applicableActivities: string[];
	createdAt: string;
	updatedAt: string;
}

// Supporting Types
export interface SafetyDocument {
	id: string;
	name: string;
	url: string;
	type: string;
	size: number;
	uploadedAt: string;
}

// Worker Safety Status for Integration
export interface WorkerSafetyStatus {
	workerId: string;
	workerName: string;
	toolboxCompleteToday: boolean;
	requiredTrainingComplete: boolean;
	medicalClearance: boolean;
	eligibleForWork: boolean;
	restrictions?: string[];
	lastToolboxDate?: string;
	lastMedicalCheckDate?: string;
}

// Observation Management Types
export interface ObservationAssignment {
	observationId: string;
	assignedToUserId: string;
	assignedToName: string;
	assignedByUserId: string;
	assignedByName: string;
	assignedAt: string;
	dueDate?: string;
	priority: "low" | "medium" | "high" | "critical";
	notes?: string;
	status: "pending" | "accepted" | "in-progress" | "completed" | "overdue";
}

export interface ObservationAction {
	id: string;
	observationId: string;
	actionType: "review" | "assign" | "update" | "complete" | "verify" | "close";
	description: string;
	performedBy: string;
	performedByName: string;
	performedAt: string;
	notes?: string;
	attachments?: string[];
}

export interface ObservationReview {
	observationId: string;
	reviewedBy: string;
	reviewedByName: string;
	reviewedAt: string;
	priority: "low" | "medium" | "high" | "critical";
	requiresAction: boolean;
	recommendedActions: string[];
	assignToUserId?: string;
	assignToName?: string;
	dueDate?: string;
	reviewNotes: string;
}

// Filter Types
export interface SafetyFilters {
	severity?: string;
	status?: string;
	type?: string;
	observationType?: string;
	category?: string;
	priority?: string;
	assignedTo?: string;
	dateRange?: {
		start: Date;
		end: Date;
	};
}

// Notification Types
export interface SafetyNotification {
	id: string;
	type: "incident" | "capa-overdue" | "rams-expiry" | "toolbox-missed";
	title: string;
	message: string;
	severity: "low" | "medium" | "high" | "critical";
	timestamp: string;
	read: boolean;
}
