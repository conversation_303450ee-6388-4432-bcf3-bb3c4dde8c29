import React, { useState } from 'react';
import {  Download, DollarSign, Clock, FileText } from 'lucide-react';

interface PayrollRecord {
  id: string;
  payPeriod: string;
  regularHours: number;
  overtimeHours: number;
  totalHours: number;
  grossPay: number;
  deductions: {
    paye: number;
    nssf: number;
    shif: number;
    housingLevy: number;
    ppeDeductions: number;
    other: number;
  };
  netPay: number;
  status: 'draft' | 'processed' | 'paid';
  payDate?: string;
}

interface PayrollDeduction {
  id: string;
  date: string;
  type: 'ppe' | 'advance' | 'loan' | 'other';
  description: string;
  amount: number;
  status: 'pending' | 'applied' | 'reversed';
}

interface PayrollTabProps {
  workerId: string;
  workerName: string;
}

const PayrollTab: React.FC<PayrollTabProps> = ({  }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [activeSection, setActiveSection] = useState<'records' | 'deductions' | 'summary'>('records');

  // Mock payroll data
  const mockPayrollRecords: PayrollRecord[] = [
    {
      id: 'pr1',
      payPeriod: 'January 2025',
      regularHours: 176,
      overtimeHours: 12,
      totalHours: 188,
      grossPay: 85000,
      deductions: {
        paye: 12500,
        nssf: 2160,
        shif: 2337.5,
        housingLevy: 1275,
        ppeDeductions: 1500,
        other: 0
      },
      netPay: 65227.5,
      status: 'processed',
      payDate: '2025-01-31'
    },
    {
      id: 'pr2',
      payPeriod: 'December 2024',
      regularHours: 168,
      overtimeHours: 8,
      totalHours: 176,
      grossPay: 80000,
      deductions: {
        paye: 11000,
        nssf: 2160,
        shif: 2200,
        housingLevy: 1200,
        ppeDeductions: 0,
        other: 500
      },
      netPay: 62940,
      status: 'paid',
      payDate: '2024-12-31'
    }
  ];

  const mockDeductions: PayrollDeduction[] = [
    {
      id: 'pd1',
      date: '2025-01-15',
      type: 'ppe',
      description: 'Lost Safety Helmet - Class G',
      amount: 1500,
      status: 'applied'
    },
    {
      id: 'pd2',
      date: '2024-12-20',
      type: 'other',
      description: 'Uniform replacement',
      amount: 500,
      status: 'applied'
    }
  ];

  const currentRecord = mockPayrollRecords[0];
  const totalDeductions = Object.values(currentRecord.deductions).reduce((sum, amount) => sum + amount, 0);

  return (
    <div className="space-y-6">
      {/* Section Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveSection('records')}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeSection === 'records'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Payroll Records
        </button>
        <button
          onClick={() => setActiveSection('deductions')}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeSection === 'deductions'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Deductions
        </button>
        <button
          onClick={() => setActiveSection('summary')}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeSection === 'summary'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Summary
        </button>
      </div>

      {/* Payroll Records Section */}
      {activeSection === 'records' && (
        <div className="space-y-6">
          {/* Period Selector */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="border border-gray-300 rounded-md text-sm p-2"
              >
                <option value="current-month">Current Month</option>
                <option value="last-month">Last Month</option>
                <option value="last-3-months">Last 3 Months</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>
            <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
              <Download className="h-4 w-4 mr-2" />
              Export Payslip
            </button>
          </div>

          {/* Current Payroll Summary */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">Current Pay Period - {currentRecord.payPeriod}</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Hours Summary */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                    Hours Worked
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Regular Hours:</span>
                      <span className="text-sm font-medium">{currentRecord.regularHours}h</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Overtime Hours:</span>
                      <span className="text-sm font-medium">{currentRecord.overtimeHours}h</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-medium">Total Hours:</span>
                      <span className="text-sm font-bold">{currentRecord.totalHours}h</span>
                    </div>
                  </div>
                </div>

                {/* Earnings */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                    Earnings
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Gross Pay:</span>
                      <span className="text-sm font-medium">KES {currentRecord.grossPay.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Deductions:</span>
                      <span className="text-sm font-medium text-red-600">-KES {totalDeductions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-medium">Net Pay:</span>
                      <span className="text-sm font-bold text-green-600">KES {currentRecord.netPay.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Status */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-purple-500" />
                    Status
                  </h4>
                  <div className="space-y-2">
                    <div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        currentRecord.status === 'paid' ? 'bg-green-100 text-green-800' :
                        currentRecord.status === 'processed' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {currentRecord.status.charAt(0).toUpperCase() + currentRecord.status.slice(1)}
                      </span>
                    </div>
                    {currentRecord.payDate && (
                      <div className="text-sm text-gray-600">
                        Pay Date: {new Date(currentRecord.payDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payroll History */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">Payroll History</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pay Period
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hours
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gross Pay
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Deductions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Net Pay
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mockPayrollRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {record.payPeriod}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {record.totalHours}h ({record.overtimeHours}h OT)
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        KES {record.grossPay.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                        -KES {Object.values(record.deductions).reduce((sum, amount) => sum + amount, 0).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        KES {record.netPay.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          record.status === 'paid' ? 'bg-green-100 text-green-800' :
                          record.status === 'processed' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Deductions Section */}
      {activeSection === 'deductions' && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">Payroll Deductions</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockDeductions.map((deduction) => (
                  <tr key={deduction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(deduction.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        deduction.type === 'ppe' ? 'bg-red-100 text-red-800' :
                        deduction.type === 'advance' ? 'bg-blue-100 text-blue-800' :
                        deduction.type === 'loan' ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {deduction.type.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {deduction.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                      -KES {deduction.amount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        deduction.status === 'applied' ? 'bg-green-100 text-green-800' :
                        deduction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {deduction.status.charAt(0).toUpperCase() + deduction.status.slice(1)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Summary Section */}
      {activeSection === 'summary' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* YTD Summary */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Year to Date Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Hours Worked:</span>
                <span className="text-sm font-medium">364h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Overtime:</span>
                <span className="text-sm font-medium">20h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Gross Earnings:</span>
                <span className="text-sm font-medium">KES 165,000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Deductions:</span>
                <span className="text-sm font-medium text-red-600">-KES 36,772.5</span>
              </div>
              <div className="flex justify-between border-t pt-3">
                <span className="text-sm font-medium">Net Earnings:</span>
                <span className="text-sm font-bold text-green-600">KES 128,227.5</span>
              </div>
            </div>
          </div>

          {/* Deduction Breakdown */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Deduction Breakdown (YTD)</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">PAYE Tax:</span>
                <span className="text-sm font-medium">KES 23,500</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">NSSF:</span>
                <span className="text-sm font-medium">KES 4,320</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">SHIF:</span>
                <span className="text-sm font-medium">KES 4,537.5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Housing Levy:</span>
                <span className="text-sm font-medium">KES 2,475</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">PPE Deductions:</span>
                <span className="text-sm font-medium">KES 1,500</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Other:</span>
                <span className="text-sm font-medium">KES 500</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PayrollTab;
