import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { Filter, Search, Eye, Calendar, User, CheckCircle, XCircle, Clock } from 'lucide-react';
import { GET_INSPECTIONS } from '../../graphql/queries';

interface InspectionsListProps {
  siteId: string;
  onInspectionClick: (inspectionId: number) => void;
}

interface Inspection {
  id: number;
  approved: boolean;
  comments: string;
  inspectionType: string;
  inspectedBy: {
    id: number;
    name: string;
  };
  inspectionItems: Array<{
    id: number;
    description: string;
    isTrue: boolean;
    remarks: string;
    imageFiles: Array<{
      id: number;
      fileName: string;
      url: string;
    }>;
  }>;
  createdAt: string;
  createdBy: string;
}

const InspectionsList: React.FC<InspectionsListProps> = ({
  siteId: _siteId,
  onInspectionClick,
}) => {
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'type' | 'status'>('date');

  const { data, loading, error, refetch } = useQuery(GET_INSPECTIONS, {
    variables: { skip: 0, take: 50 },
    fetchPolicy: 'cache-and-network',
  });

  const inspections: Inspection[] = data?.inspections || [];

  // Filter and sort inspections
  const filteredInspections = inspections
    .filter(inspection => {
      if (filterType !== 'all' && inspection.inspectionType !== filterType) {
        return false;
      }
      if (searchTerm && !inspection.inspectedBy.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !inspection.inspectionType.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'type':
          return a.inspectionType.localeCompare(b.inspectionType);
        case 'status':
          return Number(b.approved) - Number(a.approved);
        default:
          return 0;
      }
    });

  const getStatusIcon = (approved: boolean) => {
    return approved ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  const getStatusText = (approved: boolean) => {
    return approved ? 'Approved' : 'Failed';
  };

  const getStatusColor = (approved: boolean) => {
    return approved
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800';
  };

  const formatInspectionType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUniqueInspectionTypes = () => {
    const types = [...new Set(inspections.map(i => i.inspectionType))];
    return types.sort();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading inspections</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">All Inspections</h3>
            <p className="text-sm text-gray-600 mt-1">
              {filteredInspections.length} of {inspections.length} inspections
            </p>
          </div>
          <button
            onClick={() => refetch()}
            className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Refresh
          </button>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by inspector or type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            />
          </div>

          {/* Type Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 appearance-none bg-white"
            >
              <option value="all">All Types</option>
              {getUniqueInspectionTypes().map(type => (
                <option key={type} value={type}>
                  {formatInspectionType(type)}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'date' | 'type' | 'status')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          >
            <option value="date">Sort by Date</option>
            <option value="type">Sort by Type</option>
            <option value="status">Sort by Status</option>
          </select>
        </div>
      </div>

      {/* Inspections List */}
      <div className="divide-y divide-gray-200">
        {filteredInspections.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No inspections found</p>
            {searchTerm || filterType !== 'all' ? (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilterType('all');
                }}
                className="mt-2 text-green-600 hover:text-green-700"
              >
                Clear filters
              </button>
            ) : null}
          </div>
        ) : (
          filteredInspections.map((inspection) => (
            <div
              key={inspection.id}
              onClick={() => onInspectionClick(inspection.id)}
              className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(inspection.approved)}
                    <h4 className="text-lg font-medium text-gray-900 truncate">
                      {formatInspectionType(inspection.inspectionType)}
                    </h4>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(inspection.approved)}`}
                    >
                      {getStatusText(inspection.approved)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {inspection.inspectedBy.name}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(inspection.createdAt)}
                    </div>
                  </div>

                  {inspection.comments && (
                    <p className="text-sm text-gray-600 truncate">
                      {inspection.comments}
                    </p>
                  )}

                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span>{inspection.inspectionItems.length} items</span>
                    <span>
                      {inspection.inspectionItems.reduce((acc, item) => acc + item.imageFiles.length, 0)} photos
                    </span>
                  </div>
                </div>

                <div className="flex-shrink-0 ml-4">
                  <Eye className="h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default InspectionsList;
