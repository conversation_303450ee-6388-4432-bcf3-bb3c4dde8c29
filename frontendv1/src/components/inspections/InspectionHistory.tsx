import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
	Calendar,
	CheckCircle,
	XCircle,
	AlertTriangle,
} from "lucide-react";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";
import Pagination from "../shared/Pagination";

interface InspectionHistoryProps {
	siteId: string;
}

interface CompletedInspection {
	id: string;
	templateName: string;
	target: string;
	inspector: string;
	completedAt: string;
	status: "passed" | "failed" | "in-progress";
	criticalIssues: number;
	totalItems: number;
	passedItems: number;
	failedItems: number;
	duration: string;
	inspectionType: string;
	location: string;
}

const InspectionHistory: React.FC<InspectionHistoryProps> = ({ siteId: _siteId }) => {
	const navigate = useNavigate();

	// Mock data - replace with actual API call
	const completedInspections: CompletedInspection[] = [
		{
			id: "1",
			templateName: "Daily Site Safety Inspection",
			target: "Main Construction Area",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 14:30",
			status: "passed",
			criticalIssues: 0,
			totalItems: 20,
			passedItems: 20,
			failedItems: 0,
			duration: "45m",
			inspectionType: "Safety",
			location: "Building A",
		},
		{
			id: "2",
			templateName: "Equipment Pre-Use Check",
			target: "Excavator CAT-001",
			inspector: "Mary Wanjiku",
			completedAt: "2024-01-15 11:15",
			status: "failed",
			criticalIssues: 2,
			totalItems: 12,
			passedItems: 8,
			failedItems: 4,
			duration: "30m",
			inspectionType: "Equipment",
			location: "Site Yard",
		},
		{
			id: "3",
			templateName: "Scaffold Safety Check",
			target: "Building A - Level 3",
			inspector: "Peter Kiprotich",
			completedAt: "2024-01-15 09:45",
			status: "passed",
			criticalIssues: 0,
			totalItems: 15,
			passedItems: 15,
			failedItems: 0,
			duration: "35m",
			inspectionType: "Safety",
			location: "Building A",
		},
		{
			id: "4",
			templateName: "Fire Safety Inspection",
			target: "Site Office Building",
			inspector: "Sarah Njeri",
			completedAt: "2024-01-14 16:20",
			status: "passed",
			criticalIssues: 0,
			totalItems: 18,
			passedItems: 18,
			failedItems: 0,
			duration: "40m",
			inspectionType: "Safety",
			location: "Site Office",
		},
		{
			id: "5",
			templateName: "PPE Compliance Check",
			target: "Site Entrance Gate",
			inspector: "David Ochieng",
			completedAt: "2024-01-14 08:30",
			status: "failed",
			criticalIssues: 1,
			totalItems: 10,
			passedItems: 7,
			failedItems: 3,
			duration: "25m",
			inspectionType: "Safety",
			location: "Main Entrance",
		},
	];

	const [inspections, _setInspections] = useState<CompletedInspection[]>(completedInspections);
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedStatus, setSelectedStatus] = useState("all");
	const [dateRange, setDateRange] = useState({
		start: "",
		end: "",
	});
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 10;

	// Status filter options
	const statusFilters: TagOption[] = [
		{ id: "passed", name: "Passed" },
		{ id: "failed", name: "Failed" },
		{ id: "in-progress", name: "In Progress" },
	];

	// Pagination handlers
	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	// Filtered inspections with search, status, and date range
	const filteredInspectionsAll = useMemo(() => {
		return inspections.filter((inspection) => {
			// Search filter
			const matchesSearch = !searchQuery ||
				inspection.templateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				inspection.inspector.toLowerCase().includes(searchQuery.toLowerCase()) ||
				inspection.target.toLowerCase().includes(searchQuery.toLowerCase()) ||
				inspection.location?.toLowerCase().includes(searchQuery.toLowerCase());

			// Status filter
			const matchesStatus = selectedStatus === "all" || inspection.status === selectedStatus;

			// Date range filter
			let matchesDateRange = true;
			if (dateRange.start) {
				matchesDateRange = matchesDateRange && inspection.completedAt >= dateRange.start;
			}
			if (dateRange.end) {
				matchesDateRange = matchesDateRange && inspection.completedAt <= dateRange.end;
			}

			return matchesSearch && matchesStatus && matchesDateRange;
		});
	}, [inspections, searchQuery, selectedStatus, dateRange]);

	// Paginated results
	const filteredInspections = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredInspectionsAll.slice(startIndex, endIndex);
	}, [filteredInspectionsAll, currentPage, itemsPerPage]);

	const totalPages = Math.ceil(filteredInspectionsAll.length / itemsPerPage);

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "passed":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "failed":
				return <XCircle className="h-4 w-4 text-red-500" />;
			case "in-progress":
				return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
			default:
				return <CheckCircle className="h-4 w-4 text-gray-500" />;
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "passed":
				return "text-green-700 bg-green-50";
			case "failed":
				return "text-red-700 bg-red-50";
			case "in-progress":
				return "text-yellow-700 bg-yellow-50";
			default:
				return "text-gray-700 bg-gray-50";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "passed":
				return "Passed";
			case "failed":
				return "Failed";
			case "in-progress":
				return "In Progress";
			default:
				return "Unknown";
		}
	};

	const formatDateTime = (dateTimeString: string) => {
		const dateTime = new Date(dateTimeString);
		return {
			date: dateTime.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
				year: "numeric",
			}),
			time: dateTime.toLocaleTimeString("en-US", {
				hour: "2-digit",
				minute: "2-digit",
				hour12: false,
			}),
		};
	};

	const handleViewInspection = (inspection: CompletedInspection) => {
		// Navigate to inspection details page
		navigate(`/sites/${_siteId}/inspections/view/${inspection.id}`);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Inspection History</h2>
					<p className="text-sm text-gray-600">Historical inspection records and completed activities</p>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						Total {filteredInspectionsAll.length} inspections
					</div>
					<div className="flex items-center space-x-2">
						<Calendar className="h-4 w-4 text-gray-400" />
						<input
							type="date"
							value={dateRange.start}
							onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
							className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
							placeholder="Start date"
						/>
						<span className="text-gray-400">to</span>
						<input
							type="date"
							value={dateRange.end}
							onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
							className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
							placeholder="End date"
						/>
					</div>
				</div>
			</div>

			{/* Search and Filter */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={setSearchQuery}
				searchPlaceholder="Search by inspection name, inspector, target, or location..."
				tags={[{ id: "all", name: "All" }, ...statusFilters]}
				selectedTagId={selectedStatus}
				onTagChange={setSelectedStatus}
			/>



			{/* Inspections Table */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Inspection Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Inspector
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredInspections.map((inspection) => {
								const { date, time } = formatDateTime(inspection.completedAt);
								return (
									<tr key={inspection.id} className="hover:bg-gray-50">
										<td className="px-6 py-4">
											<div>
												<div className="text-sm font-medium text-gray-900">
													{inspection.templateName}
												</div>
												<div className="text-sm text-gray-500">
													{date} at {time} • {inspection.duration}
												</div>
												<div className="text-xs text-gray-500">
													Target: {inspection.target}
												</div>
											</div>
										</td>
										<td className="px-6 py-4">
											<div>
												<div className="text-sm font-medium text-gray-900">
													{inspection.inspector}
												</div>
												<div className="text-sm text-gray-500">Inspector</div>
											</div>
										</td>
										<td className="px-6 py-4">
											<div>
												<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
													inspection.status === 'passed' ? 'bg-green-100 text-green-800' :
													inspection.status === 'failed' ? 'bg-red-100 text-red-800' :
													'bg-yellow-100 text-yellow-800'
												}`}>
													{inspection.status === 'passed' ? 'Passed' :
													 inspection.status === 'failed' ? 'Failed' : 'In Progress'}
												</span>
												<div className="text-xs text-gray-500 mt-1">
													{inspection.passedItems}/{inspection.totalItems} items passed
												</div>
												{inspection.criticalIssues > 0 && (
													<div className="text-xs text-red-600 mt-1">
														{inspection.criticalIssues} critical issues
													</div>
												)}
											</div>
										</td>
										<td className="px-6 py-4 text-right">
											<button
												onClick={() => handleViewInspection(inspection)}
												className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
												style={{ borderRadius: '5px' }}
											>
												View
											</button>
										</td>
									</tr>
								);
							})}
						</tbody>
					</table>
				</div>

			{/* Empty State */}
			{filteredInspectionsAll.length === 0 && (
				<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm p-12 text-center" style={{ borderRadius: '5px' }}>
					<Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						No inspections found
					</h3>
					<p className="text-gray-500">
						{searchQuery || selectedStatus !== "all" || dateRange.start || dateRange.end
							? "Try adjusting your search criteria or date range."
							: "No historical inspections available for this site."}
					</p>
				</div>
			)}

		{/* Pagination */}
		{filteredInspectionsAll.length > 0 && (
			<Pagination
				currentPage={currentPage}
				totalPages={totalPages}
				totalItems={filteredInspectionsAll.length}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
			/>
		)}
	</div>
);
};

export default InspectionHistory;
