import React, { useState, useRef } from 'react';
import { Camera, Upload, X, Eye, Trash2 } from 'lucide-react';

interface PhotoCaptureProps {
  onPhotosChange: (files: File[]) => void;
  maxPhotos?: number;
  maxFileSize?: number; // in MB
  disabled?: boolean;
  className?: string;
}

interface PhotoPreview {
  file: File;
  url: string;
  id: string;
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  onPhotosChange,
  maxPhotos = 5,
  maxFileSize = 10,
  disabled = false,
  className = '',
}) => {
  const [photos, setPhotos] = useState<PhotoPreview[]>([]);
  const [isCapturing, setIsCapturing] = useState(false);
  const [previewPhoto, setPreviewPhoto] = useState<PhotoPreview | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const updatePhotos = (newPhotos: PhotoPreview[]) => {
    setPhotos(newPhotos);
    onPhotosChange(newPhotos.map(p => p.file));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (photos.length + files.length > maxPhotos) {
      alert(`Maximum ${maxPhotos} photos allowed`);
      return;
    }

    const validFiles: File[] = [];
    const maxSizeBytes = maxFileSize * 1024 * 1024;

    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        alert(`${file.name} is not an image file`);
        continue;
      }

      if (file.size > maxSizeBytes) {
        alert(`${file.name} is too large. Maximum size is ${maxFileSize}MB`);
        continue;
      }

      validFiles.push(file);
    }

    const newPhotos: PhotoPreview[] = validFiles.map(file => ({
      file,
      url: URL.createObjectURL(file),
      id: Math.random().toString(36).substr(2, 9),
    }));

    updatePhotos([...photos, ...newPhotos]);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const startCamera = async () => {
    try {
      setIsCapturing(true);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }, // Use back camera if available
        audio: false,
      });
      
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Unable to access camera. Please check permissions.');
      setIsCapturing(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsCapturing(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (!blob) return;

      const file = new File([blob], `photo-${Date.now()}.jpg`, {
        type: 'image/jpeg',
      });

      const newPhoto: PhotoPreview = {
        file,
        url: URL.createObjectURL(file),
        id: Math.random().toString(36).substr(2, 9),
      };

      updatePhotos([...photos, newPhoto]);
      stopCamera();
    }, 'image/jpeg', 0.8);
  };

  const removePhoto = (id: string) => {
    const photoToRemove = photos.find(p => p.id === id);
    if (photoToRemove) {
      URL.revokeObjectURL(photoToRemove.url);
    }
    updatePhotos(photos.filter(p => p.id !== id));
  };

  const previewPhotoHandler = (photo: PhotoPreview) => {
    setPreviewPhoto(photo);
  };

  const closePreview = () => {
    setPreviewPhoto(null);
  };

  return (
    <div className={`space-y-4 min-w-0 ${className}`}>
      {/* Upload Controls */}
      <div className="flex flex-wrap gap-2 min-w-0">
        <label className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0">
          <Upload className="h-4 w-4 mr-2" />
          Upload Photos
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileUpload}
            disabled={disabled || photos.length >= maxPhotos}
            className="sr-only"
          />
        </label>

        <button
          type="button"
          onClick={isCapturing ? stopCamera : startCamera}
          disabled={disabled || (photos.length >= maxPhotos && !isCapturing)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
        >
          <Camera className="h-4 w-4 mr-2" />
          {isCapturing ? 'Cancel Camera' : 'Take Photo'}
        </button>

        {isCapturing && (
          <button
            type="button"
            onClick={capturePhoto}
            className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex-shrink-0"
          >
            Capture
          </button>
        )}
      </div>

      {/* Photo Count Info */}
      <p className="text-sm text-gray-500 break-words">
        {photos.length} of {maxPhotos} photos • Max {maxFileSize}MB each
      </p>

      {/* Camera View */}
      {isCapturing && (
        <div className="relative bg-black rounded-lg overflow-hidden min-w-0">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="w-full h-64 object-cover"
          />
          <canvas ref={canvasRef} className="hidden" />
        </div>
      )}

      {/* Photo Grid */}
      {photos.length > 0 && (
        <div className="grid grid-cols-2 gap-2 min-w-0">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group min-w-0">
              <img
                src={photo.url}
                alt="Captured photo"
                className="w-full h-20 object-cover rounded-lg border border-gray-200"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => previewPhotoHandler(photo)}
                    className="p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                  >
                    <Eye className="h-4 w-4 text-gray-600" />
                  </button>
                  <button
                    type="button"
                    onClick={() => removePhoto(photo.id)}
                    className="p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                  >
                    <Trash2 className="h-4 w-4 text-red-600" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Photo Preview Modal */}
      {previewPhoto && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full w-full">
            <button
              onClick={closePreview}
              className="absolute top-4 right-4 p-2 bg-white rounded-full shadow-md hover:bg-gray-100 z-10"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>
            <img
              src={previewPhoto.url}
              alt="Photo preview"
              className="max-w-full max-h-full object-contain rounded-lg mx-auto block"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PhotoCapture;
