import React from 'react';
import { Search } from 'lucide-react';

export type UniversalFilterVariant = 'search-tags' | 'search-dropdowns';

export interface TagOption {
  id: string;
  name: string;
}

export interface DropdownOption {
  value: string;
  label: string;
}

export interface DropdownConfig {
  id: string;
  label?: string;
  value: string;
  onChange: (value: string) => void;
  options: DropdownOption[];
}

export interface UniversalFilterProps {
  variant: UniversalFilterVariant;

  // Search
  searchQuery: string;
  onSearchChange: (query: string) => void;
  searchPlaceholder?: string;

  // Tags (for 'search-tags')
  tags?: TagOption[];
  selectedTagId?: string;
  onTagChange?: (id: string) => void;

  // Dropdowns (for 'search-dropdowns')
  dropdowns?: DropdownConfig[];

  className?: string;
}

const UniversalFilter: React.FC<UniversalFilterProps> = ({
  variant,
  searchQuery,
  onSearchChange,
  searchPlaceholder = 'Search by title, number, description, or location...',
  tags = [],
  selectedTagId = 'all',
  onTagChange,
  dropdowns = [],
  className = '',
}) => {
  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between gap-4">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder={searchPlaceholder}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-full leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>

        {/* Right side controls */}
        {variant === 'search-tags' && (
          <div className="flex items-center space-x-2">
            {tags.map((t) => {
              const selected = selectedTagId === t.id;
              return (
                <button
                  key={t.id}
                  onClick={() => onTagChange && onTagChange(t.id)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-full border transition-colors ${
                    selected
                      ? 'bg-blue-50 text-blue-700 border-blue-200'
                      : 'bg-[#f3f4f6] text-gray-700 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  {t.name}
                </button>
              );
            })}
          </div>
        )}

        {variant === 'search-dropdowns' && (
          <div className="flex items-center gap-2">
            {dropdowns.map((dd) => (
              <div key={dd.id} className="flex items-center gap-2">
                {dd.label && (
                  <label className="text-sm text-gray-600 whitespace-nowrap">{dd.label}</label>
                )}
                <select
                  value={dd.value}
                  onChange={(e) => dd.onChange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                >
                  {dd.options.map((opt) => (
                    <option key={opt.value} value={opt.value}>
                      {opt.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UniversalFilter;
