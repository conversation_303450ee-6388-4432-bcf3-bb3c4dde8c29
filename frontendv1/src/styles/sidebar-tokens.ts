/**
 * Design tokens for the Sidebar component system
 * Centralized design values for consistent styling and easy maintenance
 */

import { SidebarTokens } from "../types/sidebar";

export const sidebarTokens: SidebarTokens = {
  spacing: {
    sidebarWidth: '4.5rem', // 72px - w-18 (further reduced width)
    flyoutWidth: '16rem', // 256px - w-64
    logoHeight: '4rem', // 64px - h-16
    itemSpacing: {
      company: '1.5rem', // 24px - space-y-6
      site: '0.75rem', // 12px - space-y-3
    },
    padding: {
      small: '0.375rem', // 6px - p-1.5
      medium: '0.5rem', // 8px - p-2
      large: '1rem', // 16px - p-4
    },
  },
  colors: {
    background: {
      sidebar: '#eaede8', // Same as body background for visibility
      flyout: '#eaede8', // Default flyout background color
      flyoutItemHover: '#fdfdf9', // Reverted hover background for flyout items
      itemActive: '#fdfdf9', // Reverted background for active states
      itemHover: '#fdfdf9', // Reverted hover background for consistency
    },
    text: {
      primary: '#374151', // gray-700
      secondary: '#6b7280', // gray-500
      active: '#22c55e', // green-500 (custom green from config)
      hover: '#22c55e', // green-500
    },
    border: {
      flyout: 'rgba(229, 231, 235, 0.3)', // Subtle border
      divider: '#e5e7eb', // gray-200
    },
    focus: {
      ring: '#22c55e', // green-500 for focus rings
    },
  },
  animation: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      enter: 'cubic-bezier(0.4, 0, 0.2, 1)', // ease-out
      exit: 'cubic-bezier(0.4, 0, 1, 1)', // ease-in
    },
  },
  typography: {
    fontSize: {
      small: '0.75rem', // text-xs
      base: '0.875rem', // text-sm
    },
    fontWeight: {
      normal: '400', // font-normal
      medium: '500', // font-medium
    },
  },
};

/**
 * Hover configuration with optimized timing for smooth interactions
 */
export const defaultHoverConfig = {
	enterDelay: 0, // Immediate response for better UX
	leaveDelay: 150, // Short delay to prevent accidental closes
	sidebarLeaveDelay: 400, // Longer delay when leaving sidebar for smoother transitions
	flyoutLeaveDelay: 300, // Medium delay when leaving flyout for better UX
};

/**
 * Z-index values for proper layering
 */
export const zIndexes = {
	sidebar: 10,
	flyout: 50,
	overlay: 40,
} as const;

/**
 * Responsive breakpoints for sidebar behavior
 */
export const breakpoints = {
	mobile: "768px",
	tablet: "1024px",
	desktop: "1280px",
} as const;

/**
 * Animation variants for different states
 */
export const animationVariants = {
	flyout: {
		hidden: {
			transform: "translateX(-100%)",
			opacity: 0,
		},
		visible: {
			transform: "translateX(0)",
			opacity: 1,
		},
	},
	flyoutContent: {
		hidden: {
			transform: "translateX(-20px)",
			opacity: 0,
		},
		visible: {
			transform: "translateX(0)",
			opacity: 1,
		},
	},
	menuItem: {
		idle: {
			scale: 1,
			backgroundColor: "transparent",
		},
		hover: {
			scale: 1,
			backgroundColor: sidebarTokens.colors.background.itemHover,
		},
		active: {
			scale: 1,
			backgroundColor: sidebarTokens.colors.background.itemActive,
		},
	},
} as const;

/**
 * Accessibility constants
 */
export const a11yConstants = {
	roles: {
		navigation: "navigation",
		menu: "menu",
		menuitem: "menuitem",
	},
	labels: {
		mainNavigation: "Main Navigation",
		siteNavigation: "Site Navigation",
		backToDashboard: "Back to Main Dashboard",
		home: "Home",
	},
	keys: {
		enter: "Enter",
		space: " ",
		arrowUp: "ArrowUp",
		arrowDown: "ArrowDown",
		escape: "Escape",
		tab: "Tab",
	},
} as const;

/**
 * CSS class name constants for consistency
 */
export const cssClasses = {
	sidebar: {
		container: "sidebar-container",
		menu: "sidebar-menu",
		menuItem: "sidebar-menu-item",
		logo: "sidebar-logo",
		backButton: "sidebar-back-button",
	},
	flyout: {
		container: "flyout-menu",
		header: "flyout-header",
		content: "flyout-content",
		menuItem: "flyout-menu-item",
	},
	states: {
		active: "active",
		hover: "hover",
		focused: "focused",
		expanded: "expanded",
	},
} as const;

/**
 * Utility function to get responsive spacing based on sidebar variant
 */
export const getSpacing = (variant: 'company' | 'site') => ({
  itemSpacing: sidebarTokens.spacing.itemSpacing[variant],
  iconSize: variant === 'site' ? 'scale-90' : '',
  containerWidth: variant === 'site' ? 'w-14' : 'w-14', // Further reduced width for 72px sidebar
  iconPadding: variant === 'site' ? 'p-1' : 'p-1', // Reduced padding for tighter spacing
  textMaxWidth: variant === 'site' ? 'max-w-[3.5rem]' : 'max-w-[3.5rem]', // Adjusted for 72px width
  textSize: 'text-[10px]', // Smaller than text-xs for better fit
  textLineHeight: 'leading-tight',
});

/**
 * Utility function to get color classes for different states
 */
export const getColorClasses = (
	isActive: boolean,
	isHover: boolean = false,
) => {
	if (isActive) {
		return {
			text: "text-green-500",
			background: "bg-[#fdfdf9]",
			fontWeight: "font-medium",
		};
	}

	return {
		text: isHover ? "text-green-500" : "text-gray-600",
		background: isHover ? "bg-[#fdfdf9]" : "",
		fontWeight: "font-normal",
	};
};

/**
 * Utility function to generate transition classes
 */
export const getTransitionClasses = (
	type: "colors" | "transform" | "all" = "colors",
) => {
	const baseClass = "transition-";
	const duration = "duration-300";
	const easing = "ease-out";

	return `${baseClass}${type} ${duration} ${easing}`;
};

/**
 * Utility function to get flyout menu item classes
 */
export const getFlyoutItemClasses = (
	isActive: boolean,
	isAddAction: boolean = false,
) => {
	const baseClasses = `
    ${cssClasses.flyout.menuItem}
    block
    px-4
    py-3
    text-sm
    rounded-md
    mb-1
    ${getTransitionClasses('colors')}
    focus:outline-none
    ${isAddAction ? 'flex items-center' : ''}
  `;

  if (isActive) {
    return `${baseClasses} bg-[#fdfdf9] text-gray-700 font-medium`;
  }

  if (isAddAction) {
    return `${baseClasses} text-green-500 bg-[#fdfdf9] hover:text-green-500 hover:bg-gray-600`;
  }

	return `${baseClasses} text-gray-700 hover:text-green-500 hover:bg-[#fdfdf9]`;
};
