// import React from 'react';
// import { render, screen, fireEvent, waitFor } from '@testing-library/react';
// import { MockedProvider } from '@apollo/client/testing';
// import { BrowserRouter } from 'react-router-dom';
// import { toast } from 'react-toastify';

// import { useNotifications } from '../hooks/useNotifications';
// import { useNotificationPreferences } from '../hooks/useNotificationPreferences';
// import NotificationSystem from '../components/notifications/NotificationSystem';
// import NotificationToast, { showNotificationToast } from '../components/notifications/NotificationToast';
// import { NotificationProvider } from '../components/notifications/NotificationProvider';
// import { 
//   GET_MY_NOTIFICATIONS, 
//   GET_UNREAD_NOTIFICATION_COUNT,
//   GET_ALL_NOTIFICATION_PREFERENCES 
// } from '../graphql/queries';
// import { 
//   MARK_NOTIFICATION_AS_READ, 
//   MARK_ALL_NOTIFICATIONS_AS_READ,
//   UPDATE_NOTIFICATION_PREFERENCES 
// } from '../graphql/mutations';
// import { ON_NOTIFICATION } from '../graphql/subscriptions';
// import { NotificationPriority } from '../types/notifications';

// // Mock react-toastify
// jest.mock('react-toastify', () => ({
//   toast: {
//     success: jest.fn(),
//     error: jest.fn(),
//     warning: jest.fn(),
//     info: jest.fn(),
//     dismiss: jest.fn(),
//   },
// }));

// // Mock audio
// global.Audio = jest.fn().mockImplementation(() => ({
//   play: jest.fn().mockResolvedValue(undefined),
//   volume: 0.5,
// }));

// // Mock Notification API
// global.Notification = {
//   permission: 'granted',
//   requestPermission: jest.fn().mockResolvedValue('granted'),
// } as any;

// const mockNotifications = [
//   {
//     id: 1,
//     type: 'training_expiring',
//     title: 'Training Expiring',
//     message: 'Your certification expires in 5 days',
//     priority: NotificationPriority.HIGH,
//     status: 'SENT',
//     readAt: null,
//     createdAt: '2024-01-01T10:00:00Z',
//     userId: 1,
//     tenantId: 1,
//     createdBy: 'system',
//     deliveries: []
//   },
//   {
//     id: 2,
//     type: 'system_alert',
//     title: 'System Maintenance',
//     message: 'Scheduled maintenance tonight',
//     priority: NotificationPriority.MEDIUM,
//     status: 'SENT',
//     readAt: '2024-01-01T11:00:00Z',
//     createdAt: '2024-01-01T09:00:00Z',
//     userId: 1,
//     tenantId: 1,
//     createdBy: 'system',
//     deliveries: []
//   }
// ];

// const mockPreferences = [
//   {
//     id: 1,
//     notificationType: 'training_expiring',
//     inAppEnabled: true,
//     emailEnabled: true,
//     smsEnabled: false,
//     minimumPriority: NotificationPriority.LOW,
//     doNotDisturbEnabled: false,
//     userId: 1,
//     createdAt: '2024-01-01T00:00:00Z'
//   }
// ];

// const mocks = [
//   {
//     request: {
//       query: GET_MY_NOTIFICATIONS,
//       variables: { skip: 0, take: 50, unreadOnly: false }
//     },
//     result: {
//       data: {
//         myNotifications: mockNotifications
//       }
//     }
//   },
//   {
//     request: {
//       query: GET_UNREAD_NOTIFICATION_COUNT
//     },
//     result: {
//       data: {
//         unreadNotificationCount: 1
//       }
//     }
//   },
//   {
//     request: {
//       query: GET_ALL_NOTIFICATION_PREFERENCES
//     },
//     result: {
//       data: {
//         allNotificationPreferences: mockPreferences
//       }
//     }
//   },
//   {
//     request: {
//       query: MARK_NOTIFICATION_AS_READ,
//       variables: { notificationId: 1 }
//     },
//     result: {
//       data: {
//         markNotificationAsRead: true
//       }
//     }
//   },
//   {
//     request: {
//       query: MARK_ALL_NOTIFICATIONS_AS_READ
//     },
//     result: {
//       data: {
//         markAllNotificationsAsRead: true
//       }
//     }
//   }
// ];

// const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
//   <MockedProvider mocks={mocks} addTypename={false}>
//     <BrowserRouter>
//       <NotificationProvider enableToasts={false} enableSounds={false}>
//         {children}
//       </NotificationProvider>
//     </BrowserRouter>
//   </MockedProvider>
// );

// describe('Notification System', () => {
//   beforeEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('useNotifications Hook', () => {
//     const TestComponent = () => {
//       const { notifications, unreadCount, loading, markAsRead, markAllAsRead } = useNotifications();
      
//       return (
//         <div>
//           <div data-testid="loading">{loading ? 'Loading' : 'Loaded'}</div>
//           <div data-testid="unread-count">{unreadCount}</div>
//           <div data-testid="notification-count">{notifications.length}</div>
//           <button onClick={() => markAsRead(1)} data-testid="mark-read-btn">
//             Mark as Read
//           </button>
//           <button onClick={markAllAsRead} data-testid="mark-all-read-btn">
//             Mark All as Read
//           </button>
//         </div>
//       );
//     };

//     it('should load notifications successfully', async () => {
//       render(
//         <TestWrapper>
//           <TestComponent />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         expect(screen.getByTestId('loading')).toHaveTextContent('Loaded');
//       });

//       expect(screen.getByTestId('unread-count')).toHaveTextContent('1');
//       expect(screen.getByTestId('notification-count')).toHaveTextContent('2');
//     });

//     it('should mark notification as read', async () => {
//       render(
//         <TestWrapper>
//           <TestComponent />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         expect(screen.getByTestId('loading')).toHaveTextContent('Loaded');
//       });

//       fireEvent.click(screen.getByTestId('mark-read-btn'));

//       await waitFor(() => {
//         expect(screen.getByTestId('unread-count')).toHaveTextContent('0');
//       });
//     });

//     it('should mark all notifications as read', async () => {
//       render(
//         <TestWrapper>
//           <TestComponent />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         expect(screen.getByTestId('loading')).toHaveTextContent('Loaded');
//       });

//       fireEvent.click(screen.getByTestId('mark-all-read-btn'));

//       await waitFor(() => {
//         expect(screen.getByTestId('unread-count')).toHaveTextContent('0');
//       });
//     });
//   });

//   describe('NotificationSystem Component', () => {
//     it('should render notification bell with unread count', async () => {
//       render(
//         <TestWrapper>
//           <NotificationSystem />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         const unreadBadge = screen.getByText('1');
//         expect(unreadBadge).toBeInTheDocument();
//       });
//     });

//     it('should open dropdown when bell is clicked', async () => {
//       render(
//         <TestWrapper>
//           <NotificationSystem />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         const bellButton = screen.getByRole('button');
//         fireEvent.click(bellButton);
//       });

//       expect(screen.getByText('Notifications')).toBeInTheDocument();
//     });
//   });

//   describe('NotificationToast Component', () => {
//     const mockNotification = mockNotifications[0];

//     it('should render notification toast correctly', () => {
//       const mockOnClose = jest.fn();
//       const mockOnMarkAsRead = jest.fn();

//       render(
//         <NotificationToast 
//           notification={mockNotification}
//           onClose={mockOnClose}
//           onMarkAsRead={mockOnMarkAsRead}
//         />
//       );

//       expect(screen.getByText('Training Expiring')).toBeInTheDocument();
//       expect(screen.getByText('Your certification expires in 5 days')).toBeInTheDocument();
//     });

//     it('should call onMarkAsRead when mark as read is clicked', () => {
//       const mockOnClose = jest.fn();
//       const mockOnMarkAsRead = jest.fn();

//       render(
//         <NotificationToast 
//           notification={mockNotification}
//           onClose={mockOnClose}
//           onMarkAsRead={mockOnMarkAsRead}
//         />
//       );

//       const markAsReadBtn = screen.getByText('Mark as read');
//       fireEvent.click(markAsReadBtn);

//       expect(mockOnMarkAsRead).toHaveBeenCalledWith(1);
//     });

//     it('should show toast notification with correct priority', () => {
//       const mockMarkAsRead = jest.fn();
      
//       showNotificationToast(mockNotification, mockMarkAsRead);

//       expect(toast.warning).toHaveBeenCalled();
//     });
//   });

//   describe('useNotificationPreferences Hook', () => {
//     const TestPreferencesComponent = () => {
//       const { preferences, loading, updatePreferences } = useNotificationPreferences();
      
//       return (
//         <div>
//           <div data-testid="prefs-loading">{loading ? 'Loading' : 'Loaded'}</div>
//           <div data-testid="prefs-count">{preferences.length}</div>
//           <button 
//             onClick={() => updatePreferences('training_expiring', { emailEnabled: false })}
//             data-testid="update-prefs-btn"
//           >
//             Update Preferences
//           </button>
//         </div>
//       );
//     };

//     it('should load preferences successfully', async () => {
//       render(
//         <TestWrapper>
//           <TestPreferencesComponent />
//         </TestWrapper>
//       );

//       await waitFor(() => {
//         expect(screen.getByTestId('prefs-loading')).toHaveTextContent('Loaded');
//       });

//       expect(screen.getByTestId('prefs-count')).toHaveTextContent('1');
//     });
//   });
// });

// // Integration tests
// describe('Notification Integration Tests', () => {
//   it('should handle complete notification flow', async () => {
//     const TestIntegrationComponent = () => {
//       const { notifications, unreadCount, markAsRead } = useNotifications();
      
//       return (
//         <div>
//           <NotificationSystem />
//           <div data-testid="integration-unread">{unreadCount}</div>
//           {notifications.map(notification => (
//             <div key={notification.id} data-testid={`notification-${notification.id}`}>
//               <span>{notification.title}</span>
//               <button onClick={() => markAsRead(notification.id)}>
//                 Mark Read
//               </button>
//             </div>
//           ))}
//         </div>
//       );
//     };

//     render(
//       <TestWrapper>
//         <TestIntegrationComponent />
//       </TestWrapper>
//     );

//     // Wait for data to load
//     await waitFor(() => {
//       expect(screen.getByTestId('integration-unread')).toHaveTextContent('1');
//     });

//     // Check notifications are rendered
//     expect(screen.getByTestId('notification-1')).toBeInTheDocument();
//     expect(screen.getByTestId('notification-2')).toBeInTheDocument();

//     // Mark one as read
//     const markReadBtn = screen.getAllByText('Mark Read')[0];
//     fireEvent.click(markReadBtn);

//     // Check unread count updated
//     await waitFor(() => {
//       expect(screen.getByTestId('integration-unread')).toHaveTextContent('0');
//     });
//   });
// });
