[ 
    // I want you to make the following pages in site specific toolbox route
    // all the path are under the /sites/:siteId/toolbox
    // after submiting navigate to the toolbox dashboard except for eveninig toolbox where navigate to evening-toolbox
    {
        "name":"dashboard",
        "path":"/",
        "actions":[
            // dashboard for toolbox
            "There should be a big button at the top with 'Create Toolbox'. On click navigate to toolbox-job",
            "There should also have a create evening toolbox button that navigates to evening-toolbox",
            "gets toolbox that has not been closed, these toolbox are used to create a list of toolbox with the toolbox date and time and a button written 'Continue'",
            "when the toolbox list is clicked navigate to toolbox details",
            "when 'continue' button is clicked where it navigates to should depend on the status of the toolbox",
            "if the status is drafted navigate to toolbox-add-photos",
            "if status is started navigate to toolbox-fill",
            "if status is pending_attendance navigate to toolbox-attendance",
            "if status is closed navigate to toolbox-details"
            
        ]
    },
    {
        "name":"toolbox-job",
        "path":"/jobs",
        "actions":[
            "gets valid jobs from graphql and list there title and list them with checkbox",
            "at the buttom there should be a 'submit' button that takes the id of jobs selected (checkbox ticked) and uses it to create a toolbox - take the drafted by id as 1"
        ]
    },
    {
        "name":"toolbox-add-photos",
        "path":"/photos/:toolboxId",
        "actions":[
            "In this should bave a button 'Add Photo' which when clicked, the user should be able to TAKE A PHOTO (implement the take photo functionality)",
            "The page should list already taken photos and user should be able to view and remove any taken photo",
            "Use the photos to hit the AddAttendeePhotosAsync mutation with  StartedById default to 1"
        ]
    },
    {
        "name":"toolbox-fill/:toolboxId",
        "path":"/fill/:toolboxId",
        "actions":[
            "THis page should have a form to fill details needed for FillToolbox mutation",
            "the form should have a button to submit the form and validation as to ensure it is in the required shape of FillToolboxInput",
            "by default ConductorId should be 1"
        ]
    },
    {
        "name":"toolbox-attendance/:toolboxId",
        "path":"/attendance/:toolboxId",
        "actions":[
            "The user should be able to select workers. THe user should be able to search for workers and check them for attendance",
            "the checked worker ids should be used to hit the AddAttendees mutation",
            "by default ClosedById should be 1"
        ]
    },
    {
        "name":"toolbox-details",
        "path":"/details/:toolboxId",
        "actions":[
            "Hits the toolbox by id query and displays the details"
        ]
    },
    {
        "name":"evening-toolbox",
        "path":"/evening-toolbox",
        "actions":[
            "Should have a fill evening toolbox button",
            "Gets all evening toolbox and lists them. On clicking the list navigate to evening-toolbox/:eveningToolboxId"
        ]
    },
    {
        "name":"evening-toolbox-fill",
        "path":"/evening-toolbox/fill",
        "actions":[
            "Should have a form to fill details needed for FillEveningToolbox mutation",
            "the form should have a button to submit the form and validation as to ensure it is in the required shape of EveningToolboxInput",
            "by default ConductorId should be 1"
        ]
    },
    {
        "name":"evening-toolbox-detail",
        "path":"/evening-toolbox/:eveningToolboxId",
        "actions":[
            "Hits the Evening toolbox by id query and displays the details"
        ]
    }
]