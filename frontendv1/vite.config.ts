/// <reference types="vitest" />
/// <reference types="vite/client" />

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [react()],
	test:{
		globals: true,
		environment: "jsdom",
		setupFiles: ["./src/__tests__/setup.ts"],
	},
	server: {
		allowedHosts: true,
		port: 5173,
		host: true,
	},
	build: {
		chunkSizeWarningLimit: 5000,
	},
});
