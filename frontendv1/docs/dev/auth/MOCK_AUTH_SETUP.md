# Mock Authentication Setup

This document explains how to use the mock authentication system for frontend development.

## Quick Start

The application is now configured to use mock authentication by default in development mode. Simply run:

```bash
npm run dev
```

The application will start at `http://localhost:5173/` and you can log in using the mock credentials.

## Mock Credentials

Use any of these credentials to log in:

### Primary Demo Account
- **Email**: `<EMAIL>`
- **Password**: `Admin@123!`
- **Tenant ID**: `1` (optional)

### Alternative Credentials
- **Email**: Any valid email address (e.g., `<EMAIL>`, `<EMAIL>`)
- **Password**: `password`
- **Tenant ID**: Any number (optional)

## Environment Configuration

### Development Mode (Mock Authentication)
The `.env` file is configured for mock authentication:
```env
VITE_GRAPHQL_URI_1="http://localhost:5192/graphql"
VITE_FILE_BASE_URI_1="http://localhost:5192"
VITE_USE_MOCK_AUTH="true"
```

### Production Mode (Real GraphQL Backend)
The `.env.production` file is configured for real backend:
```env
VITE_GRAPHQL_URI_1="http://localhost:5192/graphql"
VITE_FILE_BASE_URI_1="http://localhost:5192"
VITE_USE_MOCK_AUTH="false"
```

## Switching Between Mock and Production

### Method 1: Environment Variable (Recommended)
Change the `VITE_USE_MOCK_AUTH` value in your `.env` file:

- **For Mock Mode**: `VITE_USE_MOCK_AUTH="true"`
- **For Production Mode**: `VITE_USE_MOCK_AUTH="false"`

### Method 2: Environment Files
Use different environment files:

- **Development**: `npm run dev` (uses `.env`)
- **Production**: `npm run build` (uses `.env.production`)

### Method 3: Manual Override
You can temporarily override the environment variable:

```bash
# Windows PowerShell
$env:VITE_USE_MOCK_AUTH="false"; npm run dev

# Windows Command Prompt
set VITE_USE_MOCK_AUTH=false && npm run dev

# Linux/Mac
VITE_USE_MOCK_AUTH=false npm run dev
```

## How It Works

The authentication system uses a service selector pattern:

1. **Service Selector** (`src/services/authServiceSelector.ts`):
   - Checks the `VITE_USE_MOCK_AUTH` environment variable
   - Routes to either `mockAuthService` or `graphqlAuthService`
   - Provides a consistent interface for both services

2. **Mock Service** (`src/services/mockAuthService.ts`):
   - Simulates all authentication operations
   - Stores tokens in localStorage
   - Provides realistic delays and responses
   - Supports all the same methods as the real GraphQL service

3. **Auth Context** (`src/hooks/useAuthContext.tsx`):
   - Uses the service selector instead of directly calling GraphQL service
   - No changes needed to components using authentication

## Features

### Mock Authentication Features
- ✅ Login with demo credentials
- ✅ User registration simulation
- ✅ Token management (localStorage)
- ✅ Session persistence
- ✅ Password reset simulation
- ✅ User profile updates
- ✅ Permission checking
- ✅ Multiple session management
- ✅ Realistic API delays

### Console Logging
When using mock authentication, you'll see helpful console messages:
- Service type being used (MOCK vs GRAPHQL)
- Available credentials
- Mock operations (password resets, session management)

## Troubleshooting

### Issue: Application still tries to connect to GraphQL
**Solution**: Make sure `VITE_USE_MOCK_AUTH="true"` is set in your `.env` file and restart the dev server.

### Issue: Login not working with demo credentials
**Solution**: Check the console for error messages. Ensure you're using the exact credentials listed above.

### Issue: Changes not taking effect
**Solution**: Restart the development server after changing environment variables.

## Development Tips

1. **Check Console**: The application logs which authentication service is being used
2. **Network Tab**: In mock mode, you won't see GraphQL requests in the browser's network tab
3. **localStorage**: Mock tokens are stored in localStorage and persist between sessions
4. **Hot Reload**: Environment variable changes require a server restart

## Production Deployment

For production deployment:

1. Ensure `.env.production` has `VITE_USE_MOCK_AUTH="false"`
2. Update the GraphQL URI to point to your production backend
3. Build the application: `npm run build`
4. Deploy the `dist` folder

## File Structure

```
src/
├── services/
│   ├── authServiceSelector.ts    # Service selector (main entry point)
│   ├── mockAuthService.ts        # Mock authentication implementation
│   └── graphqlAuthService.ts     # Real GraphQL authentication
├── hooks/
│   └── useAuthContext.tsx        # Updated to use service selector
└── ...
```

This setup provides a clean separation between mock and production authentication while maintaining the exact same GraphQL query/mutation structure throughout your application.
