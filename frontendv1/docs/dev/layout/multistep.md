# Site Creation Multi-Step Form Implementation

## Overview
A 5-step site creation wizard that transforms minimal user input into comprehensive site data. Each step focuses on essential information while the system auto-calculates derived properties for a magical user experience.

## Schema-Based Step Division

### Step Flow Overview
1. **Basic Info** (2 minutes) - Core project details
2. **Site Boundary** (3 minutes) - Location search and polygon drawing
3. **Areas** (1 minute) - Simple area name list
4. **Stakeholders** (2 minutes) - Key contacts and roles
5. **Regulatory** (1 minute) - Required approvals

**Total Time: ~9 minutes for complete site setup**

## Layout Structure

### Container
- FloatingCard component with sidebar-free layout for site creation
- Full-width content area for optimal drawing and form experience
- Clean, distraction-free interface
- Responsive design for desktop and tablet use

## Step Navigation Design

### Responsive Sidebar Layout
**Purpose**: Effortless viewing of pending items and completed steps

**Sidebar Dimensions:**
- **Width**: 1/3 of available content area width (33.33vw) on large screens
- **Minimum Width**: 280px (prevents overcrowding on smaller screens)
- **Maximum Width**: 400px (prevents excessive width on ultra-wide screens)
- **Position**: Fixed, always visible during form interaction
- **Height**: Full viewport height (100vh)

**Content Alignment:**
- **Step items**: Right-aligned within the sidebar area
- **Text alignment**: Right-aligned for step titles and descriptions
- **Connector line**: Positioned relative to right-aligned step circles
- **Visual hierarchy**: Steps flow from top to bottom, right-aligned

### Responsive Step Components
**Step Circle (Height-Responsive):**
- **Large screens (>1200px)**: 44px diameter
- **Medium screens (768-1200px)**: 40px diameter
- **Small screens (<768px)**: 36px diameter
- **Position**: Right-aligned, anchored to connector line
- **Content**: Step number (1-5) or checkmark icon
- **Color**: Changes based on completion state

**Step Content (Height-Responsive):**
- **Step Title**:
  - Large screens: 18px bold
  - Medium screens: 16px bold
  - Small screens: 14px bold
- **Step Description**:
  - Large screens: 16px regular
  - Medium screens: 14px regular
  - Small screens: 12px regular
- **Text Alignment**: Right-aligned to match circle position
- **Color**: Dark gray (#374151) for titles, muted gray (#6B7280) for descriptions

**Spacing (Height-Responsive):**
- **Step Vertical Spacing**:
  - Large screens: 64px between steps
  - Medium screens: 48px between steps
  - Small screens: 36px between steps
- **Circle to Text**: 16px horizontal gap (consistent)
- **Title to Description**: 4px vertical gap (consistent)

**Connector Line Design (Right-Aligned):**
- **Position**: Vertical line positioned at the center of right-aligned step circles
- **Width**: 2px (consistent across all screen sizes)
- **Height**: Spans from first to last step circle
- **Alignment**: Right-aligned relative to sidebar content area
- **Base Color**: Light gray (#E5E7EB)
- **Progress Glow**: Animated gradient based on completion percentage
- **Animation**: Line glows from top to current step position
- **Transitions**: Smooth 0.3s ease-in-out between states

**Glow Animation Specifications:**
- **Completed Segments**: Solid green line (#10B981) with subtle glow
- **Current Progress**: Blue gradient (#3B82F6) with animated glow effect
- **Pending Segments**: Light gray (#E5E7EB) with no glow
- **Animation**: 0.3s ease-in-out transitions between states
- **Glow Effect**:
  ```css
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6),
              0 0 16px rgba(59, 130, 246, 0.3);
  ```

**Progress Calculation:**
- Step 1 Complete: 20% progress
- Step 2 Complete: 40% progress
- Step 3 Complete: 60% progress
- Step 4 Complete: 80% progress
- Step 5 Complete: 100% progress
- Smooth gradient transition between completed and pending segments

### Site Creation Steps

1. **Basic Info**
   - Icon: Building/Info icon
   - Description: "Project details & timeline"
   - Fields: Site name, project type, dates, budget, client, PM
   - Auto-generates: Site code, duration calculation

2. **Site Boundary**
   - Icon: Map/Location icon
   - Description: "Location & boundary drawing"
   - Fields: OSM search, polygon drawing
   - Auto-calculates: Area, perimeter, center point, bounding box

3. **Areas**
   - Icon: Grid/Layout icon
   - Description: "Define site areas"
   - Fields: Simple area name list
   - Example: ["Main Building", "Parking Area", "Storage Zone"]

4. **Stakeholders**
   - Icon: Users/People icon
   - Description: "Key contacts & roles"
   - Fields: Client, PM, contractor, architect details
   - Optional: Additional stakeholders in JSON

5. **Regulatory**
   - Icon: Shield/Document icon
   - Description: "Required approvals"
   - Fields: Building permit, NEMA, NCA, fire safety status
   - Optional: Additional approvals in JSON

### Step States & Styling
**Completed Steps:**
- Green circle (#10B981) with white checkmark
- Green glow on connector line segment
- Clickable with hover effects
- Title in dark color, description in normal gray

**Current Step:**
- Blue circle (#3B82F6) with white step number
- Blue glow on connector line up to current position
- Highlighted background (light blue tint)
- Title in dark color, description in normal gray

**Pending Steps:**
- Gray circle (#9CA3AF) with step number
- No glow on connector line
- Disabled state, not clickable
- Title and description in muted gray

**Error State:**
- Red circle (#EF4444) with exclamation mark
- Red glow on connector line segment
- Error count badge if multiple errors
- Title in red, description shows error summary
- 

## Form Design Specifications

### Layout Structure
**Main Content Area:**
- **Width**: 2/3 of available content area (66.67vw) on large screens
- **Position**: Right side of screen, adjacent to sidebar
- **Padding**: Flush with page edges (no container margins)
- **Background**: Clean, minimal white background
- **Internal Spacing**: Generous padding for content (48px on large screens)
- **Responsive Width**:
  - Large screens: calc(100vw - 33.33vw) = 66.67vw
  - Medium screens: calc(100vw - 280px) when sidebar hits minimum width
  - Small screens: 100vw when sidebar is collapsed

### Input Field Design
**Visual Style:**
- Pure white background (#FFFFFF)
- Thin 1px border in light gray (#D1D5DB)
- Rounded corners (6px border-radius)
- Subtle box-shadow on focus (0 0 0 3px rgba(59, 130, 246, 0.1))
- Smooth transitions for all state changes

**Field Specifications:**
- Height: 44px for text inputs and dropdowns
- Padding: 12px horizontal, 10px vertical
- Font: 16px regular, dark gray text (#374151)
- Placeholder: 14px regular, muted gray (#9CA3AF)

**Field States:**
- **Default**: White background, gray border
- **Focus**: Blue border (#3B82F6), blue shadow
- **Error**: Red border (#EF4444), red shadow
- **Disabled**: Light gray background (#F9FAFB), muted border

**Label Design:**
- 14px medium weight, dark gray (#374151)
- 8px margin bottom from label to input
- Required fields marked with red asterisk

**Dropdown Styling:**
- Chevron down icon on right (gray, changes to blue on focus)
- Dropdown menu with white background and subtle shadow
- Hover states for dropdown options

### Form Section Layout
**Section Grouping:**
- Related fields grouped in visual sections
- Section headers: 18px semi-bold, dark gray
- 32px spacing between sections
- 16px spacing between fields within sections

**Responsive Behavior:**
- Single column layout for optimal focus
- Fields stretch to full available width
- Minimum field width: 280px
- Maximum field width: 480px for better readability

## Step 1: Basic Info Implementation

### Form Fields (Direct Schema Mapping)
```javascript
// Maps directly to schema columns
const basicInfoFields = {
  siteName: "",              // site_name VARCHAR(100) NOT NULL
  projectType: "",           // project_type VARCHAR(50) NOT NULL
  constructionType: "",      // construction_type VARCHAR(50) NOT NULL
  plannedStartDate: "",      // planned_start_date DATE NOT NULL
  plannedEndDate: "",        // planned_end_date DATE NOT NULL
  estimatedBudget: 0,        // estimated_budget DECIMAL(15,2)
  currency: "KES",           // currency VARCHAR(3) DEFAULT 'KES'
  clientName: "",            // client_name VARCHAR(100) NOT NULL
  projectManagerName: ""     // project_manager_name VARCHAR(100) NOT NULL
};
```

### Form Layout
**Project Details Section:**
- Site Name (text input, required)
- Project Type (dropdown: Residential, Commercial, Industrial, etc.)
- Construction Type (dropdown: New, Renovation, Extension, Demolition, Maintenance)
- Description (textarea, optional)

**Timeline Section:**
- Planned Start Date (date picker, required)
- Planned End Date (date picker, required)
- Estimated Duration (auto-calculated, read-only)

**Budget Section:**
- Estimated Budget (number input)
- Currency (dropdown, default KES)

**Key Contacts Section:**
- Client Name (text input, required)
- Project Manager Name (text input, required)

### Auto-Calculations
- Site Code: Auto-generated from project type + name + year + random
- Duration: Auto-calculated from start/end dates
- Form validation before proceeding to next step

## Step 2: Site Boundary Implementation

### Form Fields (Schema Mapping)
```javascript
// Maps to location and boundary schema columns
const boundaryFields = {
  searchQuery: "",           // search_query VARCHAR(500)
  displayName: "",           // display_name VARCHAR(500)
  addressStreet: "",         // address_street VARCHAR(200)
  addressCity: "",           // address_city VARCHAR(100)
  addressCounty: "",         // address_county VARCHAR(100)
  latitude: 0,               // latitude DECIMAL(10,8)
  longitude: 0,              // longitude DECIMAL(11,8)
  siteBoundaryGeom: null,    // site_boundary_geom GEOMETRY(POLYGON, 4326)
  drawingComplete: false     // drawing_complete BOOLEAN
};
```

### Interactive Components
**Location Search:**
- OSM search input with autocomplete
- Search results dropdown with place suggestions
- Map centers on selected location

**Map Interface:**
- Interactive map (Leaflet/MapBox)
- Polygon drawing tool
- Drawing controls (draw, edit, delete)
- Coordinate display

**Address Form:**
- Street Address (auto-filled from OSM, editable)
- City (auto-filled from OSM, editable)
- County (auto-filled from OSM, editable)
- Country (default Kenya, editable)

### Auto-Calculations
- Area calculation from drawn polygon
- Perimeter calculation from polygon
- Center point (centroid) calculation
- Bounding box calculation
- Address resolution from coordinates

## Step 3: Areas Implementation

### Form Fields (Schema Mapping)
```javascript
// Maps to JSON array in schema
const areasFields = {
  areaNames: []              // area_names JSON - ["Main Building", "Parking Area"]
};
```

### Simple Interface
**Area List Management:**
- Text input for area name
- "Add Area" button
- List of added areas with delete option
- Drag-and-drop reordering (optional)

**Pre-defined Suggestions:**
- Common area types as quick-add buttons
- "Main Building", "Parking Area", "Storage Zone"
- "Security Gate", "Generator Room", "Waste Area"

### Validation
- Minimum 1 area required
- Maximum 20 areas recommended
- Duplicate name prevention
- Area name length validation (3-50 characters)

## Step 4: Stakeholders Implementation

### Form Fields (Direct Schema Mapping)
```javascript
// Mandatory stakeholders as direct columns
const stakeholderFields = {
  clientEmail: "",           // client_email VARCHAR(100)
  clientPhone: "",           // client_phone VARCHAR(20)
  projectManagerEmail: "",   // project_manager_email VARCHAR(100)
  projectManagerPhone: "",   // project_manager_phone VARCHAR(20)
  mainContractorName: "",    // main_contractor_name VARCHAR(100)
  mainContractorEmail: "",   // main_contractor_email VARCHAR(100)
  mainContractorCompany: "", // main_contractor_company VARCHAR(100)
  architectName: "",         // architect_name VARCHAR(100)
  architectEmail: "",        // architect_email VARCHAR(100)
  architectCompany: "",      // architect_company VARCHAR(100)
  additionalStakeholders: [] // additional_stakeholders JSON
};
```

### Form Layout
**Client Details:**
- Email (required, validation)
- Phone (optional, format validation)

**Project Manager Details:**
- Email (required, validation)
- Phone (optional, format validation)

**Main Contractor:**
- Company Name (optional)
- Contact Person (optional)
- Email (optional, validation)

**Architect:**
- Name (optional)
- Company (optional)
- Email (optional, validation)

**Additional Stakeholders:**
- Dynamic list for optional stakeholders
- Role, Name, Company, Email fields
- Add/remove functionality

## Step 5: Regulatory Implementation

### Form Fields (Direct Schema Mapping)
```javascript
// Mandatory approvals as direct columns
const regulatoryFields = {
  buildingPermitRequired: true,     // building_permit_required BOOLEAN
  buildingPermitStatus: "Not Started", // building_permit_status VARCHAR(20)
  nemaEiaRequired: false,           // nema_eia_required BOOLEAN
  nemaEiaStatus: "Not Started",     // nema_eia_status VARCHAR(20)
  ncaRegistrationRequired: true,    // nca_registration_required BOOLEAN
  ncaRegistrationStatus: "Not Started", // nca_registration_status VARCHAR(20)
  fireSafetyRequired: true,         // fire_safety_required BOOLEAN
  fireSafetyStatus: "Not Started",  // fire_safety_status VARCHAR(20)
  additionalApprovals: []           // additional_approvals JSON
};
```

### Form Layout
**Building Permit:**
- Required checkbox (default checked)
- Status dropdown (Not Started, In Progress, Approved, Rejected)
- Authority field (auto-filled based on location)

**NEMA EIA:**
- Required checkbox (default unchecked)
- Status dropdown (conditional on required)

**NCA Registration:**
- Required checkbox (default checked)
- Status dropdown

**Fire Safety:**
- Required checkbox (default checked)
- Status dropdown

**Additional Approvals:**
- Dynamic list for special permits
- Type, Authority, Status fields
- Add/remove functionality

### Auto-Calculations
- Overall compliance status based on individual statuses
- Required approvals based on project type and location
- Authority suggestions based on address county

## Navigation & State Management

### Button Layout
**Bottom Navigation:**
- Previous Button (secondary style, disabled on first step)
- Next Button (primary style, validates before proceeding)
- Save Draft Button (always available)

### Step Navigation Behavior
- **Completed steps**: Clickable, green checkmark
- **Current step**: Highlighted, blue indicator
- **Pending steps**: Disabled, gray indicator
- **Invalid steps**: Red indicator with error count

### Form State Management
```javascript
const siteCreationState = {
  currentStep: 1,              // current_step INTEGER
  completedSteps: [],          // completed_steps JSON
  sessionId: "uuid",           // session_id VARCHAR(100)
  isComplete: false,           // is_complete BOOLEAN
  validationErrors: [],        // validation_errors JSON
  lastSavedAt: new Date()      // last_saved_at TIMESTAMP
};
```

### Auto-Save Implementation
- Save form data every 30 seconds
- Save on step navigation
- Save on field blur for critical fields
- Show save status indicator
- Handle offline scenarios

### Validation Rules
- **Step 1**: Site name, project type, dates, client name, PM name required
- **Step 2**: Valid polygon with minimum area required
- **Step 3**: At least one area name required
- **Step 4**: Client and PM email required
- **Step 5**: Required approval statuses must be set

### Error Handling
- Field-level validation with inline errors
- Step-level validation summary
- Network error handling for auto-save
- Graceful degradation for map/OSM failures

## CSS Implementation Examples

### Responsive Sidebar Step Navigation
```css
.step-sidebar {
  /* Responsive width: 1/3 of viewport with constraints */
  width: clamp(280px, 33.33vw, 400px);
  height: 100vh;
  background: #F9FAFB;
  border-right: 1px solid #E5E7EB;
  padding: clamp(24px, 4vh, 48px) clamp(16px, 3vw, 32px);
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center steps vertically */
}

.step-list {
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* Right-align all step items */
  position: relative;
  width: 100%;
}

.step-connector-line {
  position: absolute;
  right: clamp(18px, 2.5vw, 22px); /* Center of right-aligned circles */
  top: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom,
    #10B981 0%,
    #10B981 var(--progress-percentage),
    #E5E7EB var(--progress-percentage),
    #E5E7EB 100%);
  transition: all 0.3s ease-in-out;
  z-index: 1;
}

.step-connector-line.glowing {
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6),
              0 0 16px rgba(59, 130, 246, 0.3);
}

.step-item {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end; /* Right-align content */
  margin-bottom: clamp(36px, 8vh, 64px); /* Responsive vertical spacing */
  position: relative;
  z-index: 2;
  width: 100%;
  text-align: right; /* Right-align text */
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 16px; /* Gap between text and circle */
}

.step-title {
  font-size: clamp(14px, 2.5vh, 18px); /* Responsive font size */
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  text-align: right;
}

.step-description {
  font-size: clamp(12px, 2vh, 16px); /* Responsive font size */
  color: #6B7280;
  text-align: right;
  line-height: 1.4;
}

.step-circle {
  width: clamp(36px, 5vh, 44px); /* Responsive circle size */
  height: clamp(36px, 5vh, 44px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: clamp(14px, 2.2vh, 18px); /* Responsive font size */
  transition: all 0.3s ease-in-out;
  flex-shrink: 0; /* Prevent circle from shrinking */
}

.step-circle.completed {
  background: #10B981;
  color: white;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.step-circle.current {
  background: #3B82F6;
  color: white;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step-circle.pending {
  background: #9CA3AF;
  color: white;
}

.step-circle.error {
  background: #EF4444;
  color: white;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
}
```

### Form Input Styling
```css
.form-input {
  width: 100%;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 16px;
  color: #374151;
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #EF4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input::placeholder {
  color: #9CA3AF;
  font-size: 14px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: " *";
  color: #EF4444;
}

.form-section {
  margin-bottom: 32px;
}

.form-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.form-field {
  margin-bottom: 16px;
}
```

### Responsive Main Content Layout
```css
.main-content {
  /* Responsive width: 2/3 of viewport with sidebar constraints */
  margin-left: clamp(280px, 33.33vw, 400px);
  width: calc(100vw - clamp(280px, 33.33vw, 400px));
  min-height: 100vh;
  background: #FFFFFF;
  padding: clamp(24px, 4vh, 48px) clamp(32px, 5vw, 64px);
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive breakpoints */
@media (max-width: 1024px) {
  .step-sidebar {
    width: 280px; /* Fixed width on medium screens */
  }

  .main-content {
    margin-left: 280px;
    width: calc(100vw - 280px);
    padding: clamp(20px, 3vh, 32px) clamp(24px, 4vw, 48px);
  }
}

@media (max-width: 768px) {
  .step-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    width: 280px;
    z-index: 1000;
  }

  .step-sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    width: 100vw;
    padding: 16px 24px;
  }

  /* Adjust step spacing for mobile */
  .step-item {
    margin-bottom: 24px;
  }

  .step-title {
    font-size: 14px;
  }

  .step-description {
    font-size: 12px;
  }

  .step-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

/* Ultra-wide screen optimization */
@media (min-width: 1600px) {
  .step-sidebar {
    max-width: 400px; /* Prevent excessive width */
  }

  .main-content {
    margin-left: 400px;
    width: calc(100vw - 400px);
  }
}
```

This implementation provides a streamlined, magical site creation experience that transforms minimal user input into comprehensive site data through intelligent auto-calculations and schema-optimized data storage.