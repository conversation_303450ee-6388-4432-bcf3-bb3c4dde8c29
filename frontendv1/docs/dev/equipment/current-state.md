# Equipment 
## Findings

I analyzed the routes and the full component trees that render:
- http://localhost:5173/company-equipment
- http://localhost:5173/sites/:siteId/equipment

Below is a comprehensive map of components, data flow, utilities, and contexts involved.

## Routing

- Route definitions live in [frontendv1/src/App.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:0:0-0:0).
  - Company equipment (org-level):
    - Route: `/company-equipment`
    - Element: [CompanyEquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:21:0-146:1) from [src/pages/CompanyEquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:0:0-0:0)
  - Site equipment (site-level):
    - Route: `/sites/:siteId/equipment`
    - Element: [EquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:21:0-143:2) from [src/pages/EquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:0:0-0:0)

Both routes are nested inside a protected area that wraps content in [AppLayout](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:94:0-106:2) which displays the `Sidebar` and `TopBar` via [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2).

# Company Equipment page (org-level)

URL: `/company-equipment`

## Top-level page

- [src/pages/CompanyEquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:0:0-0:0)
  - Wraps content in [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) ([src/components/layout/FloatingCard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:0:0-0:0))
  - Uses `TabContainer` (`src/components/data/shared/TabContainer`) with tabs:
    - dashboard → [CompanyEquipmentDashboard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:92:0-229:1)
    - equipment → [CompanyEquipmentList](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentList.tsx:186:0-387:1)
    - add-import → [CompanyEquipmentAdd](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAdd.tsx:4:0-290:1)
    - equipment-view → inline placeholder
    - assignments → [CompanyEquipmentAssignments](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAssignments.tsx:2:0-47:1)
    - maintenance → [CompanyEquipmentMaintenance](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentMaintenance.tsx:2:0-52:1)
    - templates → inline placeholder
    - reports → [CompanyEquipmentReports](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentReports.tsx:2:0-47:1)
  - URL behavior:
    - Reads `?tab=` and `?search=` from `useLocation()`
    - If `search` exists and no `tab`, defaults to equipment tab
    - Updates `?tab=` when switching tabs

## Tabs and components

- [src/components/equipment/CompanyEquipmentDashboard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:0:0-0:0)
  - Displays KPI summary cards and quick actions.
  - Data source: `mockCompanyEquipment`, [getEquipmentKPIs](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:311:0-348:2) from [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0)
  - Navigates to other tabs via `onNavigateToTab` callback passed from page
  - Icons: `lucide-react`

- [src/components/equipment/CompanyEquipmentList.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentList.tsx:0:0-0:0)
  - Search and filters via `UniversalSearchFilter` (`src/components/shared/UniversalSearchFilter`) [not opened, but referenced]
  - Data source: `mockCompanyEquipment`, `equipmentCategories`, `equipmentStatuses`, `complianceStatuses`, `ownershipTypes`, `equipmentSites` from [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0)
  - Renders either list rows or grid cards
  - Action handlers are currently stubs (console logs)
  - External link: view company equipment in context, via `window.open("/company-equipment?search=...")`

- [src/components/equipment/CompanyEquipmentAdd.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAdd.tsx:0:0-0:0)
  - Add single equipment form and a “Bulk Import” placeholder
  - Uses options from [equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0) for categories, ownership types, sites
  - Stores form state locally, `console.log` on submit

- [src/components/equipment/CompanyEquipmentAssignments.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAssignments.tsx:0:0-0:0)
  - Placeholder UI for site assignments

- [src/components/equipment/CompanyEquipmentMaintenance.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentMaintenance.tsx:0:0-0:0)
  - Placeholder UI for maintenance and inspections

- [src/components/equipment/CompanyEquipmentReports.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentReports.tsx:0:0-0:0)
  - Placeholder for reports/exports

## Layout and page scaffolding

- [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) ([src/components/layout/FloatingCard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:0:0-0:0))
  - Integrates with `TopBar` (`src/components/layout/TopBar`) via `useTopBarContext`, sets title/minimal/back from props
  - Uses `useLayoutContext` to determine full width vs. offset layout (sidebar)
  - Provides inner scroll container

# Site Equipment page (site-level)

URL: `/sites/:siteId/equipment`

## Top-level page

- [src/pages/EquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:0:0-0:0)
  - `useParams()` to grab `siteId`
  - Wraps content in [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2)
  - Breadcrumb shows a link to “Company Equipment” using `Link` to `/company-equipment` with `Building` icon
  - Uses `TabContainer` with tabs:
    - dashboard → [EquipmentDashboard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentDashboard.tsx:23:0-269:2)
    - general-equipment → [GeneralEquipment](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/GeneralEquipment.tsx:25:0-456:2)
    - request-equipment → [SiteEquipmentRequest](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/SiteEquipmentRequest.tsx:214:0-367:1)
    - assignments → inline placeholder
    - inspections → [EquipmentInspections](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentInspections.tsx:21:0-541:2)
    - maintenance → [EquipmentMaintenance](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentMaintenance.tsx:21:0-728:2)
    - analytics → [EquipmentAnalytics](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentAnalytics.tsx:17:0-428:2)
  - Tab state synced through URL hash (`#general-equipment`, `#maintenance`, etc.)

## Tabs and components

- [src/components/equipment/EquipmentDashboard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentDashboard.tsx:0:0-0:0)
  - Dashboard metrics for the site; uses mocked local state
  - Navigates to other tabs via `onNavigateToTab`
  - Icons and visual cards via internal shared UI (`StatCard`, [QuickActionCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:78:0-90:1))

- [src/components/equipment/GeneralEquipment.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/GeneralEquipment.tsx:0:0-0:0)
  - Displays site inventory at the [SiteEquipmentInventory](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:145:0-165:1) level (site-specific view)
  - Internal mock data built in the component (useEffect builds placeholder equipment objects)
  - Filtering via `UniversalSearchFilter`
  - Action to “View in Company Equipment” opens `/company-equipment?search=<name>`
  - “Assign” button opens a local placeholder modal

- [src/components/equipment/SiteEquipmentRequest.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/SiteEquipmentRequest.tsx:0:0-0:0)
  - Allows a site to request company equipment
  - Data source: `mockCompanyEquipment` with filters (`equipmentCategories`, `ownershipTypes`)
  - Internal state holds equipment requests; submit logs and stores in memory

- [src/components/equipment/EquipmentInspections.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentInspections.tsx:0:0-0:0)
  - Shows site inspections list; uses local mock inspection data
  - Includes a detail modal
  - Filtering and search are local state

- [src/components/equipment/EquipmentMaintenance.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentMaintenance.tsx:0:0-0:0)
  - Shows maintenance work orders; large component with local mock work orders and a detail modal
  - Filtering/search are local state

- [src/components/equipment/EquipmentAnalytics.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentAnalytics.tsx:0:0-0:0)
  - Analytics tab with utilization trends and cost metrics
  - Mocked data and timers to simulate loading
  - Simple inline chart rendering via divs (no chart lib)

# Data models and mock data

- [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0)
  - Defines [CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1) shape (org-level) and supplies `mockCompanyEquipment` list
  - Option lists: `equipmentCategories`, `equipmentStatuses`, `complianceStatuses`, `ownershipTypes`, `equipmentSites`
  - [getEquipmentKPIs()](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:311:0-348:2) for the dashboard KPIs

- [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0)
  - Rich set of TypeScript types for:
    - Company-level equipment ([CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1) + related info)
    - Site-level models ([EquipmentMaster](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:113:0-134:1), [SiteEquipmentInventory](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:145:0-165:1), [MaintenanceWorkOrder](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:228:0-246:1), [EquipmentInspection](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:257:0-271:1), etc.)
    - UI/filters ([EquipmentFilters](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:298:0-305:1), dashboard types)
  - Note: There is a “CompanyEquipment” here as well as in mock data. The page components under “company” use the mock-data version ([src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0)) in practice. The type overlap is consistent but you might later want to unify usage to one interface.

# Shared UI, layout, and context

- [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) ([src/components/layout/FloatingCard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:0:0-0:0))
  - Wraps pages with TopBar, uses `useTopBarContext`, `useLayoutContext`
  - Pages do not set `title` in props; TopBar likely has defaults or dynamic behavior elsewhere

- `TabContainer` (`src/components/data/shared/TabContainer`), `StatCard`, [QuickActionCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:78:0-90:1), `UniversalSearchFilter`
  - Provide the tabbed experience, KPI cards, action cards, and search/filter header
  - UniversalSearchFilter powers many of the filter/search patterns across the equipment modules

- [AppLayout](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:94:0-106:2) inside [App.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:0:0-0:0)
  - Shows `Sidebar` when `useLayoutContext` indicates so
  - Each protected route is wrapped by `ProtectedRoute`, `LayoutProvider`, `TopBarProvider`, `TenantProvider`, `AuthProvider`

# Component Trees

## Company Equipment

- [App.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:0:0-0:0) → `ProtectedRoute` → [AppLayout](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:94:0-106:2) → [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) → [CompanyEquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:21:0-146:1)
  - Header text guide
  - `TabContainer` with tabs:
    - [CompanyEquipmentDashboard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:92:0-229:1) → uses [getEquipmentKPIs(mockCompanyEquipment)](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:311:0-348:2)
    - [CompanyEquipmentList](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentList.tsx:186:0-387:1) → `UniversalSearchFilter` + list/grid of [CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1)
    - [CompanyEquipmentAdd](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAdd.tsx:4:0-290:1) → add form and bulk placeholder
    - Inline “Equipment View” placeholder
    - [CompanyEquipmentAssignments](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAssignments.tsx:2:0-47:1) → placeholder
    - [CompanyEquipmentMaintenance](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentMaintenance.tsx:2:0-52:1) → placeholder
    - Inline “Templates & Compliance” placeholder
    - [CompanyEquipmentReports](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentReports.tsx:2:0-47:1) → placeholder

## Site Equipment

- [App.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:0:0-0:0) → `ProtectedRoute` → [AppLayout](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:94:0-106:2) → [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) → [EquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:21:0-143:2)
  - Breadcrumb to `/company-equipment`
  - `TabContainer` with tabs:
    - [EquipmentDashboard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentDashboard.tsx:23:0-269:2)
    - [GeneralEquipment](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/GeneralEquipment.tsx:25:0-456:2) (site inventory, local mock)
    - [SiteEquipmentRequest](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/SiteEquipmentRequest.tsx:214:0-367:1) (from company pool using `mockCompanyEquipment`)
    - Inline “Assignments” placeholder
    - [EquipmentInspections](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentInspections.tsx:21:0-541:2) (local mock)
    - [EquipmentMaintenance](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentMaintenance.tsx:21:0-728:2) (local mock)
    - [EquipmentAnalytics](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentAnalytics.tsx:17:0-428:2) (local mock + simple chart)

# Data flow summary

- Both pages are entirely front-end and currently powered by mock data and local component state.
- No API services are invoked for equipment yet.
- Company page mostly uses centralized mock data from [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0).
- Site page mixes:
  - Internal, component-local mock arrays (GeneralEquipment, Inspections, Maintenance, Analytics)
  - Company mock data ([SiteEquipmentRequest](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/SiteEquipmentRequest.tsx:214:0-367:1) pulls `mockCompanyEquipment`) to request from the company pool.

# Navigation and URL behavior

- [CompanyEquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:21:0-146:1) reads and writes `?tab=` and `?search=`.
- [EquipmentPage](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:21:0-143:2) uses the URL hash `#tab-id` to switch tabs, and `Link` breadcrumb to `/company-equipment`.
- Buttons in site views provide deep-links to company equipment with `?search=` to support cross-navigation.

# Libraries, styling, assets

- Icons: `lucide-react`
- Styling: Tailwind CSS classes across components
- No external chart library; Analytics tab uses custom DOM bars
- Shared UI components abstract repeated patterns (tabs, search/filters, cards)

# Gaps, risks, and improvement suggestions

- Data source inconsistency:
  - Company-level uses [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0) shared mocks. Site-level tabs (General, Inspections, Maintenance, Analytics) use separate, local mock data. Consider centralizing mocks and adding service hooks to facilitate a smooth backend integration later.

- Type duplication:
  - There is a [CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1) interface in [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0) and also a separate [CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1) type in [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0). They are similar but not identical (e.g., images/documents fields differ). Consider unifying to a single canonical type in [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0) and ensure mock data conforms to it.

- State and URL sync:
  - Company page uses query params; site page uses hash. Consider unifying to one strategy (query params are generally more flexible, e.g., `?tab=...` and `?search=...`).

- Placeholder tabs:
  - [CompanyEquipmentAssignments](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAssignments.tsx:2:0-47:1), [CompanyEquipmentMaintenance](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentMaintenance.tsx:2:0-52:1), and [CompanyEquipmentReports](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentReports.tsx:2:0-47:1) are placeholders. If these are on the short-term roadmap, plan the data models and API contracts early.

- Navigation stubs:
  - [CompanyEquipmentList](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentList.tsx:186:0-387:1) actions (`onView`, `onAssign`, `onEdit`) log to console. If an equipment detail page is expected, add a route such as `/company-equipment/:equipmentId` and wire view/edit/assign accordingly.

- TopBar title:
  - [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) can set `TopBar` title from props, but Equipment pages aren’t passing `title`. It’s fine visually because pages render headers inside the content area; if you want consistent top bar titles, pass `title` props to [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2).

- Testing integration points:
  - Several components simulate loading via `setTimeout`. When real APIs are integrated, replace with service hooks (e.g., `react-query` or custom hooks) and add error, loading, and empty states consistently.

# Quick reference: files powering the pages

- Routing:
  - [src/App.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/App.tsx:0:0-0:0)

- Company equipment:
  - [src/pages/CompanyEquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/CompanyEquipmentPage.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentDashboard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentDashboard.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentList.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentList.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentAdd.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAdd.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentAssignments.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentAssignments.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentMaintenance.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentMaintenance.tsx:0:0-0:0)
  - [src/components/equipment/CompanyEquipmentReports.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/CompanyEquipmentReports.tsx:0:0-0:0)
  - [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0)
  - [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0) (types, not directly imported by company list/dashboard)

- Site equipment:
  - [src/pages/EquipmentPage.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/pages/EquipmentPage.tsx:0:0-0:0)
  - [src/components/equipment/EquipmentDashboard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentDashboard.tsx:0:0-0:0)
  - [src/components/equipment/GeneralEquipment.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/GeneralEquipment.tsx:0:0-0:0)
  - [src/components/equipment/SiteEquipmentRequest.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/SiteEquipmentRequest.tsx:0:0-0:0)
  - [src/components/equipment/EquipmentInspections.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentInspections.tsx:0:0-0:0)
  - [src/components/equipment/EquipmentMaintenance.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentMaintenance.tsx:0:0-0:0)
  - [src/components/equipment/EquipmentAnalytics.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/equipment/EquipmentAnalytics.tsx:0:0-0:0)
  - [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0)
  - [src/data/equipmentMockData.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:0:0-0:0) (for request tab)

- Shared UI and layout:
  - [src/components/layout/FloatingCard.tsx](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:0:0-0:0)
  - `src/components/data/shared/TabContainer` (referenced)
  - `src/components/shared/UniversalSearchFilter` (referenced)
  - `src/components/data/shared/StatCard` (referenced)
  - `src/components/data/shared/QuickActionCard` (referenced)
  - `src/components/layout/Sidebar` (via layout)
  - `src/components/layout/TopBar` (via [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2))

# Recommendations

- Unify types and mock data
  - Adopt [src/types/equipment.ts](cci:7://file:///d:/atomio/workforce-dev/frontendv1/src/types/equipment.ts:0:0-0:0) as the single source of truth.
  - Update `mockCompanyEquipment` to match the canonical [CompanyEquipment](cci:2://file:///d:/atomio/workforce-dev/frontendv1/src/data/equipmentMockData.ts:5:0-47:1) interface.
  - Extract site-level mock generators into `src/data/` so General/Inspections/Maintenance/Analytics all use shared mocks.

- Introduce service hooks
  - Create hooks like `useCompanyEquipment()`, `useSiteEquipment(siteId)`, `useInspections(siteId)`, etc., to hide the implementation details behind a stable interface and make swapping mock → API seamless.

- Standardize tab URL scheme
  - Prefer `?tab=` for both pages. Also persist search state via query when relevant.

- Plan detail and edit routes
  - Add routes for viewing/editing equipment items, e.g., `/company-equipment/:equipmentId` and wire `onView`/`onEdit`.

- Improve TopBar titles
  - Pass `title` to [FloatingCard](cci:1://file:///d:/atomio/workforce-dev/frontendv1/src/components/layout/FloatingCard.tsx:22:0-75:2) for both pages for consistent UX at the top.
