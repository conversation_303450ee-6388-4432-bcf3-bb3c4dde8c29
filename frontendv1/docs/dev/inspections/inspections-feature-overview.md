# Inspections Feature - Comprehensive Documentation

## Overview

The inspections feature is a comprehensive system for managing equipment and safety inspections within construction sites. It provides functionality for creating, conducting, tracking, and reporting on various types of inspections including equipment checks, safety audits, and compliance verifications.

## Architecture Overview

The inspections system follows a modular architecture with clear separation of concerns:

```
inspections/
├── pages/
│   ├── InspectionsPage.tsx        # Main inspections hub with tabbed interface
│   └── InspectionFormPage.tsx     # Individual inspection form interface
├── components/
│   ├── InspectionsDashboard.tsx   # Overview dashboard with KPIs
│   ├── ScheduledInspections.tsx   # Pending/scheduled inspections
│   ├── InspectionHistory.tsx      # Completed inspections history
│   ├── InspectionForms.tsx        # Available form templates
│   ├── InspectionReports.tsx      # Analytics and reporting
│   └── shared/                    # Shared inspection components
├── data/
│   └── inspectionFormTemplate.ts  # Form templates and type definitions
├── graphql/
│   ├── queries.ts                 # Inspection-related queries
│   └── mutations.ts               # Inspection-related mutations
└── types/                         # TypeScript type definitions
```

## Core Components

### 1. InspectionsPage.tsx

**Purpose**: Main hub for all inspection-related activities with a tabbed interface

**Key Features**:
- Tab-based navigation (Dashboard, Pending, History, Forms, Reports)
- URL hash navigation for deep linking
- Site-specific context management
- Breadcrumb navigation

**Props**:
```typescript
interface InspectionsPageProps {
  siteId: string;
}
```

**Navigation Structure**:
- `#dashboard` - Overview metrics and quick actions
- `#pending` - Scheduled/pending inspections
- `#history` - Completed inspections
- `#forms` - Available inspection templates
- `#reports` - Analytics and reporting

### 2. InspectionFormPage.tsx

**Purpose**: Interactive form interface for conducting inspections

**Key Features**:
- Dynamic form rendering based on template ID
- Multi-section form with validation
- Photo capture integration
- Real-time form state management
- GraphQL mutation integration

**Form Structure**:
```typescript
interface InspectionFormData {
  approved: {
    value: boolean;
    remarks: string;
  };
  generalComments: string;
  inspectedBy: string;
  date: string;
  time: string;
  signature: string;
  responses: { 
    [key: number]: { 
      response: string; 
      remarks: string; 
      photos: File[] 
    } 
  };
}
```

**Key Functionality**:
- Auto-populates date and time
- Validates required fields
- Handles photo uploads per inspection item
- Submits to GraphQL backend with file handling

### 3. InspectionsDashboard.tsx

**Purpose**: Overview dashboard with KPIs and quick actions

**Key Metrics**:
- Total scheduled inspections
- Overdue inspections
- Completed inspections
- In-progress inspections
- Compliance rate percentage

**Quick Actions**:
- Start new inspection
- View overdue inspections
- Access recent inspections
- Navigate to different sections

### 4. ScheduledInspections.tsx

**Purpose**: Management of pending and scheduled inspections

**Features**:
- List view of upcoming inspections
- Filtering by category and search
- Assignment management
- Due date tracking
- Estimated duration display

### 5. InspectionHistory.tsx

**Purpose**: Historical view of completed inspections

**Features**:
- Paginated list of completed inspections
- Status filtering (passed/failed/in-progress)
- Search and category filters
- Performance metrics per inspection
- Export capabilities

### 6. InspectionForms.tsx

**Purpose**: Catalog of available inspection templates

**Features**:
- Grid view of available forms
- Category-based organization
- Search functionality
- Item count display
- Direct navigation to form

**Categories**:
- Tools
- Machinery
- Concrete
- Safety
- Vehicles
- Equipment

## Data Models

### Form Templates

Located in `src/data/inspectionFormTemplate.ts`

```typescript
type FormStructureType = {
  serialNo: number;
  description: string;
  response: string;
  remarks: string;
}

type FormRepresentationType = {
  id: string;
  name: string;
  information: FormStructureType[];
}
```

### Inspection Type Mapping

Maps form template IDs to GraphQL enum values:

```typescript
export const inspectionTypeMap: { [key: string]: string } = {
  '1': 'AIR_COMPRESSOR_MACHINE_INSPECTION',
  '2': 'ANGLE_GRINDING_MACHINE_INSPECTION',
  '3': 'BACKHOE_LOADER_INSPECTION',
  // ... 21 total inspection types
};
```

### Available Inspection Templates

1. **AIR_COMPRESSOR_MACHINE** - 11 inspection points
2. **ANGLE_GRINDING_MACHINE** - Equipment safety checks
3. **BACKHOE_LOADER** - Heavy machinery inspection
4. **BAR_BENDING_MACHINE** - Construction tool checks
5. **BENCH_CUTTING_MACHINE** - Tool safety inspection
6. **CIRCULAR_SAW_MACHINE** - Power tool checks
7. **CONCRETE_BOOM_PLACER** - Concrete equipment
8. **CONCRETE_MIXER** - Mixer safety and functionality
9. **CONCRETE_PUMP** - Pumping equipment checks
10. **DIESEL_GENERATOR** - Power generation equipment
11. **DRILLING_MACHINE** - Drilling equipment safety
12. **DRUM_ROLLER** - Road construction equipment
13. **EXCAVATOR** - Heavy machinery comprehensive check
14. **FIRE_EXTINGUISHERS** - Safety equipment inspection
15. **GRADER** - Road grading equipment
16. **JACK_HAMMER** - Pneumatic tool checks
17. **POCKER_VIBRATOR** - Concrete vibrator inspection
18. **READY_MIX** - Ready-mix concrete delivery
19. **STEEL_REBAR_CUTTING_MACHINE** - Steel processing
20. **TIPPER_TRUCK** - Vehicle safety inspection
21. **WELDING_MACHINE** - Welding equipment safety

## GraphQL Integration

### Queries

Located in `src/graphql/queries.ts`

#### GET_INSPECTIONS
```graphql
query GetInspections($skip: Int = 0, $take: Int = 50) {
  inspections(skip: $skip, take: $take) {
    id
    approved
    comments
    inspectionType
    inspectedById
    inspectedBy {
      id
      name
    }
    signatureFileId
    signatureFile {
      id
      fileName
      url
    }
    inspectionItems {
      id
      description
      isTrue
      remarks
      imageFiles {
        id
        fileName
        url
        contentType
      }
    }
    createdAt
    createdBy
    updatedAt
    updatedBy
  }
}
```

#### GET_INSPECTION_BY_ID
Retrieves a specific inspection with full details including file information.

#### GET_INSPECTIONS_BY_INSPECTOR
Filters inspections by inspector ID for personal inspection tracking.

### Mutations

Located in `src/graphql/mutations.ts`

#### CREATE_INSPECTION
```graphql
mutation CreateInspection($input: CreateInspectionInput!) {
  createInspection(input: $input) {
    id
    approved
    comments
    inspectionType
    inspectedById
    inspectedBy {
      id
      name
    }
    signatureFileId
    signatureFile {
      id
      fileName
      url
    }
    inspectionItems {
      id
      description
      isTrue
      remarks
      imageFiles {
        id
        fileName
        url
        contentType
      }
    }
    createdAt
    createdBy
    updatedAt
    updatedBy
  }
}
```

### Input Types

```typescript
interface CreateInspectionInput {
  inspectionItems: InspectionItemInput[];
  approved: boolean;
  comments: string;
  inspectedById: number;
  inspectionType: string;
}

interface InspectionItemInput {
  description: string;
  isTrue: boolean;
  remarks: string;
  imageFiles: File[];
}
```

## Supporting Components

### PhotoCapture Component

**Location**: `src/components/shared/PhotoCapture.tsx`

**Purpose**: Handles photo capture and file upload for inspection items

**Features**:
- Camera access for live photo capture
- File upload from device
- Image preview and management
- File size and type validation
- Multiple photo support per item

**Props**:
```typescript
interface PhotoCaptureProps {
  onPhotosChange: (files: File[]) => void;
  maxPhotos?: number;        // Default: 5
  maxFileSize?: number;      // Default: 10MB
  disabled?: boolean;
  className?: string;
}
```

### FloatingCard Component

**Purpose**: Provides consistent layout wrapper for inspection pages

**Features**:
- Breadcrumb navigation
- Action buttons in top bar
- Responsive design
- Consistent spacing and styling

### UniversalFilter Component

**Purpose**: Provides search and filtering capabilities

**Features**:
- Text search
- Tag-based category filtering
- Date range filtering
- Status filtering

## User Interface Flow

### Starting an Inspection

1. **Navigation**: User navigates to `/sites/{siteId}/inspections`
2. **Form Selection**: 
   - From Dashboard → Quick Actions → "Start Inspection"
   - From Forms tab → Select specific form template
3. **Form Filling**: Navigate to `/sites/{siteId}/inspections/form/{formId}`
4. **Data Entry**:
   - Fill inspector details
   - Answer each inspection point (YES/NO)
   - Add remarks for failed items
   - Capture photos as evidence
   - Provide overall approval decision
5. **Submission**: Submit form to backend via GraphQL mutation

### Viewing Inspection History

1. **Access**: Navigate to History tab in inspections page
2. **Filtering**: Use search and filters to find specific inspections
3. **Details**: Click on inspection to view full details
4. **Export**: Generate reports from inspection data

### Managing Scheduled Inspections

1. **View Pending**: Access Pending tab to see upcoming inspections
2. **Assignment**: Assign inspections to team members
3. **Tracking**: Monitor due dates and overdue items
4. **Execution**: Start inspections directly from schedule

## State Management

### Form State

The `InspectionFormPage` maintains complex form state:

```typescript
// Individual item responses
responses: { 
  [serialNo: number]: { 
    response: 'YES' | 'NO' | '';
    remarks: string;
    photos: File[];
  }
}

// Overall form data
formData: {
  approved: { value: boolean; remarks: string };
  generalComments: string;
  inspectedBy: string;
  date: string;          // Auto-generated
  time: string;          // Auto-generated
  signature: string;
}
```

### Navigation State

The main inspections page uses URL hash-based navigation:

```typescript
const [activeTab, setActiveTab] = useState("dashboard");

// Handles browser navigation and deep linking
useEffect(() => {
  const hash = location.hash.replace("#", "");
  if (hash && validTabs.includes(hash)) {
    setActiveTab(hash);
  }
}, [location.hash]);
```

## File Upload Handling

### Photo Management

Each inspection item can have multiple photos:

1. **Capture**: Use device camera or file upload
2. **Validation**: Check file type and size limits
3. **Preview**: Display thumbnails with management options
4. **Submission**: Include in GraphQL mutation as file uploads

### File Constraints

- **Maximum photos per item**: 3-5 (configurable)
- **File size limit**: 5-10MB per photo
- **Supported formats**: JPEG, PNG, WebP
- **Storage**: Backend file service integration

## Error Handling

### Form Validation

- **Required fields**: Inspector name is mandatory
- **Response completion**: All inspection items should be answered
- **Photo validation**: File size and type checking
- **Network errors**: Graceful handling of submission failures

### User Feedback

- **Success**: Toast notification on successful submission
- **Errors**: Specific error messages for validation failures
- **Loading states**: Visual indicators during submission
- **Offline support**: Local storage for form drafts

## Performance Considerations

### Data Loading

- **Lazy loading**: Components load data on demand
- **Pagination**: History and lists use pagination
- **Caching**: Apollo Client caching for repeated queries
- **Optimistic updates**: Immediate UI feedback

### File Handling

- **Compression**: Image compression before upload
- **Chunked uploads**: Large file handling
- **Progress indicators**: Upload progress feedback
- **Retry logic**: Failed upload recovery

## Security Considerations

### Data Validation

- **Input sanitization**: All form inputs are validated
- **File type checking**: Strict image file validation
- **Size limits**: Prevent large file uploads
- **User permissions**: Role-based access control

### Authentication

- **Session management**: Maintain user context
- **Permission checks**: Verify inspection permissions
- **Audit trails**: Track all inspection activities
- **Data encryption**: Secure data transmission

## Testing Strategy

### Unit Tests

- Form validation logic
- State management functions
- Data transformation utilities
- Component rendering

### Integration Tests

- GraphQL operation flows
- File upload workflows
- Navigation between components
- Form submission processes

### E2E Tests

- Complete inspection workflows
- Multi-user scenarios
- Photo capture functionality
- Report generation

## Future Enhancements

### Planned Features

1. **Offline Support**: Complete inspections without internet
2. **AI Integration**: Automated issue detection from photos
3. **Mobile App**: Native mobile inspection app
4. **Advanced Analytics**: ML-powered inspection insights
5. **Voice Notes**: Audio annotations for inspection items
6. **QR Codes**: Equipment identification via QR scanning
7. **Real-time Collaboration**: Multiple inspectors on same form
8. **Custom Forms**: User-defined inspection templates

### Technical Improvements

- **Performance**: Further optimize for large datasets
- **PWA**: Progressive Web App capabilities
- **Accessibility**: Enhanced WCAG compliance
- **Internationalization**: Multi-language support
- **Advanced Caching**: Service worker implementation

## API Dependencies

### External Services

- **GraphQL Backend**: Core data operations
- **File Storage**: Image and document storage
- **Authentication**: User management service
- **Notifications**: Real-time updates

### Backend Schema

The frontend expects specific GraphQL schema definitions:

```graphql
enum InspectionType {
  AIR_COMPRESSOR_MACHINE_INSPECTION
  ANGLE_GRINDING_MACHINE_INSPECTION
  # ... other types
}

type Inspection {
  id: Int!
  approved: Boolean!
  comments: String
  inspectionType: InspectionType!
  inspectedById: Int!
  inspectedBy: User!
  signatureFileId: Int
  signatureFile: File
  inspectionItems: [InspectionItem!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type InspectionItem {
  id: Int!
  description: String!
  isTrue: Boolean!
  remarks: String
  imageFiles: [File!]!
}
```

## Deployment Notes

### Environment Configuration

- **API Endpoints**: Configure GraphQL endpoint
- **File Upload**: Set up file storage service
- **Camera Permissions**: Handle browser permission requests
- **Error Monitoring**: Integrate error tracking

### Browser Support

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+
- **Mobile**: iOS Safari 13+, Chrome Mobile 80+
- **Camera API**: Requires HTTPS in production
- **File API**: Modern file handling support

This documentation provides a comprehensive overview of the inspections feature, covering all aspects from architecture to implementation details. The system is designed to be scalable, maintainable, and user-friendly while providing robust inspection management capabilities.