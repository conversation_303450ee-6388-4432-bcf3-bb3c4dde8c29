### 1. Time management related Database Schema Design

This schema is designed to directly map to the fields provided in your API examples.

#### `Sites` & `Terminals`
*(These tables remain as previously designed, they are solid.)*

#### `Workers`
This table should store the "source of truth" for your system.

```csharp
// EF Core Entity Representation
public class Worker
{
    public Guid WorkerId { get; set; } // Your internal PK
    public int SiteId { get; set; }     // FK to Sites table

    [MaxLength(32)]
    public string HikEmployeeNo { get; set; } // "employeeNo" - CRITICAL for API calls
    
    public string Name { get; set; }
    public string Gender { get; set; } // "male", "female", etc.
    public string UserType { get; set; } // "normal", "admin"
    
    // For the "Valid" object
    public bool IsValidityPeriodEnabled { get; set; }
    public DateTime ValidBeginTime { get; set; }
    public DateTime ValidEndTime { get; set; }
    
    // Your internal reference to the worker's photo
    public string FaceImageUrl { get; set; }
    
    // To manage access permissions
    public int PermissionTemplateId { get; set; } // FK to your PermissionTemplates
}
```

#### `RawEvents`
This table is your ingestion layer for both push and pull events. It's designed for durability and reconciliation.

```csharp
// EF Core Entity Representation
public class RawEvent
{
    public long EventId { get; set; } // Your internal PK
    public Guid TerminalId { get; set; } // FK to Terminals table
    
    // Key fields for reconciliation and processing
    public long HikSerialNo { get; set; } // "serialNo" from the payload
    public string EmployeeNo { get; set; } // "employeeNoString"
    public DateTimeOffset EventTimestamp { get; set; } // "dateTime"
    
    // Event details
    public string EventType { get; set; } // e.g., "AccessControllerEvent"
    public string VerifyMode { get; set; } // e.g., "face"
    public string AttendanceStatus { get; set; } // e.g., "checkIn"
    public string MaskStatus { get; set; } // e.g., "unMask"
    public string PictureUrl { get; set; } // URL provided in the event
    
    // Hybrid model tracking fields
    public string Source { get; set; } // "PUSH" or "PULL"
    public bool IsReconciled { get; set; }
    public bool IsProcessed { get; set; }
    
    // Store the original payload for auditing
    public string RawPayload { get; set; } // Store the original XML or JSON
}
```
**Database Index:** Create a **unique index** on `(TerminalId, HikSerialNo)` in the `RawEvents` table. This is the cornerstone of your deduplication and reconciliation logic. An event from a specific terminal with a specific serial number should only exist once. The `Source` field helps you track how it arrived.

#### `AttendanceRecords`
This is your final, clean data table for payroll and reporting.

```csharp
// EF Core Entity Representation
public class AttendanceRecord
{
    public long AttendanceRecordId { get; set; }
    public Guid WorkerId { get; set; }
    public int SiteId { get; set; }
    
    public DateTimeOffset CheckInTimestamp { get; set; }
    public DateTimeOffset? CheckOutTimestamp { get; set; } // Nullable
    
    // Identifiers from the source events
    public long CheckInRawEventId { get; set; }
    public long? CheckOutRawEventId { get; set; }
}
```

---

### 2. Backend Services / Components (C#/.NET)

Here is a logical separation of your backend services, designed to run as scalable .NET Worker Services.

#### 1. Web API (`MyProject.Api`)
*   **Responsibilities**:
    *   **User/Site/Terminal Management Endpoints**: Standard CRUD operations.
    *   **HTTP Listening Endpoint**: `POST /api/hikvision/events`
        *   This endpoint must be fast. It receives the **XML `EventNotificationAlert`**.
        *   It should immediately parse the XML into a canonical model, and publish it to a message queue (e.g., RabbitMQ, Azure Service Bus) on a topic named `events.pushed`.
        *   It must return `200 OK` quickly.

#### 2. Device Sync Service (`MyProject.SyncService`)
*   **Responsibilities**:
    *   Triggered by messages from the API (e.g., `worker.updated`, `site.assignment.changed`).
    *   Constructs the **JSON `UserInfo` object** based on your `Workers` table.
    *   Calls `POST /ISAPI/AccessControl/UserInfo/Record` to create the worker.
    *   Constructs a **`multipart/form-data` request** to `POST /ISAPI/Intelligent/FDLib/FaceDataRecord` to upload the face image.
    *   Handles batching (30 users per request for info, 1 face at a time) and adds small delays between API calls to avoid overwhelming the terminal.

#### 3. Event Polling Service (`MyProject.EventPoller`)
*   **Technology**: .NET Worker Service using a `PeriodicTimer`.
*   **Responsibilities**:
    *   Runs hourly.
    *   Fetches all online terminals.
    *   For each terminal, it calls `POST /ISAPI/AccessControl/AcsEvent/Search` using the last known `serialNo` or `timestamp` to get only new events.
    *   It receives the **JSON `AcsEvent` response**.
    *   For each event in the `InfoList`, it publishes it to a message queue on a topic named `events.pulled`.

#### 4. Event Processing Service (`MyProject.EventProcessor`)
*   **Technology**: .NET Worker Service.
*   **Responsibilities**:
    *   Subscribes to both `events.pushed` and `events.pulled` topics.
    *   **Canonical Model**: It first maps the incoming XML or JSON into a single, consistent C# class (`CanonicalEvent`).
    *   It attempts to insert this `CanonicalEvent` into the `RawEvents` table.
        *   If the `(TerminalId, HikSerialNo)` unique key already exists, it means the event has already been received via the other channel. This is expected and can be logged at a DEBUG level.
        *   If it's a new event, it's inserted.
    *   After inserting, it performs the business logic to create/update records in the `AttendanceRecords` table.

#### 5. Reconciliation Service (`MyProject.ReconciliationService`)
*   **Technology**: .NET Worker Service (Scheduled).
*   **Responsibilities**:
    *   Runs on a schedule (e.g., every hour, after the `EventPoller` has finished).
    *   Queries `RawEvents` for records where `IsReconciled = false`.
    *   It looks for any `HikSerialNo` that was received via PULL but not via PUSH. This indicates a potential network issue with the push mechanism and should trigger an alert for your team.
    *   Marks the verified events as `IsReconciled = true`. This provides a clear audit trail.

---

### 3. System Modeling Guidance

#### 1. Person Fields to Store:
Based on the `UserInfo` payload, you should store:
*   `HikEmployeeNo` (string, max 32)
*   `Name` (string)
*   `UserType` (string: "normal" or "admin")
*   `Gender` (string: "male", "female", or "unknown")
*   `ValidBeginTime` & `ValidEndTime` (datetime)
*   Access Rights: `doorRight` and `RightPlan` should be stored in your permissions tables and linked to the worker.

#### 2. Event Log Fields:
Your `RawEvents` table should capture these key fields from both push/pull payloads:
*   `HikSerialNo` (from `serialNo`)
*   `EventTimestamp` (from `dateTime` or `time`)
*   `EmployeeNo` (from `employeeNoString`)
*   `WorkerName` (from `name`)
*   `VerifyMode` (from `currentVerifyMode`)
*   `AttendanceStatus` (from `attendanceStatus`)
*   `PictureUrl` (from `pictureURL`)
*   `MaskStatus` (from `mask`)

#### 3. Services Separation:
The logical separation into **5 services** (API, Sync, Poller, Processor, Monitor/Reconciler) is an excellent, scalable design. It isolates responsibilities, making the system easier to build, test, and maintain.

#### 4. Example Canonical Event Payload (C# Model):
This is the internal model your `EventProcessor` would work with, populated from either the XML push or JSON pull.

```csharp
public class CanonicalEvent
{
    public string DeviceUUID { get; set; }
    public long SerialNumber { get; set; }
    public string EmployeeNo { get; set; }
    public string EmployeeName { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    public string VerificationMode { get; set; } // "face", "card", etc.
    public string AttendanceStatus { get; set; } // "checkIn", "checkOut", etc.
    public string MaskStatus { get; set; } // "mask", "unMask", "unknown"
    public string PictureUrl { get; set; }
    public string RawPayload { get; set; }
    public string Source { get; set; } // "PUSH" or "PULL"
}
```

User Data Structure

{
  "UserInfo": {
    "employeeNo": "EMP001",           // Primary identifier (use as username)
    "name": "John Doe",               // Display name
    "userType": "normal",             // normal | admin
    "Valid": {
      "enable": true,
      "beginTime": "2000-01-01T00:00:00",
      "endTime": "2037-12-31T23:59:59",
      "timeType": "local"
    },
    "doorRight": "1",                 // Door access permissions
    "RightPlan": [{
      "doorNo": 1,
      "planTemplateNo": "1"
    }],
    "gender": "male",                 // male | female | unknown
    "numOfCard": 0,                   // Number of cards assigned
    "numOfFP": 0,                     // Number of fingerprints
    "numOfFace": 0                    // Number of face templates
  }
}


Search Users - Expected Response

{
  "UserInfoSearch": {
    "searchID": "1",
    "responseStatusStrg": "OK",
    "numOfMatches": 2,
    "totalMatches": 2,
    "UserInfo": [
      {
        "employeeNo": "EMP001",
        "name": "John Doe",
        "userType": "normal",
        "Valid": {
          "enable": true,
          "beginTime": "2000-01-01T00:00:00",
          "endTime": "2037-12-31T23:59:59",
          "timeType": "local"
        },
        "doorRight": "1",
        "gender": "male",
        "numOfCard": 0,
        "numOfFP": 1,
        "numOfFace": 1
      }
    ]
  }
}


Hikvision HTTP Push Notification

<?xml version="1.0" encoding="UTF-8"?>
<EventNotificationAlert version="2.0" xmlns="http://www.isapi.org/ver20/XMLSchema">
  <ipAddress>*************</ipAddress>
  <portNo>80</portNo>
  <protocol>HTTP</protocol>
  <macAddress>AC:B9:2F:F7:2D:B3</macAddress>
  <channelID>1</channelID>
  <dateTime>2025-06-23T14:30:15+03:00</dateTime>
  <activePostCount>1</activePostCount>
  <eventType>AccessControllerEvent</eventType>
  <eventState>active</eventState>
  <eventDescription>Access Controller Event</eventDescription>
  <AccessControllerEvent>
    <employeeNoString>EMP001</employeeNoString>
    <name>John Doe</name>
    <cardNo>0</cardNo>
    <cardType>normalCard</cardType>
    <whiteListNo>1</whiteListNo>
    <reportChannel>1</reportChannel>
    <cardReaderKind>accessController</cardReaderKind>
    <cardReaderNo>1</cardReaderNo>
    <doorNo>1</doorNo>
    <verifyNo>1</verifyNo>
    <alarmInNo>0</alarmInNo>
    <alarmOutNo>0</alarmOutNo>
    <caseSensitive>false</caseSensitive>
    <serialNo>12345</serialNo>
    <userType>normal</userType>
    <currentVerifyMode>face</currentVerifyMode>
    <attendanceStatus>checkIn</attendanceStatus>
    <mask>unMask</mask>
    <pictureURL>/ISAPI/AccessControl/CaptureFaceData/picture?employeeNo=EMP001</pictureURL>
  </AccessControllerEvent>
</EventNotificationAlert>


Event Polling - Expected Response

{
  "AcsEvent": {
    "searchID": "1",
    "responseStatusStrg": "OK",
    "numOfMatches": 5,
    "totalMatches": 100,
    "InfoList": [
      {
        "employeeNoString": "EMP001",
        "name": "John Doe",
        "time": "2025-06-23T14:30:15+03:00",
        "cardNo": "0",
        "doorNo": 1,
        "eventType": "accessControllerEvent",
        "currentVerifyMode": "face",
        "attendanceStatus": "checkIn",
        "serialNo": 12345,
        "pictureURL": "/ISAPI/AccessControl/CaptureFaceData/picture?employeeNo=EMP001"
      }
    ]
  }
}