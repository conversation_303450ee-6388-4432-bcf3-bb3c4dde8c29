# Worker Management System Design

## System Overview

The worker management system needs to handle:
1. **Company-level worker database** with multi-site assignment capability
2. **Trade-based training compliance** tracking
3. **Site assignment eligibility** verification
4. **Daily attendance** tracking
5. **Training certificate management** and renewal workflows

## Core Entity Relationships

### 1. Company → Workers → Sites Hierarchy

```
Company
├── Workers (Master Database)
│   ├── Personal Info
│   ├── Trade Classifications
│   ├── Training Records
│   ├── Medical Records
│   └── Site Assignments
├── Sites/Projects
│   ├── Site-Specific Requirements
│   ├── Assigned Workers
│   └── Daily Attendance Records
└── Training Management
    ├── Training Modules
    ├── Certification Records
    └── Renewal Schedules
```

## Database Schema Design

### Core Tables

#### 1. Workers Table
```sql
workers {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  employee_number: String (Unique)
  first_name: String
  last_name: String
  email: String
  phone: String
  hire_date: Date
  status: Enum (active, inactive, terminated)
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 2. Worker Trades Table (Many-to-Many)
```sql
worker_trades {
  id: UUID (Primary Key)
  worker_id: UUID (Foreign Key)
  trade_id: UUID (Foreign Key)
  is_primary: Boolean
  certification_level: Enum (beginner, intermediate, advanced)
  assigned_date: Date
  created_at: Timestamp
}
```

#### 3. Trades Table
```sql
trades {
  id: UUID (Primary Key)
  name: String (e.g., "Carpenter", "Electrician")
  code: String (e.g., "CARP", "ELEC")
  category: Enum (skilled, operations, management, safety, support)
  description: Text
  is_active: Boolean
}
```

#### 4. Training Modules Table
```sql
training_modules {
  id: UUID (Primary Key)
  name: String
  code: String
  category: Enum (critical, operational, foundation, administrative)
  validity_period_months: Integer (null for no expiry)
  requires_full_recertification: Boolean
  description: Text
  is_active: Boolean
}
```

#### 5. Trade Training Requirements Table
```sql
trade_training_requirements {
  id: UUID (Primary Key)
  trade_id: UUID (Foreign Key)
  training_module_id: UUID (Foreign Key)
  is_mandatory: Boolean
  created_at: Timestamp
}
```

#### 6. Worker Training Records Table
```sql
worker_training_records {
  id: UUID (Primary Key)
  worker_id: UUID (Foreign Key)
  training_module_id: UUID (Foreign Key)
  certificate_number: String
  completion_date: Date
  expiry_date: Date (calculated or null)
  certificate_file_url: String
  training_provider: String
  status: Enum (valid, expired, pending_renewal)
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 7. Sites/Projects Table
```sql
sites {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  name: String
  code: String
  location: String
  start_date: Date
  end_date: Date
  status: Enum (planning, active, completed, suspended)
  site_manager_id: UUID (Foreign Key to workers)
  created_at: Timestamp
}
```

#### 8. Site Worker Assignments Table
```sql
site_worker_assignments {
  id: UUID (Primary Key)
  site_id: UUID (Foreign Key)
  worker_id: UUID (Foreign Key)
  assigned_date: Date
  end_date: Date (null if ongoing)
  status: Enum (assigned, active, completed, terminated)
  assigned_by: UUID (Foreign Key to workers)
  created_at: Timestamp
}
```

#### 9. Daily Attendance Table
```sql
daily_attendance {
  id: UUID (Primary Key)
  site_id: UUID (Foreign Key)
  worker_id: UUID (Foreign Key)
  hikvision_device_id: String
  attendance_date: Date
  check_in_time: Time
  check_out_time: Time (null if not checked out)
  total_hours: Decimal (calculated)
  status: Enum (present, absent, late, early_departure)
  face_recognition_confidence: Float
  device_event_id: String (Hikvision event reference)
  sync_status: Enum (synced, pending, failed)
  notes: Text
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 10. Medical Records Table
```sql
worker_medical_records {
  id: UUID (Primary Key)
  worker_id: UUID (Foreign Key)
  medical_type: Enum (fitness_certificate, vaccination, health_screening)
  issue_date: Date
  expiry_date: Date
  certificate_file_url: String
  medical_provider: String
  status: Enum (valid, expired, pending_renewal)
  created_at: Timestamp
}
```

## Hikvision Device Integration Tables

#### 11. Hikvision Devices Table
```sql
hikvision_devices {
  id: UUID (Primary Key)
  site_id: UUID (Foreign Key)
  device_id: String (Hikvision device identifier)
  device_name: String
  device_ip: String
  device_model: String
  location_description: String (e.g., "Main Gate", "Building A Entrance")
  status: Enum (active, inactive, maintenance)
  last_sync: Timestamp
  api_endpoint: String
  access_credentials: JSON (encrypted)
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 12. Device User Sync Table
```sql
device_user_sync {
  id: UUID (Primary Key)
  hikvision_device_id: UUID (Foreign Key)
  worker_id: UUID (Foreign Key)
  device_user_id: String (ID assigned by Hikvision device)
  face_template_uploaded: Boolean
  sync_status: Enum (pending, synced, failed, removed)
  last_sync_attempt: Timestamp
  sync_error_message: Text
  access_level: Integer (Hikvision access level)
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 13. Device Events Log Table
```sql
device_events_log {
  id: UUID (Primary Key)
  hikvision_device_id: UUID (Foreign Key)
  worker_id: UUID (Foreign Key, nullable)
  event_type: Enum (access_granted, access_denied, face_recognized, unknown_face)
  event_timestamp: Timestamp
  device_event_id: String
  face_recognition_confidence: Float
  raw_event_data: JSON
  processed: Boolean
  created_at: Timestamp
}
```

## Business Logic & Workflows

### 1. Worker Onboarding Process

```
1. Add Worker to Company Database
   ├── Personal Information
   ├── Assign Trade(s)
   └── Generate Employee Number

2. Training Compliance Check
   ├── Identify Required Trainings (based on trade)
   ├── Upload Existing Certificates
   └── Schedule Missing Trainings

3. Medical Compliance Check
   ├── Upload Medical Certificates
   └── Verify Validity Periods

4. Site Assignment Eligibility
   ├── Verify All Required Trainings Valid
   ├── Verify Medical Clearance
   └── Approve for Site Assignment
```

### 2. Site Assignment Workflow

```
1. Site Assignment Request
   ├── Select Site/Project
   ├── Choose Workers
   └── Check Eligibility

2. Compliance Verification
   ├── Training Status Check
   ├── Medical Status Check
   └── Generate Compliance Report

3. Assignment Approval
   ├── Manager Review
   ├── Final Approval
   └── Worker Notification

4. Site Access Activation
   ├── Generate Site Access Credentials
   ├── Setup Attendance Tracking
   └── Notify Site Management
```

### 3. Training Management Workflow

```
1. Training Event Planning
   ├── Schedule Training Sessions
   ├── Identify Participants
   └── Book Training Provider

2. Training Execution
   ├── Attendance Tracking
   ├── Assessment Results
   └── Certificate Generation

3. Certificate Management
   ├── Upload Certificates
   ├── Update Worker Records
   └── Calculate New Expiry Dates

4. Compliance Update
   ├── Update Site Eligibility
   ├── Generate Compliance Reports
   └── Schedule Renewal Reminders
```

### 4. Daily Attendance Workflow

```
1. Site Check-in
   ├── Worker Identification (ID/Biometric)
   ├── Compliance Status Verification
   └── Record Check-in Time

2. Work Period Tracking
   ├── Monitor Site Presence
   ├── Track Break Times (optional)
   └── Record Work Activities

3. Site Check-out
   ├── Record Check-out Time
   ├── Calculate Total Hours
   └── Generate Daily Report

4. Attendance Reporting
   ├── Daily Attendance Summary
   ├── Weekly/Monthly Reports
   └── Payroll Integration
```

## System Features & Capabilities

### 1. Worker Management
- **Master Worker Database** at company level
- **Multi-site assignment** capability
- **Trade classification** and skill tracking
- **Worker profile** with photo and documents
- **Employment history** and performance tracking

### 2. Training Compliance
- **Automated compliance checking** based on trade requirements
- **Certificate expiry tracking** with automated alerts
- **Bulk training updates** after company-wide sessions
- **Training history** and audit trail
- **Renewal scheduling** and reminders

### 3. Site Assignment
- **Eligibility verification** before assignment
- **Site-specific requirements** overlay
- **Assignment approval** workflow
- **Worker availability** tracking
- **Site capacity** management

### 4. Attendance Tracking
- **Real-time check-in/out** system
- **Biometric integration** capability
- **GPS location** verification (optional)
- **Overtime calculation** and alerts
- **Attendance analytics** and reporting

### 5. Reporting & Analytics
- **Compliance dashboards** by site/trade/worker
- **Training renewal** schedules
- **Attendance reports** and analytics
- **Worker utilization** metrics
- **Audit trails** for all activities

## Implementation Considerations

### 1. Data Import/Export
- **Bulk worker import** from existing systems
- **Certificate batch upload** functionality
- **Integration APIs** for external systems
- **Data export** for reporting and backup

### 2. Mobile Application
- **Worker self-service** portal
- **Site supervisor** attendance app
- **Training coordinator** certificate management
- **Offline capability** for remote sites

### 3. Integration Points
- **Payroll systems** for attendance data
- **Training providers** for certificate updates
- **Access control systems** for site entry
- **Document management** for certificate storage

### 4. Security & Compliance
- **Role-based access** control
- **Data encryption** for sensitive information
- **Audit logging** for all changes
- **Backup and recovery** procedures
- **GDPR compliance** for personal data

This system design provides a comprehensive framework for managing workers, their training compliance, site assignments, and daily attendance while maintaining the flexibility to handle complex multi-site operations.