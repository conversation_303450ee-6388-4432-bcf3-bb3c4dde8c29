# Worker Creation UI Documentation

## Overview
The Worker Creation interface follows a **dual-panel layout** optimized for construction site workforce management. The design emphasizes clear progress tracking and intuitive navigation through the worker onboarding process.

## Layout Structure

### Left Panel - Profile & Progress Tracker
**Width**: 320px fixed
**Background**: Clean white with subtle shadow

#### Profile Card (Top Section)
```
┌─────────────────────────────┐
│      WORKER PROFILE         │
│                             │
│    ┌─────────────────┐      │
│    │                 │📷    │
│    │   Profile Pic   │      │
│    │                 │      │
│    └─────────────────┘      │
│                             │
│    John <PERSON> (Centered)      │
│    Electrician              │
│                             │
└─────────────────────────────┘
```

**Profile Image Container:**
- **Size**: 80px diameter circle
- **Border**: 2px solid #e5e7eb (gray-200)
- **Camera Icon**: 24px positioned bottom-right with blue background (#3b82f6)
- **Placeholder**: User icon when no image uploaded
- **Hover State**: Subtle scale animation and cursor pointer

#### Progress Steps (Below Profile)
Vertical step indicator with clean, minimal design:

**Step States:**
- **Pending**: Gray circle with white number (#6b7280)
- **Active**: Blue circle with white number (#3b82f6)
- **Complete**: Green circle with white checkmark (#10b981)

**Step Sequence:**
1. **REGISTER** - Basic worker information and identity verification
2. **DOCUMENTS** - Academic credentials, certifications, ID verification
3. **TRAINING** - Safety certifications, role-specific training records
4. **MEDICAL** - Health clearances, fitness assessments, drug testing
5. **TOOLS** - Equipment assignments, tool certifications, PPE allocation

**Step Layout:**
```
○ 1  REGISTER
│
○ 2  DOCUMENTS  
│
○ 3  TRAINING
│
○ 4  MEDICAL
│
○ 5  TOOLS
```

**Visual Hierarchy:**
- **Step Number**: 32px circle, bold number/icon
- **Step Name**: 14px font, medium weight
- **Connection Line**: 1px vertical line connecting steps
- **Spacing**: 16px between each step

### Right Panel - Main Content Area

#### Top Bar
The current topbar has a topbar provider and the provider has a way for loading buttons based on context and hiding certain items

**Layout:**
```
┌──────────────────────────────────────────────────────────────┐
│                                               [Cancel] [Save]│
└──────────────────────────────────────────────────────────────┘
```

**Button Specifications:**
- **Save**: Primary button
- **Cancel**: Secondary button
- **Dimensions**: 120px width, 36px height
- **Position**: Right-aligned with 16px padding

#### Tab Navigation
**Height**: 48px sticky header below top bar
**Background**: Light gray (#f9fafb)

**Tab Structure:**
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│  Personal   │ Documents   │  Training   │   Medical   │    Tools    │
│   Details   │             │             │             │             │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**Tab States:**
- **Active**: White background, blue bottom border (3px)
- **Inactive**: Transparent background, gray text
- **Hover**: Light blue background (#eff6ff)

#### Content Area
**Padding**: 24px on all sides
**Background**: White
**Max Width**: 800px centered

**Form Input Styling:**
- **Text Inputs**: 
  - Height: 44px
  - Border: 1px solid #d1d5db
  - Border Radius: 6px
  - Focus: Blue border (#3b82f6) with subtle shadow
  - Padding: 12px

- **Dropdowns/Selects**:
  - Same styling as text inputs
  - Chevron down icon on right

- **File Upload Areas**:
  - Dashed border (#9ca3af)
  - 120px height for document areas
  - Upload icon and helper text
  - Drag & drop functionality

#### Bottom Navigation
**Fixed Position**: Bottom of viewport
**Background**: White with top border
**Height**: 72px
**Visibility**: Appears after user scrolls past initial view

```
┌──────────────────────────────────────────────────────────────┐
│              [← Previous]           [Next →]                 │
└──────────────────────────────────────────────────────────────┘
```

**Button Styling:**
- **Previous**: Secondary style, left-aligned
- **Next**: Primary blue style, right-aligned  
- **Width**: 140px each
- **Center spacing**: Auto margins between buttons

## Tab Content Details

### 1. Personal Details Tab
**Purpose**: Basic worker information and identity verification

**Form Sections:**
- **Basic Information**
  - Full Name (required)
  - Phone Number (required)
  - Email Address (optional)
  - Gender (required)

- **Identification**
  - National ID Number (required)
  - ID Document Upload (photo)
  - Date of Birth

- **Employment Details**
  - Company/Contractor
  - Job Role/Position
  - Start Date
  - Employment Type (Full-time/Contract/Temporary)

- **Address Information**
  - Current Address
  - Emergency Contact
  - Emergency Contact Phone

### 2. Documents Tab
**Purpose**: Academic credentials, certifications, and official documents

**Document Categories:**
- **Academic Credentials**
  - High School Diploma
  - Trade School Certificates
  - University Degrees

- **Professional Licenses**
  - Trade Licenses (Electrical, Plumbing, etc.)
  - Professional Certifications
  - Equipment Operation Licenses

- **Government Documents**
  - Work Authorization
  - Social Security Card
  - Driver's License

**Upload Interface:**
- Drag & drop zones for each document type
- PDF/Image format support
- Document expiry date tracking
- Verification status indicators

### 3. Training Tab
**Purpose**: Safety training, OSHA certifications, and skill development

**Training Categories:**
- **Safety Certifications**
  - OSHA 10/30 Hour
  - Fall Protection
  - Confined Space
  - Hazmat Handling

- **Role-Specific Training**
  - Equipment Operation
  - Specialized Procedures
  - Quality Control

- **Compliance Training**
  - Site-Specific Safety
  - Environmental Awareness
  - Security Protocols

**Training Record Interface:**
- Certificate upload
- Training provider information
- Completion and expiry dates
- Renewal tracking system

### 4. Medical Tab
**Purpose**: Health clearances, fitness assessments, and medical documentation

**Medical Requirements:**
- **Health Clearances**
  - Physical Fitness Assessment
  - Vision/Hearing Tests
  - Respiratory Clearance

- **Drug & Alcohol Testing**
  - Pre-employment Screening
  - Random Testing Consent
  - Test Result Upload

- **Vaccinations & Health Records**
  - Required Vaccinations
  - Medical History (if required)
  - Allergy Information

**Medical Record Features:**
- Secure document storage
- Expiry date tracking
- Health status indicators
- Privacy compliance notices

### 5. Tools Tab
**Purpose**: Equipment assignments, tool certifications, and PPE management

**Equipment Categories:**
- **Personal Protective Equipment (PPE)**
  - Hard Hat Assignment
  - Safety Vest
  - Steel-toed Boots
  - Safety Glasses

- **Tools & Equipment**
  - Power Tools Assignment
  - Specialized Equipment
  - Tool Certification Requirements

- **Access Cards & Keys**
  - Site Access Cards
  - Equipment Keys
  - Locker Assignments

**Tool Management Interface:**
- Equipment check-out system
- Maintenance tracking
- Certification requirements
- Return date tracking

## Responsive Design Considerations

### Desktop (1024px+)
- Full dual-panel layout as described
- Left panel: 320px fixed width
- Right panel: Remaining space with max 800px content width

### Tablet (768px - 1023px)
- Left panel collapses to sliding drawer
- Tab navigation becomes horizontal scroll
- Content area uses full width

### Mobile (< 768px)
- Left panel becomes bottom sheet/modal
- Tabs become accordion-style navigation
- Single column form layout
- Bottom navigation becomes full-width buttons

## Interaction Patterns

### Step Progression
1. **Linear Flow**: Users must complete steps in sequence
2. **Save & Continue**: Progress is auto-saved on field blur
3. **Validation Gates**: Cannot proceed without required fields
4. **Visual Feedback**: Real-time validation with error states

### Document Management
1. **Upload States**: Processing → Uploaded → Verified
2. **Error Handling**: Clear messages for file type/size issues
3. **Preview Mode**: Click to view uploaded documents
4. **Replace Function**: Easy document replacement workflow

### Navigation Behavior
1. **Tab Memory**: System remembers last active tab
2. **Unsaved Changes**: Warning when switching tabs with unsaved data
3. **Deep Linking**: Each tab has unique URL for bookmarking
4. **Keyboard Navigation**: Tab key support throughout interface

## Accessibility Features

- **ARIA Labels**: All interactive elements properly labeled
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Compatible with screen reading software  
- **Color Contrast**: WCAG 2.1 AA compliant color ratios
- **Focus Indicators**: Clear focus states for all controls
- **Error Announcements**: Screen reader announces form errors

## Technical Implementation Notes

### State Management
- **Form Data**: Persisted in local storage during session
- **File Uploads**: Chunked upload with progress indicators
- **Auto-save**: Debounced save every 30 seconds
- **Offline Support**: Works offline with sync when reconnected

### Performance Optimization
- **Lazy Loading**: Tab content loaded on demand
- **Image Optimization**: Automatic image compression
- **Caching Strategy**: Aggressive caching of static assets
- **Bundle Splitting**: Separate bundles for each major feature

### Security Considerations
- **File Validation**: Server-side file type and content validation
- **Data Encryption**: All sensitive data encrypted at rest
- **Access Control**: Role-based permissions for data access
- **Audit Trail**: All changes logged with user attribution