# Notification Sounds

This directory contains sound files for different notification priorities:

- `critical-notification.mp3` - For critical priority notifications
- `high-notification.mp3` - For high priority notifications  
- `medium-notification.mp3` - For medium priority notifications
- `low-notification.mp3` - For low priority notifications

## Sound Requirements

- Format: MP3
- Duration: 1-3 seconds
- Volume: Normalized to prevent jarring sounds
- Quality: 44.1kHz, 16-bit minimum

## Usage

These sounds are played automatically by the NotificationProvider component based on notification priority levels. Users can disable sounds in their notification preferences.

## Fallback

If sound files are not available, the system will gracefully handle the error and continue without audio notifications.
